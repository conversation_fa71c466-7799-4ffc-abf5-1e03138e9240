// SPDX-License-Identifier: (GPL-2.0-only OR MIT)
/*
 * Copyright 2024 NXP
 */

#include <dt-bindings/clock/nxp,imx95-clock.h>
#include <dt-bindings/dma/fsl-edma.h>
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/net/nxp-netc.h>
#include <dt-bindings/thermal/thermal.h>

#include "imx95-clock.h"
#include "imx95-pinfunc.h"
#include "imx95-power.h"

/ {
	interrupt-parent = <&gic>;
	#address-cells = <2>;
	#size-cells = <2>;

	aliases {
		gpio0 = &gpio1;
		gpio1 = &gpio2;
		gpio2 = &gpio3;
		gpio3 = &gpio4;
		gpio4 = &gpio5;
		i2c0 = &lpi2c1;
		i2c1 = &lpi2c2;
		i2c2 = &lpi2c3;
		i2c3 = &lpi2c4;
		i2c4 = &lpi2c5;
		i2c5 = &lpi2c6;
		i2c6 = &lpi2c7;
		i2c7 = &lpi2c8;
		mmc0 = &usdhc1;
		mmc1 = &usdhc2;
		mmc2 = &usdhc3;
		serial0 = &lpuart1;
		serial1 = &lpuart2;
		serial2 = &lpuart3;
		serial3 = &lpuart4;
		serial4 = &lpuart5;
		serial5 = &lpuart6;
		serial6 = &lpuart7;
		serial7 = &lpuart8;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		idle-states {
			entry-method = "psci";

			cpu_pd_wait: cpu-pd-wait {
				compatible = "arm,idle-state";
				arm,psci-suspend-param = <0x0010033>;
				local-timer-stop;
				/*
				entry-latency-us = <10000>;
				exit-latency-us = <7000>;
				min-residency-us = <27000>;
				wakeup-latency-us = <15000>;
				*/
				entry-latency-us = <1000>;
				exit-latency-us = <700>;
				min-residency-us = <2700>;
				wakeup-latency-us = <1500>;
				status = "okay";
			};
		};

		A55_0: cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a55";
			reg = <0x0>;
			enable-method = "psci";
			#cooling-cells = <2>;
			cpu-idle-states = <&cpu_pd_wait>;
			power-domains = <&scmi_perf IMX95_PERF_A55>;
			power-domain-names = "perf";
			i-cache-size = <32768>;
			i-cache-line-size = <64>;
			i-cache-sets = <128>;
			d-cache-size = <32768>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			next-level-cache = <&l2_cache_l0>;
		};

		A55_1: cpu@100 {
			device_type = "cpu";
			compatible = "arm,cortex-a55";
			reg = <0x100>;
			enable-method = "psci";
			#cooling-cells = <2>;
			cpu-idle-states = <&cpu_pd_wait>;
			power-domains = <&scmi_perf IMX95_PERF_A55>;
			power-domain-names = "perf";
			i-cache-size = <32768>;
			i-cache-line-size = <64>;
			i-cache-sets = <128>;
			d-cache-size = <32768>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			next-level-cache = <&l2_cache_l1>;
		};

		A55_2: cpu@200 {
			device_type = "cpu";
			compatible = "arm,cortex-a55";
			reg = <0x200>;
			enable-method = "psci";
			#cooling-cells = <2>;
			cpu-idle-states = <&cpu_pd_wait>;
			power-domains = <&scmi_perf IMX95_PERF_A55>;
			power-domain-names = "perf";
			i-cache-size = <32768>;
			i-cache-line-size = <64>;
			i-cache-sets = <128>;
			d-cache-size = <32768>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			next-level-cache = <&l2_cache_l2>;
		};

		A55_3: cpu@300 {
			device_type = "cpu";
			compatible = "arm,cortex-a55";
			reg = <0x300>;
			enable-method = "psci";
			#cooling-cells = <2>;
			cpu-idle-states = <&cpu_pd_wait>;
			power-domains = <&scmi_perf IMX95_PERF_A55>;
			power-domain-names = "perf";
			i-cache-size = <32768>;
			i-cache-line-size = <64>;
			i-cache-sets = <128>;
			d-cache-size = <32768>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			next-level-cache = <&l2_cache_l3>;
		};

		A55_4: cpu@400 {
			device_type = "cpu";
			compatible = "arm,cortex-a55";
			reg = <0x400>;
			power-domains = <&scmi_perf IMX95_PERF_A55>;
			power-domain-names = "perf";
			enable-method = "psci";
			#cooling-cells = <2>;
			cpu-idle-states = <&cpu_pd_wait>;
			i-cache-size = <32768>;
			i-cache-line-size = <64>;
			i-cache-sets = <128>;
			d-cache-size = <32768>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			next-level-cache = <&l2_cache_l4>;
		};

		A55_5: cpu@500 {
			device_type = "cpu";
			compatible = "arm,cortex-a55";
			reg = <0x500>;
			power-domains = <&scmi_perf IMX95_PERF_A55>;
			power-domain-names = "perf";
			enable-method = "psci";
			#cooling-cells = <2>;
			cpu-idle-states = <&cpu_pd_wait>;
			i-cache-size = <32768>;
			i-cache-line-size = <64>;
			i-cache-sets = <128>;
			d-cache-size = <32768>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			next-level-cache = <&l2_cache_l5>;
		};

		l2_cache_l0: l2-cache-l0 {
			compatible = "cache";
			cache-size = <65536>;
			cache-line-size = <64>;
			cache-sets = <256>;
			cache-level = <2>;
			cache-unified;
			next-level-cache = <&l3_cache>;
		};

		l2_cache_l1: l2-cache-l1 {
			compatible = "cache";
			cache-size = <65536>;
			cache-line-size = <64>;
			cache-sets = <256>;
			cache-level = <2>;
			cache-unified;
			next-level-cache = <&l3_cache>;
		};

		l2_cache_l2: l2-cache-l2 {
			compatible = "cache";
			cache-size = <65536>;
			cache-line-size = <64>;
			cache-sets = <256>;
			cache-level = <2>;
			cache-unified;
			next-level-cache = <&l3_cache>;
		};

		l2_cache_l3: l2-cache-l3 {
			compatible = "cache";
			cache-size = <65536>;
			cache-line-size = <64>;
			cache-sets = <256>;
			cache-level = <2>;
			cache-unified;
			next-level-cache = <&l3_cache>;
		};

		l2_cache_l4: l2-cache-l4 {
			compatible = "cache";
			cache-size = <65536>;
			cache-line-size = <64>;
			cache-sets = <256>;
			cache-level = <2>;
			cache-unified;
			next-level-cache = <&l3_cache>;
		};

		l2_cache_l5: l2-cache-l5 {
			compatible = "cache";
			cache-size = <65536>;
			cache-line-size = <64>;
			cache-sets = <256>;
			cache-level = <2>;
			cache-unified;
			next-level-cache = <&l3_cache>;
		};

		l3_cache: l3-cache {
			compatible = "cache";
			cache-size = <524288>;
			cache-line-size = <64>;
			cache-sets = <512>;
			cache-level = <3>;
			cache-unified;
		};

		cpu-map {
			cluster0 {
				core0 {
					cpu = <&A55_0>;
				};

				core1 {
					cpu = <&A55_1>;
				};

				core2 {
					cpu = <&A55_2>;
				};

				core3 {
					cpu = <&A55_3>;
				};

				core4 {
					cpu = <&A55_4>;
				};

				core5 {
					cpu = <&A55_5>;
				};
			};
		};
	};

	dummy: clock-dummy {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <0>;
		clock-output-names = "dummy";
	};

	clk_ext1: clock-ext1 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <133000000>;
		clock-output-names = "clk_ext1";
	};

	sai1_mclk: clock-sai-mclk1 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency= <0>;
		clock-output-names = "sai1_mclk";
	};

	sai2_mclk: clock-sai-mclk2 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency= <0>;
		clock-output-names = "sai2_mclk";
	};

	sai3_mclk: clock-sai-mclk3 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency= <0>;
		clock-output-names = "sai3_mclk";
	};

	sai4_mclk: clock-sai-mclk4 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency= <0>;
		clock-output-names = "sai4_mclk";
	};

	sai5_mclk: clock-sai-mclk5 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency= <0>;
		clock-output-names = "sai5_mclk";
	};

	mqs1: mqs1 {
		compatible = "fsl,imx95-aonmix-mqs";
		status = "disabled";
	};

	mqs2: mqs2 {
		compatible = "fsl,imx95-netcmix-mqs";
		status = "disabled";
	};

	osc_24m: clock-24m {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <24000000>;
		clock-output-names = "osc_24m";
	};

	ldb_pll_pixel: ldb_pll_div7 {
		#clock-cells = <0>;
		compatible = "fixed-factor-clock";
		clocks = <&scmi_clk IMX95_CLK_LDBPLL>;
		clock-div = <7>;
		clock-mult = <1>;
		clock-output-names = "ldb_pll_div7";
	};

	sram1: sram@204c0000 {
		compatible = "mmio-sram";
		reg = <0x0 0x204c0000 0x0 0x18000>;
		ranges = <0x0 0x0 0x204c0000 0x18000>;
		#address-cells = <1>;
		#size-cells = <1>;
	};

	firmware {
		scmi {
			compatible = "arm,scmi";
			mboxes = <&mu2 5 0>, <&mu2 3 0>, <&mu2 3 1>, <&mu2 5 1>;
			shmem = <&scmi_buf0>, <&scmi_buf1>;
			#address-cells = <1>;
			#size-cells = <0>;
			arm,max-rx-timeout-ms = <5000>;

			scmi_devpd: protocol@11 {
				reg = <0x11>;
				#power-domain-cells = <1>;
			};

			scmi_sys_power: protocol@12 {
				reg = <0x12>;
			};

			scmi_perf: protocol@13 {
				reg = <0x13>;
				#power-domain-cells = <1>;
			};

			scmi_clk: protocol@14 {
				reg = <0x14>;
				#clock-cells = <1>;
			};

			scmi_sensor: protocol@15 {
				reg = <0x15>;
				#thermal-sensor-cells = <1>;
			};

			scmi_iomuxc: protocol@19 {
				reg = <0x19>;
			};

			scmi_bbm: protocol@81 {
				reg = <0x81>;
			};

			scmi_misc: protocol@84 {
				reg = <0x84>;
			};
		};
	};

	pmu {
		compatible = "arm,cortex-a55-pmu";
		interrupts = <GIC_PPI 7 (GIC_CPU_MASK_SIMPLE(6) | IRQ_TYPE_LEVEL_HIGH)>;
	};

	thermal_zones: thermal-zones {
		a55-thermal {
			polling-delay-passive = <250>;
			polling-delay = <2000>;
			thermal-sensors = <&scmi_sensor 1>;
			trips {
				cpu_alert0: trip0 {
					temperature = <105000>;
					hysteresis = <2000>;
					type = "passive";
				};

				cpu_crit0: trip1 {
					temperature = <125000>;
					hysteresis = <2000>;
					type = "critical";
				};
			};

			cooling-maps {
				map0 {
					trip = <&cpu_alert0>;
					cooling-device =
						<&A55_0 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&A55_1 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&A55_2 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&A55_3 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&A55_4 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&A55_5 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
				};
			};
		};

		ana {
			polling-delay-passive = <250>;
			polling-delay = <2000>;
			thermal-sensors = <&scmi_sensor 0>;
			trips {
				ana_alert: trip0 {
					temperature = <105000>;
					hysteresis = <2000>;
					type = "passive";
				};

				ana_crit0: trip1 {
					temperature = <125000>;
					hysteresis = <2000>;
					type = "critical";
				};
			};

			cooling-maps {
				map0 {
					trip = <&ana_alert>;
					cooling-device =
						<&A55_0 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&A55_1 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&A55_2 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&A55_3 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&A55_4 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&A55_5 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&mali  THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&vpuctrl THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
				};
				map1 {
					trip = <&ana_crit0>;
					cooling-device = <&mali THERMAL_NO_LIMIT 1>;
				};
			};
		};
	};

	etm0: etm@40840000 {
		compatible = "arm,coresight-etm4x", "arm,primecell";
		reg = <0x0 0x40840000 0x0 0x10000>;
		arm,primecell-periphid = <0xbb95d>;
		cpu = <&A55_0>;
		clocks = <&scmi_clk IMX95_CLK_A55PERIPH>;
		clock-names = "apb_pclk";
		status = "disabled";

		out-ports {
			port {
				etm0_out_port: endpoint {
					remote-endpoint = <&ca_funnel_in_port0>;
				};
			};
		};
	};

	funnel0: funnel {
		/*
		 * non-configurable funnel don't show up on the AMBA
		 * bus.  As such no need to add "arm,primecell".
		 */
		compatible = "arm,coresight-static-funnel";
		status = "disabled";

		in-ports {
			port {
				ca_funnel_in_port0: endpoint {
					remote-endpoint = <&etm0_out_port>;
				};
			};
		};

		out-ports {
			port {
				ca_funnel_out_port0: endpoint {
					remote-endpoint = <&hugo_funnel_in_port0>;
				};
			};
		};
	};

	funnel1: funnel_sys {
		compatible = "arm,coresight-static-funnel";
		status = "disabled";

		in-ports {
			port {
				hugo_funnel_in_port0: endpoint {
					remote-endpoint = <&ca_funnel_out_port0>;
				};
			};
		};

		out-ports {
			port {
				hugo_funnel_out_port0: endpoint {
					remote-endpoint = <&etf_in_port>;
				};
			};
		};
	};

	etf: etf@41030000 {
		compatible = "arm,coresight-tmc", "arm,primecell";
		reg = <0x0 0x41030000 0x0 0x1000>;
		clocks = <&scmi_clk IMX95_CLK_A55PERIPH>;
		clock-names = "apb_pclk";
		status = "disabled";

		in-ports {
			port {
				etf_in_port: endpoint {
					remote-endpoint = <&hugo_funnel_out_port0>;
				};
			};
		};

		out-ports {
			port {
				etf_out_port: endpoint {
					remote-endpoint = <&etr_in_port>;
				};
			};
		};
	};

	etr: etr@41040000 {
		compatible = "arm,coresight-tmc", "arm,primecell";
		reg = <0x0 0x41040000 0x0 0x1000>;
		clocks = <&scmi_clk IMX95_CLK_A55PERIPH>;
		clock-names = "apb_pclk";
		status = "disabled";

		in-ports {
			port {
				etr_in_port: endpoint {
					remote-endpoint = <&etf_out_port>;
				};
			};
		};
	};

	psci {
		compatible = "arm,psci-1.0";
		method = "smc";
	};

	timer {
		compatible = "arm,armv8-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(6) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(6) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(6) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(6) | IRQ_TYPE_LEVEL_LOW)>;
		clock-frequency = <24000000>;
		arm,no-tick-in-suspend;
		interrupt-parent = <&gic>;
	};

	gic: interrupt-controller@48000000 {
		compatible = "arm,gic-v3";
		reg = <0 0x48000000 0 0x10000>,
		      <0 0x48060000 0 0xc0000>;
		#address-cells = <2>;
		#size-cells = <2>;
		#interrupt-cells = <3>;
		interrupt-controller;
		interrupts = <GIC_PPI 9 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-parent = <&gic>;
		dma-noncoherent;
		ranges;

		its: msi-controller@48040000 {
			compatible = "arm,gic-v3-its";
			reg = <0 0x48040000 0 0x20000>;
			msi-controller;
			#msi-cells = <1>;
			dma-noncoherent;
		};
	};

	gpu_opp_table: opp_table {
		compatible = "operating-points-v2", "operating-points-v2-mali";

		opp-500000000 {
			opp-hz = /bits/ 64 <500000000>;
			opp-hz-real = /bits/ 64 <500000000>;
			opp-microvolt = <920000>;
		};

		opp-800000000 {
			opp-hz = /bits/ 64 <800000000>;
			opp-hz-real = /bits/ 64 <800000000>;
			opp-microvolt = <920000>;
		};

		opp-1000000000 {
			opp-hz = /bits/ 64 <1000000000>;
			opp-hz-real = /bits/ 64 <1000000000>;
			opp-microvolt = <920000>;
		};
	};

	usbphynop: usbphynop {
		compatible = "usb-nop-xceiv";
		#phy-cells = <0>;
		clocks = <&scmi_clk IMX95_CLK_HSIO>;
		clock-names = "main_clk";
	};

	imx95_soc: imx95-soc {
		compatible = "fsl,imx95-soc";
	};

	soc {
		compatible = "simple-bus";
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;

		aips2: bus@42000000 {
			compatible = "fsl,aips-bus", "simple-bus";
			reg = <0x0 0x42000000 0x0 0x800000>;
			ranges = <0x42000000 0x0 0x42000000 0x8000000>,
				 <0x28000000 0x0 0x28000000 0x10000000>;
			#address-cells = <1>;
			#size-cells = <1>;

			edma2: dma-controller@42000000 {
				compatible = "fsl,imx95-edma5";
				reg = <0x42000000 0x210000>;
				#dma-cells = <3>;
				dma-channels = <64>;
				interrupts = <GIC_SPI 128 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 128 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 129 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 129 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 130 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 130 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 131 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 131 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 132 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 132 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 133 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 133 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 134 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 134 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 135 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 135 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 136 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 136 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 137 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 137 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 138 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 138 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 139 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 139 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 140 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 140 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 141 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 141 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 142 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 142 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 143 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 143 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 144 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 144 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 145 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 145 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 146 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 146 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 147 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 147 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 148 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 148 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 149 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 149 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 150 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 150 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 151 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 151 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 152 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 152 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 153 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 153 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 154 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 154 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 155 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 155 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 156 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 156 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 157 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 157 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 158 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 158 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 159 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 159 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 127 IRQ_TYPE_LEVEL_HIGH>; //error irq
				clocks = <&scmi_clk IMX95_CLK_BUSWAKEUP>;
				clock-names = "dma";
			};

			edma3: dma-controller@42210000 {
				compatible = "fsl,imx95-edma5";
				reg = <0x42210000 0x210000>;
				#dma-cells = <3>;
				dma-channels = <64>;
				interrupts = <GIC_SPI 256 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 256 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 257 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 257 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 258 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 258 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 259 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 259 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 260 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 260 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 261 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 261 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 262 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 262 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 263 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 263 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 264 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 264 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 265 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 265 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 266 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 266 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 267 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 267 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 268 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 268 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 269 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 269 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 270 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 270 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 271 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 271 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 272 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 272 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 273 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 273 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 274 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 274 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 275 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 275 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 276 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 276 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 277 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 277 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 278 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 278 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 279 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 279 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 280 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 280 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 281 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 281 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 282 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 282 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 283 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 283 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 284 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 284 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 285 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 285 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 286 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 286 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 287 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 287 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 250 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_BUSWAKEUP>;
				clock-names = "dma";
				status = "disabled";
			};

			mu7: mailbox@42430000 {
				compatible = "fsl,imx95-mu";
				reg = <0x42430000 0x10000>;
				interrupts = <GIC_SPI 234 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_BUSWAKEUP>;
				#mbox-cells = <2>;
				status = "disabled";
			};

			wdog3: watchdog@42490000 {
				compatible = "fsl,imx93-wdt";
				reg = <0x42490000 0x10000>;
				interrupts = <GIC_SPI 77 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_BUSWAKEUP>;
				timeout-sec = <40>;
				status = "disabled";
			};

			tpm3: pwm@424e0000 {
				compatible = "fsl,imx7ulp-pwm";
				reg = <0x424e0000 0x1000>;
				clocks = <&scmi_clk IMX95_CLK_BUSWAKEUP>;
				#pwm-cells = <3>;
				status = "disabled";
			};

			tpm4: pwm@424f0000 {
				compatible = "fsl,imx7ulp-pwm";
				reg = <0x424f0000 0x1000>;
				clocks = <&scmi_clk IMX95_CLK_TPM4>;
				#pwm-cells = <3>;
				status = "disabled";
			};

			tpm5: pwm@42500000 {
				compatible = "fsl,imx7ulp-pwm";
				reg = <0x42500000 0x1000>;
				clocks = <&scmi_clk IMX95_CLK_TPM5>;
				#pwm-cells = <3>;
				status = "disabled";
			};

			tpm6: pwm@42510000 {
				compatible = "fsl,imx7ulp-pwm";
				reg = <0x42510000 0x1000>;
				clocks = <&scmi_clk IMX95_CLK_TPM6>;
				#pwm-cells = <3>;
				status = "disabled";
			};

			lpi2c3: i2c@42530000 {
				compatible = "fsl,imx95-lpi2c", "fsl,imx7ulp-lpi2c";
				reg = <0x42530000 0x10000>;
				interrupts = <GIC_SPI 58 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_LPI2C3>,
					 <&scmi_clk IMX95_CLK_BUSWAKEUP>;
				clock-names = "per", "ipg";
				#address-cells = <1>;
				#size-cells = <0>;
				dmas = <&edma2 8 0 0>, <&edma2 9 0 FSL_EDMA_RX>;
				dma-names = "tx", "rx";
				status = "disabled";
			};

			lpi2c4: i2c@42540000 {
				compatible = "fsl,imx95-lpi2c", "fsl,imx7ulp-lpi2c";
				reg = <0x42540000 0x10000>;
				interrupts = <GIC_SPI 59 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_LPI2C4>,
					 <&scmi_clk IMX95_CLK_BUSWAKEUP>;
				clock-names = "per", "ipg";
				#address-cells = <1>;
				#size-cells = <0>;
				dmas = <&edma2 10 0 0>, <&edma2 11 0 FSL_EDMA_RX>;
				dma-names = "tx", "rx";
				status = "disabled";
			};

			lpspi3: spi@42550000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "fsl,imx95-spi", "fsl,imx7ulp-spi";
				reg = <0x42550000 0x10000>;
				interrupts = <GIC_SPI 61 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_LPSPI3>,
					 <&scmi_clk IMX95_CLK_BUSWAKEUP>;
				clock-names = "per", "ipg";
				dmas = <&edma2 12 0 0>, <&edma2 13 0 FSL_EDMA_RX>;
				dma-names = "tx", "rx";
				status = "disabled";
			};

			lpspi4: spi@42560000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "fsl,imx95-spi", "fsl,imx7ulp-spi";
				reg = <0x42560000 0x10000>;
				interrupts = <GIC_SPI 62 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_LPSPI4>,
					 <&scmi_clk IMX95_CLK_BUSWAKEUP>;
				clock-names = "per", "ipg";
				dmas = <&edma2 14 0 0>, <&edma2 15 0 FSL_EDMA_RX>;
				dma-names = "tx", "rx";
				status = "disabled";
			};

			lpuart3: serial@42570000 {
				compatible = "fsl,imx95-lpuart", "fsl,imx8ulp-lpuart",
					     "fsl,imx7ulp-lpuart";
				reg = <0x42570000 0x1000>;
				interrupts = <GIC_SPI 64 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_LPUART3>;
				clock-names = "ipg";
				dmas = <&edma2 18 0 FSL_EDMA_RX>, <&edma2 17 0 0>;
				dma-names = "rx", "tx";
				status = "disabled";
			};

			lpuart4: serial@42580000 {
				compatible = "fsl,imx95-lpuart", "fsl,imx8ulp-lpuart",
					     "fsl,imx7ulp-lpuart";
				reg = <0x42580000 0x1000>;
				interrupts = <GIC_SPI 65 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_LPUART4>;
				clock-names = "ipg";
				dmas = <&edma2 20 0 FSL_EDMA_RX>, <&edma2 19 0 0>;
				dma-names = "rx", "tx";
				status = "disabled";
			};

			lpuart5: serial@42590000 {
				compatible = "fsl,imx95-lpuart", "fsl,imx8ulp-lpuart",
					     "fsl,imx7ulp-lpuart";
				reg = <0x42590000 0x1000>;
				interrupts = <GIC_SPI 66 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_LPUART5>;
				clock-names = "ipg";
				dmas = <&edma2 22 0 FSL_EDMA_RX>, <&edma2 21 0 0>;
				dma-names = "rx", "tx";
				status = "disabled";
			};

			lpuart6: serial@425a0000 {
				compatible = "fsl,imx95-lpuart", "fsl,imx8ulp-lpuart",
					     "fsl,imx7ulp-lpuart";
				reg = <0x425a0000 0x1000>;
				interrupts = <GIC_SPI 67 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_LPUART6>;
				clock-names = "ipg";
				dmas = <&edma2 24 0 FSL_EDMA_RX>, <&edma2 23 0 0>;
				dma-names = "rx", "tx";
				status = "disabled";
			};

			flexcan2: can@425b0000 {
				compatible = "fsl,imx95-flexcan";
				reg = <0x425b0000 0x10000>;
				interrupts = <GIC_SPI 38 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_BUSWAKEUP>,
					 <&scmi_clk IMX95_CLK_CAN2>;
				clock-names = "ipg", "per";
				assigned-clocks = <&scmi_clk IMX95_CLK_CAN2>;
				assigned-clock-parents = <&scmi_clk IMX95_CLK_SYSPLL1_PFD1_DIV2>;
				assigned-clock-rates = <40000000>;
				fsl,clk-source = /bits/ 8 <0>;
				status = "disabled";
			};

			flexcan3: can@42600000 {
				compatible = "fsl,imx95-flexcan";
				reg = <0x42600000 0x10000>;
				interrupts = <GIC_SPI 40 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_BUSWAKEUP>,
					 <&scmi_clk IMX95_CLK_CAN3>;
				clock-names = "ipg", "per";
				assigned-clocks = <&scmi_clk IMX95_CLK_CAN3>;
				assigned-clock-parents = <&scmi_clk IMX95_CLK_SYSPLL1_PFD1_DIV2>;
				assigned-clock-rates = <40000000>;
				fsl,clk-source = /bits/ 8 <0>;
				status = "disabled";
			};

			flexspi1: spi@425e0000 {
				compatible = "nxp,imx8mm-fspi";
				reg = <0x425e0000 0x10000>, <0x28000000 0x8000000>;
				reg-names = "fspi_base", "fspi_mmap";
				#address-cells = <1>;
				#size-cells = <0>;
				interrupts = <GIC_SPI 48 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_FLEXSPI1>,
					 <&scmi_clk IMX95_CLK_FLEXSPI1>;
				clock-names = "fspi_en", "fspi";
				assigned-clocks = <&scmi_clk IMX95_CLK_FLEXSPI1>;
				assigned-clock-parents = <&scmi_clk IMX95_CLK_SYSPLL1_PFD1>;
				assigned-clock-rates = <200000000>;
				status = "disabled";
			};

			sai3: sai@42650000 {
				compatible = "fsl,imx95-sai";
				reg = <0x42650000 0x10000>;
				interrupts = <GIC_SPI 170 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_BUSWAKEUP>, <&dummy>,
					 <&scmi_clk IMX95_CLK_SAI3>, <&dummy>,
					 <&dummy>;
				clock-names = "bus", "mclk0", "mclk1", "mclk2", "mclk3";
				dmas = <&edma2 61 0 FSL_EDMA_RX>, <&edma2 60 0 0>;
				dma-names = "rx", "tx";
				status = "disabled";
			};

			sai4: sai@42660000 {
				compatible = "fsl,imx95-sai";
				reg = <0x42660000 0x10000>;
				interrupts = <GIC_SPI 171 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_BUSWAKEUP>, <&dummy>,
					 <&scmi_clk IMX95_CLK_SAI4>, <&dummy>,
					 <&dummy>;
				clock-names = "bus", "mclk0", "mclk1", "mclk2", "mclk3";
				dmas = <&edma2 68 0 FSL_EDMA_RX>, <&edma2 67 0 0>;
				dma-names = "rx", "tx";
				status = "disabled";
			};

			sai5: sai@42670000 {
				compatible = "fsl,imx95-sai";
				reg = <0x42670000 0x10000>;
				interrupts = <GIC_SPI 172 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_BUSWAKEUP>, <&dummy>,
					 <&scmi_clk IMX95_CLK_SAI5>, <&dummy>,
					 <&dummy>;
				clock-names = "bus", "mclk0", "mclk1", "mclk2", "mclk3";
				dmas = <&edma2 70 0 FSL_EDMA_RX>, <&edma2 69 0 0>;
				dma-names = "rx", "tx";
				status = "disabled";
			};

			xcvr: xcvr@42680000 {
				compatible = "fsl,imx95-xcvr";
				reg = <0x42680000 0x800>, <0x42680800 0x400>,
				      <0x42680c00 0x080>, <0x42680e00 0x080>;
				reg-names = "ram", "regs", "rxfifo", "txfifo";
				interrupts = /* XCVR IRQ 0 */
					     <GIC_SPI 189 IRQ_TYPE_LEVEL_HIGH>,
					     /* XCVR IRQ 1 */
					     <GIC_SPI 190 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_BUSWAKEUP>,
					 <&scmi_clk IMX95_CLK_SPDIF>,
					 <&dummy>,
					 <&scmi_clk IMX95_CLK_AUDIOXCVR>;
				clock-names = "ipg", "phy", "spba", "pll_ipg";
				dmas = <&edma2 65 0 1>, <&edma2 66 0 0>;
				dma-names = "rx", "tx";
				status = "disabled";
			};

			flexio_i2c_master: flexio@425c0000 {
				compatible = "imx,flexio_i2c_master";
				reg = <0x425c0000 0x10000>;
				interrupts = <GIC_SPI 46 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_BUSWAKEUP>,
					 <&scmi_clk IMX95_CLK_BUSWAKEUP>;
				clock-names = "per", "ipg";
				assigned-clocks = <&scmi_clk IMX95_CLK_BUSWAKEUP>;
				assigned-clock-parents = <&scmi_clk IMX95_CLK_BUSWAKEUP>;
				assigned-clock-rates = <24000000>;
				status = "disabled";
			};

			lpuart7: serial@42690000 {
				compatible = "fsl,imx95-lpuart", "fsl,imx8ulp-lpuart",
					     "fsl,imx7ulp-lpuart";
				reg = <0x42690000 0x1000>;
				interrupts = <GIC_SPI 68 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_LPUART7>;
				clock-names = "ipg";
				dmas = <&edma2 88 0 FSL_EDMA_RX>, <&edma2 87 0 0>;
				dma-names = "rx", "tx";
				status = "disabled";
			};

			lpuart8: serial@426a0000 {
				compatible = "fsl,imx95-lpuart", "fsl,imx8ulp-lpuart",
					     "fsl,imx7ulp-lpuart";
				reg = <0x426a0000 0x1000>;
				interrupts = <GIC_SPI 69 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_LPUART8>;
				clock-names = "ipg";
				dmas = <&edma2 90 0 FSL_EDMA_RX>, <&edma2 89 0 0>;
				dma-names = "rx", "tx";
				status = "disabled";
			};

			lpi2c5: i2c@426b0000 {
				compatible = "fsl,imx95-lpi2c", "fsl,imx7ulp-lpi2c";
				reg = <0x426b0000 0x10000>;
				interrupts = <GIC_SPI 181 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_LPI2C5>,
					 <&scmi_clk IMX95_CLK_BUSWAKEUP>;
				clock-names = "per", "ipg";
				#address-cells = <1>;
				#size-cells = <0>;
				dmas = <&edma2 71 0 0>, <&edma2 72 0 FSL_EDMA_RX>;
				dma-names = "tx", "rx";
				status = "disabled";
			};

			lpi2c6: i2c@426c0000 {
				compatible = "fsl,imx95-lpi2c", "fsl,imx7ulp-lpi2c";
				reg = <0x426c0000 0x10000>;
				interrupts = <GIC_SPI 182 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_LPI2C6>,
					 <&scmi_clk IMX95_CLK_BUSWAKEUP>;
				clock-names = "per", "ipg";
				#address-cells = <1>;
				#size-cells = <0>;
				dmas = <&edma2 73 0 0>, <&edma2 74 0 FSL_EDMA_RX>;
				dma-names = "tx", "rx";
				status = "disabled";
			};

			lpi2c7: i2c@426d0000 {
				compatible = "fsl,imx95-lpi2c", "fsl,imx7ulp-lpi2c";
				reg = <0x426d0000 0x10000>;
				interrupts = <GIC_SPI 183 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_LPI2C7>,
					 <&scmi_clk IMX95_CLK_BUSWAKEUP>;
				clock-names = "per", "ipg";
				#address-cells = <1>;
				#size-cells = <0>;
				dmas = <&edma2 75 0 0>, <&edma2 76 0 FSL_EDMA_RX>;
				dma-names = "tx", "rx";
				status = "disabled";
			};

			lpi2c8: i2c@426e0000 {
				compatible = "fsl,imx95-lpi2c", "fsl,imx7ulp-lpi2c";
				reg = <0x426e0000 0x10000>;
				interrupts = <GIC_SPI 184 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_LPI2C8>,
					 <&scmi_clk IMX95_CLK_BUSWAKEUP>;
				clock-names = "per", "ipg";
				#address-cells = <1>;
				#size-cells = <0>;
				dmas = <&edma2 77 0 0>, <&edma2 78 0 FSL_EDMA_RX>;
				dma-names = "tx", "rx";
				status = "disabled";
			};

			lpspi5: spi@426f0000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "fsl,imx95-spi", "fsl,imx7ulp-spi";
				reg = <0x426f0000 0x10000>;
				interrupts = <GIC_SPI 177 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_LPSPI5>,
					 <&scmi_clk IMX95_CLK_BUSWAKEUP>;
				clock-names = "per", "ipg";
				dmas = <&edma2 79 0 0>, <&edma2 80 0 FSL_EDMA_RX>;
				dma-names = "tx", "rx";
				status = "disabled";
			};

			lpspi6: spi@42700000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "fsl,imx95-spi", "fsl,imx7ulp-spi";
				reg = <0x42700000 0x10000>;
				interrupts = <GIC_SPI 178 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_LPSPI6>,
					 <&scmi_clk IMX95_CLK_BUSWAKEUP>;
				clock-names = "per", "ipg";
				dmas = <&edma2 81 0 0>, <&edma2 82 0 FSL_EDMA_RX>;
				dma-names = "tx", "rx";
				status = "disabled";
			};

			lpspi7: spi@42710000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "fsl,imx95-spi", "fsl,imx7ulp-spi";
				reg = <0x42710000 0x10000>;
				interrupts = <GIC_SPI 179 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_LPSPI7>,
					 <&scmi_clk IMX95_CLK_BUSWAKEUP>;
				clock-names = "per", "ipg";
				dmas = <&edma2 83 0 0>, <&edma2 84 0 FSL_EDMA_RX>;
				dma-names = "tx", "rx";
				status = "disabled";
			};

			lpspi8: spi@42720000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "fsl,imx95-spi", "fsl,imx7ulp-spi";
				reg = <0x42720000 0x10000>;
				interrupts = <GIC_SPI 180 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_LPSPI8>,
					 <&scmi_clk IMX95_CLK_BUSWAKEUP>;
				clock-names = "per", "ipg";
				dmas = <&edma2 85 0 0>, <&edma2 86 0 FSL_EDMA_RX>;
				dma-names = "tx", "rx";
				status = "disabled";
			};

			mu8: mailbox@42730000 {
				compatible = "fsl,imx95-mu";
				reg = <0x42730000 0x10000>;
				interrupts = <GIC_SPI 235 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_BUSWAKEUP>;
				#mbox-cells = <2>;
				status = "disabled";
			};

			flexcan4: can@427c0000 {
				compatible = "fsl,imx95-flexcan";
				reg = <0x427c0000 0x10000>;
				interrupts = <GIC_SPI 42 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_BUSWAKEUP>,
					 <&scmi_clk IMX95_CLK_CAN4>;
				clock-names = "ipg", "per";
				assigned-clocks = <&scmi_clk IMX95_CLK_CAN4>;
				assigned-clock-parents = <&scmi_clk IMX95_CLK_SYSPLL1_PFD1_DIV2>;
				assigned-clock-rates = <40000000>;
				fsl,clk-source = /bits/ 8 <0>;
				status = "disabled";
			};

			flexcan5: can@427d0000 {
				compatible = "fsl,imx95-flexcan";
				reg = <0x427d0000 0x10000>;
				interrupts = <GIC_SPI 44 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_BUSWAKEUP>,
					 <&scmi_clk IMX95_CLK_CAN5>;
				clock-names = "ipg", "per";
				assigned-clocks = <&scmi_clk IMX95_CLK_CAN5>;
				assigned-clock-parents = <&scmi_clk IMX95_CLK_SYSPLL1_PFD1_DIV2>;
				assigned-clock-rates = <40000000>;
				fsl,clk-source = /bits/ 8 <0>;
				status = "disabled";
			};
		};

		aips3: bus@42800000 {
			compatible = "fsl,aips-bus", "simple-bus";
			reg = <0 0x42800000 0 0x800000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges = <0x42800000 0x0 0x42800000 0x800000>;

			usdhc1: mmc@42850000 {
				compatible = "fsl,imx95-usdhc", "fsl,imx8mm-usdhc";
				reg = <0x42850000 0x10000>;
				interrupts = <GIC_SPI 86 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_BUSWAKEUP>,
					 <&scmi_clk IMX95_CLK_WAKEUPAXI>,
					 <&scmi_clk IMX95_CLK_USDHC1>;
				clock-names = "ipg", "ahb", "per";
				assigned-clocks = <&scmi_clk IMX95_CLK_USDHC1>;
				assigned-clock-parents = <&scmi_clk IMX95_CLK_SYSPLL1_PFD1>;
				assigned-clock-rates = <400000000>;
				bus-width = <8>;
				fsl,tuning-start-tap = <1>;
				fsl,tuning-step= <2>;
				status = "disabled";
			};

			usdhc2: mmc@42860000 {
				compatible = "fsl,imx95-usdhc", "fsl,imx8mm-usdhc";
				reg = <0x42860000 0x10000>;
				interrupts = <GIC_SPI 87 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_BUSWAKEUP>,
					 <&scmi_clk IMX95_CLK_WAKEUPAXI>,
					 <&scmi_clk IMX95_CLK_USDHC2>;
				clock-names = "ipg", "ahb", "per";
				assigned-clocks = <&scmi_clk IMX95_CLK_USDHC2>;
				assigned-clock-parents = <&scmi_clk IMX95_CLK_SYSPLL1_PFD1>;
				assigned-clock-rates = <400000000>;
				bus-width = <4>;
				fsl,tuning-start-tap = <1>;
				fsl,tuning-step= <2>;
				status = "disabled";
			};

			usdhc3: mmc@428b0000 {
				compatible = "fsl,imx95-usdhc", "fsl,imx8mm-usdhc";
				reg = <0x428b0000 0x10000>;
				interrupts = <GIC_SPI 191 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_BUSWAKEUP>,
					 <&scmi_clk IMX95_CLK_WAKEUPAXI>,
					 <&scmi_clk IMX95_CLK_USDHC3>;
				clock-names = "ipg", "ahb", "per";
				assigned-clocks = <&scmi_clk IMX95_CLK_USDHC3>;
				assigned-clock-parents = <&scmi_clk IMX95_CLK_SYSPLL1_PFD1>;
				assigned-clock-rates = <400000000>;
				bus-width = <4>;
				fsl,tuning-start-tap = <1>;
				fsl,tuning-step= <2>;
				status = "disabled";
			};
		};

		gpio2: gpio@43810000 {
			compatible = "fsl,imx95-gpio", "fsl,imx8ulp-gpio";
			reg = <0x0 0x43810000 0x0 0x1000>;
			gpio-controller;
			#gpio-cells = <2>;
			interrupts = <GIC_SPI 49 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 50 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-controller;
			#interrupt-cells = <2>;
			clocks = <&scmi_clk IMX95_CLK_BUSWAKEUP>,
				 <&scmi_clk IMX95_CLK_BUSWAKEUP>;
			clock-names = "gpio", "port";
			gpio-ranges = <&scmi_iomuxc 0 4 32>;
		};

		gpio3: gpio@43820000 {
			compatible = "fsl,imx95-gpio", "fsl,imx8ulp-gpio";
			reg = <0x0 0x43820000 0x0 0x1000>;
			gpio-controller;
			#gpio-cells = <2>;
			interrupts = <GIC_SPI 51 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 52 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-controller;
			#interrupt-cells = <2>;
			clocks = <&scmi_clk IMX95_CLK_BUSWAKEUP>,
				 <&scmi_clk IMX95_CLK_BUSWAKEUP>;
			clock-names = "gpio", "port";
			gpio-ranges = <&scmi_iomuxc 0 104 8>, <&scmi_iomuxc 8 74 18>,
				      <&scmi_iomuxc 26 42 2>, <&scmi_iomuxc 28 0 4>;
		};

		gpio4: gpio@43840000 {
			compatible = "fsl,imx95-gpio", "fsl,imx8ulp-gpio";
			reg = <0x0 0x43840000 0x0 0x1000>;
			gpio-controller;
			#gpio-cells = <2>;
			interrupts = <GIC_SPI 53 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 54 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-controller;
			#interrupt-cells = <2>;
			clocks = <&scmi_clk IMX95_CLK_BUSWAKEUP>,
				 <&scmi_clk IMX95_CLK_BUSWAKEUP>;
			clock-names = "gpio", "port";
			gpio-ranges = <&scmi_iomuxc 0 46 28>, <&scmi_iomuxc 28 44 2>;
		};

		gpio5: gpio@43850000 {
			compatible = "fsl,imx95-gpio", "fsl,imx8ulp-gpio";
			reg = <0x0 0x43850000 0x0 0x1000>;
			gpio-controller;
			#gpio-cells = <2>;
			interrupts = <GIC_SPI 55 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 56 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-controller;
			#interrupt-cells = <2>;
			clocks = <&scmi_clk IMX95_CLK_BUSWAKEUP>,
				 <&scmi_clk IMX95_CLK_BUSWAKEUP>;
			clock-names = "gpio", "port";
			gpio-ranges = <&scmi_iomuxc 0 92 12>, <&scmi_iomuxc 12 36 6>;
		};

		aips1: bus@44000000 {
			compatible = "fsl,aips-bus", "simple-bus";
			reg = <0x0 0x44000000 0x0 0x800000>;
			ranges = <0x44000000 0x0 0x44000000 0x800000>;
			#address-cells = <1>;
			#size-cells = <1>;

			edma1: dma-controller@44000000 {
				compatible = "fsl,imx93-edma3";
				reg = <0x44000000 0x200000>;
				#dma-cells = <3>;
				dma-channels = <31>;
				interrupts = <GIC_SPI 96 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 97 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 98 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 99 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 100 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 101 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 102 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 103 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 104 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 105 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 106 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 107 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 108 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 109 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 110 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 111 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 112 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 113 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 114 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 115 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 116 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 117 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 118 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 119 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 120 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 121 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 122 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 123 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 124 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 125 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 126 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 95 IRQ_TYPE_LEVEL_HIGH>; //error irq
				clocks = <&scmi_clk IMX95_CLK_BUSAON>;
				clock-names = "dma";
			};

			mu1: mailbox@44220000 {
				compatible = "fsl,imx95-mu";
				reg = <0x44220000 0x10000>;
				interrupts = <GIC_SPI 224 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_BUSAON>;
				#mbox-cells = <2>;
				status = "disabled";
			};

			system_counter: timer@44290000 {
				compatible = "nxp,imx95-sysctr-timer", "nxp,sysctr-timer";
				reg = <0x44290000 0x30000>;
				interrupts = <GIC_SPI 72 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&osc_24m>;
				clock-names = "per";
				nxp,no-divider;
			};

			tpm1: pwm@44310000 {
				compatible = "fsl,imx7ulp-pwm";
				reg = <0x44310000 0x1000>;
				clocks = <&scmi_clk IMX95_CLK_BUSAON>;
				#pwm-cells = <3>;
				status = "disabled";
			};

			tpm2: pwm@44320000 {
				compatible = "fsl,imx7ulp-pwm";
				reg = <0x44320000 0x1000>;
				clocks = <&scmi_clk IMX95_CLK_TPM2>;
				#pwm-cells = <3>;
				status = "disabled";
			};

			lpi2c1: i2c@44340000 {
				compatible = "fsl,imx95-lpi2c", "fsl,imx7ulp-lpi2c";
				reg = <0x44340000 0x10000>;
				interrupts = <GIC_SPI 13 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_LPI2C1>,
					 <&scmi_clk IMX95_CLK_BUSAON>;
				clock-names = "per", "ipg";
				#address-cells = <1>;
				#size-cells = <0>;
				dmas = <&edma1 12 0 0>, <&edma1 13 0 FSL_EDMA_RX> ;
				dma-names = "tx", "rx";
				status = "disabled";
			};

			lpi2c2: i2c@44350000 {
				compatible = "fsl,imx95-lpi2c", "fsl,imx7ulp-lpi2c";
				reg = <0x44350000 0x10000>;
				interrupts = <GIC_SPI 14 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_LPI2C2>,
					 <&scmi_clk IMX95_CLK_BUSAON>;
				clock-names = "per", "ipg";
				#address-cells = <1>;
				#size-cells = <0>;
				dmas = <&edma1 14 0 0>, <&edma1 15 0 FSL_EDMA_RX> ;
				dma-names = "tx", "rx";
				status = "disabled";
			};

			lpspi1: spi@44360000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "fsl,imx95-spi", "fsl,imx7ulp-spi";
				reg = <0x44360000 0x10000>;
				interrupts = <GIC_SPI 16 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_LPSPI1>,
					 <&scmi_clk IMX95_CLK_BUSAON>;
				clock-names = "per", "ipg";
				dmas = <&edma1 16 0 FSL_EDMA_RX>, <&edma1 17 0 0> ;
				dma-names = "tx", "rx";
				status = "disabled";
			};

			lpspi2: spi@44370000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "fsl,imx95-spi", "fsl,imx7ulp-spi";
				reg = <0x44370000 0x10000>;
				interrupts = <GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_LPSPI2>,
					 <&scmi_clk IMX95_CLK_BUSAON>;
				clock-names = "per", "ipg";
				dmas = <&edma1 18 0 FSL_EDMA_RX>, <&edma1 19 0 0> ;
				dma-names = "tx", "rx";
				status = "disabled";
			};

			lpuart1: serial@44380000 {
				compatible = "fsl,imx95-lpuart", "fsl,imx8ulp-lpuart",
					     "fsl,imx7ulp-lpuart";
				reg = <0x44380000 0x1000>;
				interrupts = <GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_LPUART1>;
				clock-names = "ipg";
				dmas = <&edma1 21 0 FSL_EDMA_RX>, <&edma1 20 0 0>;
				dma-names = "rx", "tx";
				status = "disabled";
			};

			lpuart2: serial@44390000 {
				compatible = "fsl,imx95-lpuart", "fsl,imx8ulp-lpuart",
					     "fsl,imx7ulp-lpuart";
				reg = <0x44390000 0x1000>;
				interrupts = <GIC_SPI 20 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_LPUART2>;
				clock-names = "ipg";
				dmas = <&edma1 23 0 FSL_EDMA_RX>, <&edma1 22 0 0>;
				dma-names = "rx", "tx";
				status = "disabled";
			};

			flexcan1: can@443a0000 {
				compatible = "fsl,imx95-flexcan";
				reg = <0x443a0000 0x10000>;
				interrupts = <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_BUSAON>,
					 <&scmi_clk IMX95_CLK_CAN1>;
				clock-names = "ipg", "per";
				assigned-clocks = <&scmi_clk IMX95_CLK_CAN1>;
				assigned-clock-parents = <&scmi_clk IMX95_CLK_SYSPLL1_PFD1_DIV2>;
				assigned-clock-rates = <40000000>;
				fsl,clk-source = /bits/ 8 <0>;
				status = "disabled";
			};

			sai1: sai@443b0000 {
				compatible = "fsl,imx95-sai";
				reg = <0x443b0000 0x10000>;
				interrupts = <GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_BUSAON>, <&dummy>,
					 <&scmi_clk IMX95_CLK_SAI1>, <&dummy>,
					 <&dummy>;
				clock-names = "bus", "mclk0", "mclk1", "mclk2", "mclk3";
				dmas = <&edma1 25 0 FSL_EDMA_RX>, <&edma1 24 0 0>;
				dma-names = "rx", "tx";
				status = "disabled";
			};

			micfil: micfil@44520000 {
				compatible = "fsl,imx95-micfil", "fsl,imx93-micfil";
				reg = <0x44520000 0x10000>;
				interrupts = <GIC_SPI 188 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 187 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 186 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 185 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_BUSAON>,
					 <&scmi_clk IMX95_CLK_PDM>,
					 <&scmi_clk IMX95_CLK_AUDIOPLL1>,
					 <&scmi_clk IMX95_CLK_AUDIOPLL2>,
					 <&dummy>;
				clock-names = "ipg_clk", "ipg_clk_app",
					      "pll8k", "pll11k", "clkext3";
				dmas = <&edma1 6 0 5>;
				dma-names = "rx";
				status = "disabled";
			};

			adc1: adc@44530000 {
				compatible = "nxp,imx93-adc";
				reg = <0x44530000 0x10000>;
				interrupts = <GIC_SPI 199 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 200 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 201 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_ADC>;
				clock-names = "ipg";
				status = "disabled";
			};

			mu2: mailbox@445b0000 {
				compatible = "fsl,imx95-mu";
				reg = <0x445b0000 0x1000>;
				ranges;
				interrupts = <GIC_SPI 226 IRQ_TYPE_LEVEL_HIGH>;
				#address-cells = <1>;
				#size-cells = <1>;
				#mbox-cells = <2>;

				sram0: sram@445b1000 {
					compatible = "mmio-sram";
					reg = <0x445b1000 0x400>;
					ranges = <0x0 0x445b1000 0x400>;
					#address-cells = <1>;
					#size-cells = <1>;

					scmi_buf0: scmi-sram-section@0 {
						compatible = "arm,scmi-shmem";
						reg = <0x0 0x80>;
					};

					scmi_buf1: scmi-sram-section@80 {
						compatible = "arm,scmi-shmem";
						reg = <0x80 0x80>;
					};
				};

			};

			mu3: mailbox@445d0000 {
				compatible = "fsl,imx95-mu";
				reg = <0x445d0000 0x10000>;
				interrupts = <GIC_SPI 228 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_BUSAON>;
				#mbox-cells = <2>;
				status = "disabled";
			};

			mu4: mailbox@445f0000 {
				compatible = "fsl,imx95-mu";
				reg = <0x445f0000 0x10000>;
				interrupts = <GIC_SPI 230 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_BUSAON>;
				#mbox-cells = <2>;
				status = "disabled";
			};

			mu6: mailbox@44630000 {
				compatible = "fsl,imx95-mu";
				reg = <0x44630000 0x10000>;
				interrupts = <GIC_SPI 206 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&scmi_clk IMX95_CLK_BUSAON>;
				#mbox-cells = <2>;
				status = "disabled";
			};
		};

		v2x_mu4: mailbox@47300000 {
			compatible = "fsl,imx95-mu-v2x";
			reg = <0x0 0x47300000 0x0 0x10000>;
			interrupts = <GIC_SPI 27 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "tx", "rx";
			#mbox-cells = <2>;
			status = "okay";
		};

		v2x_mu6: mailbox@47320000 {
			compatible = "fsl,imx95-mu-v2x";
			reg = <0x0 0x47320000 0x0 0x10000>;
			interrupts = <GIC_SPI 254 IRQ_TYPE_LEVEL_HIGH>;
			#mbox-cells = <2>;
		};

		v2x_mu: mailbox@47350000 {
			compatible = "fsl,imx95-mu-v2x";
			reg = <0x0 0x47350000 0x0 0x10000>;
			interrupts = <GIC_SPI 255 IRQ_TYPE_LEVEL_HIGH>;
			#mbox-cells = <2>;
		};

		/* GPIO1 is under exclusive control of System Manager */
		gpio1: gpio@47400000 {
			compatible = "fsl,imx95-gpio", "fsl,imx8ulp-gpio";
			reg = <0x0 0x47400000 0x0 0x1000>;
			gpio-controller;
			#gpio-cells = <2>;
			interrupts = <GIC_SPI 10 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 11 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-controller;
			#interrupt-cells = <2>;
			clocks = <&scmi_clk IMX95_CLK_M33>,
				 <&scmi_clk IMX95_CLK_M33>;
			clock-names = "gpio", "port";
			gpio-ranges = <&scmi_iomuxc 0 112 16>;
			status = "disabled";
		};

		ocotp: efuse@47510000 {
			compatible = "fsl,imx95-ocotp";
			reg = <0x0 0x47510000 0x0 0x10000>;
			#address-cells = <1>;
			#size-cells = <1>;

			eth_mac0: mac-address@0 {
				reg = <0x0514 0x6>;
			};

			eth_mac1: mac-address@1 {
				reg = <0x1514 0x6>;
			};

			eth_mac2: mac-address@2 {
				reg = <0x2514 0x6>;
			};
		};

		elemu0: mailbox@47520000 {
			compatible = "fsl,imx95-mu-ele";
			reg = <0x0 0x47520000 0x0 0x10000>;
			interrupts = <GIC_SPI 21 IRQ_TYPE_LEVEL_HIGH>;
			#mbox-cells = <2>;
			status = "disabled";
		};

		elemu1: mailbox@47530000 {
			compatible = "fsl,imx95-mu-ele";
			reg = <0x0 0x47530000 0x0 0x10000>;
			interrupts = <GIC_SPI 22 IRQ_TYPE_LEVEL_HIGH>;
			#mbox-cells = <2>;
			status = "disabled";
		};

		elemu2: mailbox@47540000 {
			compatible = "fsl,imx95-mu-ele";
			reg = <0x0 0x47540000 0x0 0x10000>;
			interrupts = <GIC_SPI 23 IRQ_TYPE_LEVEL_HIGH>;
			#mbox-cells = <2>;
			status = "disabled";
		};

		elemu3: mailbox@47550000 {
			compatible = "fsl,imx95-mu-ele";
			reg = <0x0 0x47550000 0x0 0x10000>;
			interrupts = <GIC_SPI 24 IRQ_TYPE_LEVEL_HIGH>;
			#mbox-cells = <2>;
		};

		elemu4: mailbox@47560000 {
			compatible = "fsl,imx95-mu-ele";
			reg = <0x0 0x47560000 0x0 0x10000>;
			interrupts = <GIC_SPI 25 IRQ_TYPE_LEVEL_HIGH>;
			#mbox-cells = <2>;
			status = "disabled";
		};

		elemu5: mailbox@47570000 {
			compatible = "fsl,imx95-mu-ele";
			reg = <0x0 0x47570000 0x0 0x10000>;
			interrupts = <GIC_SPI 26 IRQ_TYPE_LEVEL_HIGH>;
			#mbox-cells = <2>;
			status = "disabled";
		};

		aips4: bus@49000000 {
			compatible = "fsl,aips-bus", "simple-bus";
			reg = <0x0 0x49000000 0x0 0x800000>;
			ranges = <0x49000000 0x0 0x49000000 0x800000>;
			#address-cells = <1>;
			#size-cells = <1>;

			smmu: iommu@490d0000 {
				compatible = "arm,smmu-v3";
				reg = <0x490d0000 0x100000>;
				interrupts = <GIC_SPI 325 IRQ_TYPE_EDGE_RISING>,
					     <GIC_SPI 328 IRQ_TYPE_EDGE_RISING>,
					     <GIC_SPI 334 IRQ_TYPE_EDGE_RISING>,
					     <GIC_SPI 326 IRQ_TYPE_EDGE_RISING>;
				interrupt-names = "eventq", "gerror", "priq", "cmdq-sync";
				#iommu-cells = <1>;
			};
		};

		cameramix_csr: syscon@4ac10000 {
			compatible = "nxp,imx95-camera-csr", "syscon";
			#address-cells = <1>;
			#size-cells = <1>;
			reg = <0x0 0x4ac10000 0x0 0x10000>;
			#clock-cells = <1>;
			clocks = <&scmi_clk IMX95_CLK_CAMAPB>;
			power-domains = <&scmi_devpd IMX95_PD_CAMERA>;

			csi_pixel_formatter_0: formatter@20 {
				compatible = "fsl,imx95-csi-formatter";
				reg = <0x20 0x100>;
				clocks = <&cameramix_csr IMX95_CLK_CAMBLK_CSI2_FOR0>;
				power-domains = <&scmi_devpd IMX95_PD_CAMERA>;
				status = "disabled";

				ports {
					#address-cells = <1>;
					#size-cells = <0>;

					port@0 {
						reg = <0>;
					};

					port@1 {
						reg = <1>;
					};
				};
			};

			csi_pixel_formatter_1: formatter@120 {
				compatible = "fsl,imx95-csi-formatter";
				reg = <0x120 0x100>;
				clocks = <&cameramix_csr IMX95_CLK_CAMBLK_CSI2_FOR1>;
				power-domains = <&scmi_devpd IMX95_PD_CAMERA>;
				status = "disabled";

				ports {
					#address-cells = <1>;
					#size-cells = <0>;

					port@0 {
						reg = <0>;
					};

					port@1 {
						reg = <1>;
					};
				};
			};
		};

		neoisp0: isp@4ae00000 {
			compatible = "nxp,imx95-a0-neoisp";
			#address-cells = <2>;
			#size-cells = <2>;
			reg = <0x0 0x4ae00000 0x0 0x8000>,
			      <0x0 0x4afe0000 0x0 0x10000>;
			reg-names = "registers", "stats";
			interrupts = <GIC_SPI 222 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-parent = <&gic>;
			clocks = <&cameramix_csr IMX95_CLK_CAMBLK_ISP_AXI>,
				 <&cameramix_csr IMX95_CLK_CAMBLK_ISP_PIXEL>,
				 <&cameramix_csr IMX95_CLK_CAMBLK_ISP>;
			clock-names = "camblk_isp_axi", "camblk_isp_pixel", "camblk_isp";
			power-domains = <&scmi_devpd IMX95_PD_CAMERA>;
			nxp,blk-ctrl = <&cameramix_csr>;
			status = "disabled";
		};

		cameramix_mu1: mailbox@4ac60000 {
			compatible = "fsl,imx95-mu-isp";
			reg = <0x0 0x4ac60000 0x0 0x10000>;
			interrupts = <GIC_SPI 220 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&scmi_clk IMX95_CLK_CAMAPB>;
			power-domains = <&scmi_devpd IMX95_PD_CAMERA>;
			status = "disabled";
		};

		cameramix_mu2: mailbox@4ac70000 {
			compatible = "fsl,imx95-mu-isp";
			reg = <0x0 0x4ac70000 0x0 0x10000>;
			interrupts = <GIC_SPI 336 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&scmi_clk IMX95_CLK_CAMAPB>;
			power-domains = <&scmi_devpd IMX95_PD_CAMERA>;
			status = "disabled";
		};

		cameramix_mu3: mailbox@4ac80000 {
			compatible = "fsl,imx95-mu-isp";
			reg = <0x0 0x4ac80000 0x0 0x10000>;
			interrupts = <GIC_SPI 337 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&scmi_clk IMX95_CLK_CAMAPB>;
			power-domains = <&scmi_devpd IMX95_PD_CAMERA>;
			status = "disabled";
		};

		cameramix_mu4: mailbox@4ac90000 {
			compatible = "fsl,imx95-mu-isp";
			reg = <0x0 0x4ac90000 0x0 0x10000>;
			interrupts = <GIC_SPI 338 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&scmi_clk IMX95_CLK_CAMAPB>;
			power-domains = <&scmi_devpd IMX95_PD_CAMERA>;
			status = "disabled";
		};

		cameramix_mu5: mailbox@4aca0000 {
			compatible = "fsl,imx95-mu-isp";
			reg = <0x0 0x4aca0000 0x0 0x10000>;
			interrupts = <GIC_SPI 339 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&scmi_clk IMX95_CLK_CAMAPB>;
			power-domains = <&scmi_devpd IMX95_PD_CAMERA>;
			status = "disabled";
		};

		cameramix_mu6: mailbox@4acb0000 {
			compatible = "fsl,imx95-mu-isp";
			reg = <0x0 0x4acb0000 0x0 0x10000>;
			interrupts = <GIC_SPI 340 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&scmi_clk IMX95_CLK_CAMAPB>;
			power-domains = <&scmi_devpd IMX95_PD_CAMERA>;
			status = "disabled";
		};

		cameramix_mu7: mailbox@4acc0000 {
			compatible = "fsl,imx95-mu-isp";
			reg = <0x0 0x4acc0000 0x0 0x10000>;
			interrupts = <GIC_SPI 341 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&scmi_clk IMX95_CLK_CAMAPB>;
			power-domains = <&scmi_devpd IMX95_PD_CAMERA>;
			status = "disabled";
		};

		cameramix_mu8: mailbox@4acd0000 {
			compatible = "fsl,imx95-mu-isp";
			reg = <0x0 0x4acd0000 0x0 0x10000>;
			interrupts = <GIC_SPI 342 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&scmi_clk IMX95_CLK_CAMAPB>;
			power-domains = <&scmi_devpd IMX95_PD_CAMERA>;
			status = "disabled";
		};

		cameramix_mu9: mailbox@4ace0000 {
			compatible = "fsl,imx95-mu-isp";
			reg = <0x0 0x4ace0000 0x0 0x10000>;
			interrupts = <GIC_SPI 343 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&scmi_clk IMX95_CLK_CAMAPB>;
			power-domains = <&scmi_devpd IMX95_PD_CAMERA>;
			status = "disabled";
		};

		ocram_c_config: syscon@4add0000 {
			compatible = "fsl,imx95-ocram-config", "syscon";
			#address-cells = <1>;
			#size-cells = <1>;
			reg = <0x0 0x4add0000 0x0 0x10000>;
			clocks = <&scmi_clk IMX95_CLK_CAMAPB>;
			power-domains = <&scmi_devpd IMX95_PD_CAMERA>;

			interrupt-parent = <&gic>;
			interrupts =
				<GIC_SPI 251 IRQ_TYPE_LEVEL_HIGH>,
				<GIC_SPI 252 IRQ_TYPE_LEVEL_HIGH>,
				<GIC_SPI 253 IRQ_TYPE_LEVEL_HIGH>;
		};

		ocram_c: ocram@4af80000 {
			reg = <0x0 0x4af80000 0x0 0x18000>;
			no-map;
		};

		isp_fw_core: remoteproc {
			compatible = "nxp,imx95-isp-rproc";
			nxp,blk-ctrl = <&cameramix_csr>;
			nxp,ocram-cfg = <&ocram_c_config>;
			nxp,isp-firmware = "imx9xm0p_isp_fw.bin";
			power-domains = <&scmi_devpd IMX95_PD_CAMERA>;
			status = "disabled";

			mba {
				memory-region = <&ocram_c>;
				ocram-map = <0x0 0x14000>;
			};
		};

		mipi_dsi: dsi@4acf0000 {
			compatible = "nxp,imx95-mipi-dsi";
			reg = <0x0 0x4acf0000 0x0 0x10000>;
			interrupt-parent = <&displaymix_irqsteer>;
			interrupts = <48>;
			clocks = <&scmi_clk IMX95_CLK_MIPIPHYCFG>,
				 <&scmi_clk IMX95_CLK_MIPIPHYPLLBYPASS>,
				 <&scmi_clk IMX95_CLK_MIPIPHYPLLREF>,
				 <&scmi_clk IMX95_CLK_CAMAPB>,
				 <&scmi_clk IMX95_CLK_DISP1PIX>;
			clock-names = "cfg", "bypass", "ref", "pclk", "pix";
			assigned-clocks = <&scmi_clk IMX95_CLK_MIPIPHYCFG>,
					  <&scmi_clk IMX95_CLK_MIPIPHYPLLBYPASS>,
					  <&scmi_clk IMX95_CLK_MIPIPHYPLLREF>;
			assigned-clock-parents = <&scmi_clk IMX95_CLK_24M>,
						 <&scmi_clk IMX95_CLK_VIDEOPLL1>,
						 <&scmi_clk IMX95_CLK_24M>;
			mux-controls = <&mux 0>;
			power-domains = <&scmi_devpd IMX95_PD_CAMERA>;
			nxp,disp-master-csr = <&display_master_csr>;
			nxp,disp-stream-csr = <&display_stream_csr>;
			nxp,mipi-combo-phy-csr = <&mipi_tx_phy_csr>;
			status = "disabled";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				port@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;

					mipi_dsi_to_display_pixel_link0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&display_pixel_link0_to_mipi_dsi>;
					};

					mipi_dsi_to_display_pixel_link1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&display_pixel_link1_to_mipi_dsi>;
					};
				};

				port@1 {
					reg = <1>;
				};
			};
		};

		display_stream_csr: syscon@4ad00000 {
			compatible = "nxp,imx95-display-stream-csr", "syscon", "simple-mfd";
			reg = <0x0 0x4ad00000 0x0 0x10000>;
			clocks = <&scmi_clk IMX95_CLK_CAMAPB>;
		};

		display_master_csr: syscon@4ad10000 {
			compatible = "nxp,imx95-display-master-csr", "syscon";
			reg = <0x0 0x4ad10000 0x0 0x10000>;
			#clock-cells = <1>;
			clocks = <&scmi_clk IMX95_CLK_CAMAPB>;
			power-domains = <&scmi_devpd IMX95_PD_CAMERA>;

			mux: mux-controller {
				compatible = "mmio-mux";
				#mux-control-cells = <1>;
				mux-reg-masks = <0x4 0x00000001>; /* Pixel_link_sel */
				idle-states = <0>;
			};
		};

		mipi_dsi_intf: syscon@4acf0000 {
			compatible = "syscon", "simple-mfd";
			reg = <0x0 0x4acf0000 0x0 0x10000>;
			clocks = <&scmi_clk IMX95_CLK_CAMAPB>;
			status = "disabled";
		};

		combo_phy_csr: syscon@4ad20000 {
			compatible = "syscon", "simple-mfd";
			reg = <0x0 0x4ad20000 0x0 0x114>;
			clocks = <&scmi_clk IMX95_CLK_CAMAPB>;

			dphy_rx: dphy-rx {
				compatible = "fsl,imx95-dphy-rx";
				clocks = <&scmi_clk IMX95_CLK_MIPIPHYCFG>;
				clock-names = "phy_cfg";
				assigned-clocks = <&scmi_clk IMX95_CLK_MIPIPHYCFG>;
				assigned-clock-parents = <&scmi_clk IMX95_CLK_24M>;
				assigned-clock-rate = <24000000>;
				#phy-cells = <0>;
				fsl,csis = <&mipi_csi0>;
				power-domains = <&scmi_devpd IMX95_PD_CAMERA>;
				status = "disabled";
			};

			combo_rx: combo-rx {
				compatible = "fsl,imx95-combo-rx";
				clocks = <&scmi_clk IMX95_CLK_MIPIPHYCFG>;
				clock-names = "phy_cfg";
				assigned-clocks = <&scmi_clk IMX95_CLK_MIPIPHYCFG>;
				assigned-clock-parents = <&scmi_clk IMX95_CLK_24M>;
				assigned-clock-rate = <24000000>;
				#phy-cells = <0>;
				fsl,reg-offset = <0x100>;
				fsl,csis = <&mipi_csi1>;
				fsl,dsi = <&mipi_dsi_intf>;
				power-domains = <&scmi_devpd IMX95_PD_CAMERA>;
				status = "disabled";
			};

		};

		mipi_tx_phy_csr: syscon@4ad20100 {
			compatible = "fsl,imx95-mipi-tx-phy-csr", "syscon";
			reg = <0x0 0x4ad20100 0x0 0x14>;
			clocks = <&scmi_clk IMX95_CLK_CAMAPB>;
		};

		mipi_csi0: csi@4ad30000 {
			compatible = "snps,dw-mipi-csi2", "syscon";
			reg = <0x0 0x4ad30000 0x0 0x10000>;
			interrupts = <GIC_SPI 374 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cameramix_csr IMX95_CLK_CAMBLK_CSI2_FOR0>,
				 <&scmi_clk IMX95_CLK_CAMAPB>;
			clock-names = "per", "pixel";
			phys = <&dphy_rx>;
			phy-names = "dphy-rx";
			power-domains = <&scmi_devpd IMX95_PD_CAMERA>;
			status = "disabled";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				port@0 {
					reg = <0>;
				};

				port@1 {
					reg = <1>;
				};
			};
		};

		mipi_csi1: csi@4ad40000 {
			compatible = "snps,dw-mipi-csi2", "syscon";
			reg = <0x0 0x4ad40000 0x0 0x10000>;
			interrupts = <GIC_SPI 375 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&cameramix_csr IMX95_CLK_CAMBLK_CSI2_FOR1>,
				 <&scmi_clk IMX95_CLK_CAMAPB>;
			clock-names = "per", "pixel";
			phys = <&combo_rx>;
			phy-names = "dphy-rx";
			power-domains = <&scmi_devpd IMX95_PD_CAMERA>;
			status = "disabled";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				port@0 {
					reg = <0>;
				};

				port@1 {
					reg = <1>;
				};
			};
		};

		isi: isi@4ad50000 {
			compatible = "fsl,imx95-isi";
			reg = <0x0 0x4ad50000 0x0 0x80000>;
			interrupts = <GIC_SPI 221 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 344 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 345 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 346 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 347 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 348 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 349 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 350 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&scmi_clk IMX95_CLK_CAMISI>;
			clock-names = "axi";
			fsl,blk-ctrl = <&cameramix_csr>;
			power-domains = <&scmi_devpd IMX95_PD_CAMERA>;
			status = "disabled";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				port@0 {
					reg = <0>;
				};

				port@1 {
					reg = <1>;
				};

				port@2 {
					reg = <2>;
				};

				port@3 {
					reg = <3>;
				};
			};
		};

		dispmix_csr: syscon@4b010000 {
			compatible = "nxp,imx95-display-csr", "syscon";
			#address-cells = <1>;
			#size-cells = <1>;
			reg = <0x0 0x4b010000 0x0 0x10000>;
			#clock-cells = <1>;
			clocks = <&scmi_clk IMX95_CLK_DISPAPB>;
			power-domains = <&scmi_devpd IMX95_PD_DISPLAY>;
			assigned-clocks = <&scmi_clk IMX95_CLK_DISPAXI>,
					  <&scmi_clk IMX95_CLK_DISPOCRAM>,
					  <&scmi_clk IMX95_CLK_DISPAPB>;
			assigned-clock-parents = <&scmi_clk IMX95_CLK_SYSPLL1_PFD1>,
						 <&scmi_clk IMX95_CLK_SYSPLL1_PFD1>,
						 <&scmi_clk IMX95_CLK_SYSPLL1_PFD1_DIV2>;
			assigned-clock-rates = <400000000>, <400000000>, <133333333>;

			display_pixel_link: bridge@8 {
				compatible = "nxp,imx95-dc-pixel-link";
				reg = <0x8 0x4>;
				status = "disabled";

				ports {
					#address-cells = <1>;
					#size-cells = <0>;

					port@0 {
						reg = <0>;

						display_pixel_link0_to_pixel_interleaver_disp0: endpoint {
							remote-endpoint = <&pixel_interleaver_disp0_to_display_pixel_link0>;
						};
					};

					port@1 {
						reg = <1>;

						display_pixel_link1_to_pixel_interleaver_disp1: endpoint {
							remote-endpoint = <&pixel_interleaver_disp1_to_display_pixel_link1>;
						};
					};

					port@2 {
						#address-cells = <1>;
						#size-cells = <0>;
						reg = <2>;

						display_pixel_link0_to_mipi_dsi: endpoint@0 {
							reg = <0>;
							remote-endpoint = <&mipi_dsi_to_display_pixel_link0>;
						};

						display_pixel_link0_to_ldb_ch0: endpoint@1 {
							reg = <1>;
							remote-endpoint = <&ldb_ch0_to_display_pixel_link0>;
						};
					};

					port@3 {
						#address-cells = <1>;
						#size-cells = <0>;
						reg = <3>;

						display_pixel_link1_to_mipi_dsi: endpoint@0 {
							reg = <0>;
							remote-endpoint = <&mipi_dsi_to_display_pixel_link1>;
						};

						display_pixel_link1_to_ldb_ch1: endpoint@1 {
							reg = <1>;
							remote-endpoint = <&ldb_ch1_to_display_pixel_link1>;
						};
					};
				};
			};
		};

		displaymix_irqsteer: interrupt-controller@4b0b0000 {
			compatible = "fsl,imx-irqsteer";
			reg = <0x0 0x4b0b0000 0x0 0x1000>;
			interrupts = <GIC_SPI 214 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 215 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 216 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 217 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 218 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 303 IRQ_TYPE_LEVEL_HIGH>,	/* reserved */
				     <GIC_SPI 303 IRQ_TYPE_LEVEL_HIGH>,	/* reserved */
				     <GIC_SPI 219 IRQ_TYPE_LEVEL_HIGH>;
			power-domains = <&scmi_devpd IMX95_PD_DISPLAY>;
			clocks = <&scmi_clk IMX95_CLK_DISPAPB>;
			clock-names = "ipg";
			fsl,channel = <0>;
			fsl,num-irqs = <512>;
			interrupt-controller;
			#interrupt-cells = <1>;
			status = "disabled";
		};

		lvds_csr: syscon@4b0c0000 {
			compatible = "nxp,imx95-lvds-csr", "syscon";
			#address-cells = <1>;
			#size-cells = <1>;
			reg = <0x0 0x4b0c0000 0x0 0x10000>;
			#clock-cells = <1>;
			clocks = <&scmi_clk IMX95_CLK_DISPAPB>;
			power-domains = <&scmi_devpd IMX95_PD_DISPLAY>;

			ldb: ldb@4 {
				compatible = "fsl,imx95-ldb";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x4 0x4>;
				clocks = <&lvds_csr IMX95_CLK_DISPMIX_PIX_DI0_GATE>,
					 <&lvds_csr IMX95_CLK_DISPMIX_PIX_DI1_GATE>,
					 <&lvds_csr IMX95_CLK_DISPMIX_LVDS_CH0_GATE>,
					 <&lvds_csr IMX95_CLK_DISPMIX_LVDS_CH1_GATE>;
				clock-names = "ldb_di0", "ldb_di1", "ldb_ch0", "ldb_ch1";
				status = "disabled";

				channel@0 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <0>;
					phys = <&ldb0_phy1>;
					phy-names = "lvds_phy";
					status = "disabled";

					port@0 {
						reg = <0>;

						ldb_ch0_to_display_pixel_link0: endpoint {
							remote-endpoint = <&display_pixel_link0_to_ldb_ch0>;
						};
					};

					port@1 {
						reg = <1>;
					};
				};

				channel@1 {
					#address-cells = <1>;
					#size-cells = <0>;
					reg = <1>;
					phys = <&ldb1_phy1>;
					phy-names = "lvds_phy";
					status = "disabled";

					port@0 {
						reg = <0>;

						ldb_ch1_to_display_pixel_link1: endpoint {
							remote-endpoint = <&display_pixel_link1_to_ldb_ch1>;
						};
					};

					port@1 {
						reg = <1>;
					};
				};
			};

			ldb0_phy: phy@8 {
				compatible = "fsl,imx95-lvds0-phy";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x8 0x4>;
				gpr = <&lvds_csr>;
				clocks = <&scmi_clk IMX95_CLK_DISPAPB>;
				clock-names = "apb";
				power-domains = <&scmi_devpd IMX95_PD_DISPLAY>;
				status = "disabled";

				ldb0_phy1: port@0 {
					reg = <0>;
					#phy-cells = <0>;
				};
			};

			ldb1_phy: phy@c {
				compatible = "fsl,imx95-lvds1-phy";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0xc 0x4>;
				gpr = <&lvds_csr>;
				clocks = <&scmi_clk IMX95_CLK_DISPAPB>;
				clock-names = "apb";
				power-domains = <&scmi_devpd IMX95_PD_DISPLAY>;
				status = "disabled";

				ldb1_phy1: port@0 {
					reg = <0>;
					#phy-cells = <0>;
				};
			};
		};

		pixel_interleaver: bridge@4b0d0000 {
			compatible = "nxp,imx95-pixel-interleaver";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x0 0x4b0d0000 0x0 0x20000>;
			interrupt-parent = <&displaymix_irqsteer>;
			interrupts = <62>;
			clocks = <&scmi_clk IMX95_CLK_DISPAPB>;
			nxp,blk-ctrl = <&dispmix_csr>;
			status = "disabled";

			channel@0 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0>;
				status = "disabled";

				port@0 {
					reg = <0>;

					pixel_interleaver_disp0_to_dpu_disp0: endpoint {
						remote-endpoint = <&dpu_disp0_to_pixel_interleaver_disp0>;
					};
				};

				port@1 {
					reg = <1>;

					pixel_interleaver_disp0_to_display_pixel_link0: endpoint {
						remote-endpoint = <&display_pixel_link0_to_pixel_interleaver_disp0>;
					};
				};
			};

			channel@1 {
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <1>;
				status = "disabled";

				port@0 {
					reg = <0>;

					pixel_interleaver_disp1_to_dpu_disp1: endpoint {
						remote-endpoint = <&dpu_disp1_to_pixel_interleaver_disp1>;
					};
				};

				port@1 {
					reg = <1>;

					pixel_interleaver_disp1_to_display_pixel_link1: endpoint {
						remote-endpoint = <&display_pixel_link1_to_pixel_interleaver_disp1>;
					};
				};
			};
		};

		dpu: display-controller@4b400000 {
			compatible = "nxp,imx95-dpu";
			reg = <0x0 0x4b400000 0x0 0x400000>;
			interrupt-parent = <&displaymix_irqsteer>;
			interrupts = <448>, <449>, <450>,  <64>,
				      <65>,  <66>,  <67>,  <68>,
				      <69>, <192>, <193>, <194>,
				     <195>, <196>, <197>,  <70>,
				      <71>,  <72>,  <73>,  <74>,
				      <75>,  <76>,  <77>,  <78>,
				      <79>,  <80>,  <81>,  <82>,
				      <83>,  <84>,  <85>,  <86>,
				      <87>,  <88>,  <89>,  <90>,
				      <91>,  <92>, <198>, <199>,
				     <200>, <201>, <202>, <203>,
				     <204>, <205>, <206>, <207>,
				     <208>, <209>, <210>, <211>,
				     <212>, <451>,   <1>,   <2>,
				       <3>,   <4>,  <93>,  <94>,
				      <95>,  <96>,  <97>,  <98>,
				      <99>, <100>, <101>, <102>,
				     <103>, <104>, <105>, <106>,
				     <213>, <214>, <215>, <216>,
				     <217>, <218>, <219>, <220>,
				     <221>, <222>, <223>, <224>,
				     <225>, <226>;
			interrupt-names = "store9_shdload",
					  "store9_framecomplete",
					  "store9_seqcomplete",
					  "extdst0_shdload",
					  "extdst0_framecomplete",
					  "extdst0_seqcomplete",
					  "extdst4_shdload",
					  "extdst4_framecomplete",
					  "extdst4_seqcomplete",
					  "extdst1_shdload",
					  "extdst1_framecomplete",
					  "extdst1_seqcomplete",
					  "extdst5_shdload",
					  "extdst5_framecomplete",
					  "extdst5_seqcomplete",
					  "domainblend0_shdload",
					  "domainblend0_framecomplete",
					  "domainblend0_seqcomplete",
					  "disengcfg_shdload0",
					  "disengcfg_framecomplete0",
					  "disengcfg_seqcomplete0",
					  "framegen0_int0",
					  "framegen0_int1",
					  "framegen0_int2",
					  "framegen0_int3",
					  "sig0_shdload",
					  "sig0_valid",
					  "sig0_error",
					  "sig0_cluster_error",
					  "sig0_cluster_match",
					  "sig2_shdload",
					  "sig2_valid",
					  "sig2_error",
					  "sig2_cluster_error",
					  "sig2_cluster_match",
					  "idhash0_shdload",
					  "idhash0_valid",
					  "idhash0_window_error",
					  "domainblend1_shdload",
					  "domainblend1_framecomplete",
					  "domainblend1_seqcomplete",
					  "disengcfg_shdload1",
					  "disengcfg_framecomplete1",
					  "disengcfg_seqcomplete1",
					  "framegen1_int0",
					  "framegen1_int1",
					  "framegen1_int2",
					  "framegen1_int3",
					  "sig1_shdload",
					  "sig1_valid",
					  "sig1_error",
					  "sig1_cluster_error",
					  "sig1_cluster_match",
					  "cmdseq_error",
					  "comctrl_sw0",
					  "comctrl_sw1",
					  "comctrl_sw2",
					  "comctrl_sw3",
					  "framegen0_primsync_on",
					  "framegen0_primsync_off",
					  "framegen0_overflow0_on",
					  "framegen0_overflow0_off",
					  "framegen0_underrun0_on",
					  "framegen0_underrun0_off",
					  "framegen0_threshold0_rise",
					  "framegen0_threshold0_fail",
					  "framegen0_overflow1_on",
					  "framegen0_overflow1_off",
					  "framegen0_underrun1_on",
					  "framegen0_underrun1_off",
					  "framegen0_threshold1_rise",
					  "framegen0_threshold1_fail",
					  "framegen1_primsync_on",
					  "framegen1_primsync_off",
					  "framegen1_overflow0_on",
					  "framegen1_overflow0_off",
					  "framegen1_underrun0_on",
					  "framegen1_underrun0_off",
					  "framegen1_threshold0_rise",
					  "framegen1_threshold0_fail",
					  "framegen1_overflow1_on",
					  "framegen1_overflow1_off",
					  "framegen1_underrun1_on",
					  "framegen1_underrun1_off",
					  "framegen1_threshold1_rise",
					  "framegen1_threshold1_fail";
			clocks = <&scmi_clk IMX95_CLK_DISP1PIX>,
				 <&scmi_clk IMX95_CLK_DISPAPB>,
				 <&scmi_clk IMX95_CLK_DISPAXI>,
				 <&scmi_clk IMX95_CLK_DISPOCRAM>,
				 <&ldb_pll_pixel>,
				 <&scmi_clk IMX95_CLK_LDBPLL_VCO>;
			clock-names = "pix", "apb", "axi", "ocram", "ldb", "ldb_vco";
			power-domains = <&scmi_devpd IMX95_PD_DISPLAY>;
			nxp,blk-ctrl = <&dispmix_csr>;
			status = "disabled";

			ports {
				#address-cells = <1>;
				#size-cells = <0>;

				port@0 {
					reg = <0>;

					dpu_disp0_to_pixel_interleaver_disp0: endpoint {
						remote-endpoint = <&pixel_interleaver_disp0_to_dpu_disp0>;
					};
				};

				port@1 {
					reg = <1>;

					dpu_disp1_to_pixel_interleaver_disp1: endpoint {
						remote-endpoint = <&pixel_interleaver_disp1_to_dpu_disp1>;
					};
				};
			};
		};

		usb3_phy: phy@4c1f0040 {
			compatible = "fsl,imx95-usb-phy";
			reg = <0x0 0x4c1f0040 0x0 0x40>,
			      <0x0 0x4c1fc000 0x0 0x100>;
			clocks = <&scmi_clk IMX95_CLK_HSIO>;
			clock-names = "phy";
			#phy-cells = <0>;
			power-domains = <&scmi_devpd IMX95_PD_HSIO_TOP>;
			orientation-switch;
			status = "disabled";
		};

		usb3: usb@4c010010 {
			compatible = "fsl,imx95-dwc3", "fsl,imx8mp-dwc3";
			reg = <0x0 0x4c010010 0x0 0x04>,
			      <0x0 0x4c1f0000 0x0 0x20>;
			clocks = <&scmi_clk IMX95_CLK_HSIO>,
				 <&scmi_clk IMX95_CLK_32K>;
			clock-names = "hsio", "suspend";
			interrupts = <GIC_SPI 173 IRQ_TYPE_LEVEL_HIGH>;
			#address-cells = <2>;
			#size-cells = <2>;
			ranges;
			power-domains = <&scmi_devpd IMX95_PD_HSIO_TOP>;
			dma-ranges = <0x0 0x0 0x0 0x0 0x10 0x0>;
			status = "disabled";

			usb3_dwc3: usb@4c100000 {
				compatible = "snps,dwc3";
				reg = <0x0 0x4c100000 0x0 0x10000>;
				clocks = <&scmi_clk IMX95_CLK_HSIO>,
					 <&scmi_clk IMX95_CLK_24M>,
					 <&scmi_clk IMX95_CLK_32K>;
				clock-names = "bus_early", "ref", "suspend";
				interrupts = <GIC_SPI 175 IRQ_TYPE_LEVEL_HIGH>;
				phys = <&usb3_phy>, <&usb3_phy>;
				phy-names = "usb2-phy", "usb3-phy";
				snps,gfladj-refclk-lpm-sel-quirk;
				snps,parkmode-disable-ss-quirk;
				snps,tx-max-burst = /bits/ 8 <4>;
				snps,tx-thr-num-pkt = /bits/ 8 <1>;
				iommus = <&smmu 0xe>;
			};
		};

		usb2: usb@4c200000 {
			compatible = "fsl,imx95-usb", "fsl,imx7d-usb";
			reg = <0x0 0x4c200000 0x0 0x200>;
			interrupts = <GIC_SPI 176 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 174 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&scmi_clk IMX95_CLK_HSIO>,
				 <&scmi_clk IMX95_CLK_32K>;
			clock-names = "usb1_ctrl_root_clk", "usb_wakeup_clk";
			power-domains = <&scmi_devpd IMX95_PD_HSIO_TOP>;
			phys = <&usbphynop>;
			fsl,usbmisc = <&usbmisc 0>;
			iommus = <&smmu 0xf>;
			status = "disabled";
		};

		usbmisc: usbmisc@4c200200 {
			compatible = "fsl,imx95-usbmisc", "fsl,imx7d-usbmisc";
			#index-cells = <1>;
			reg = <0x0 0x4c200200 0x0 0x200>,
			      <0x0 0x4c010014 0x0 0x04>;
		};

		hsio_blk_ctl: syscon@4c0100c0 {
			compatible = "nxp,imx95-hsio-blk-ctl", "syscon";
			reg = <0x0 0x4c0100c0 0x0 0x1>;
			#clock-cells = <1>;
			power-domains = <&scmi_devpd IMX95_PD_HSIO_TOP>;
			clocks = <&dummy>;
		};

		pcie0: pcie@4c300000 {
			compatible = "fsl,imx95-pcie";
			reg = <0 0x4c300000 0 0x10000>,
			      <0 0x60100000 0 0xfe00000>,
			      <0 0x4c360000 0 0x10000>,
			      <0 0x4c340000 0 0x4000>;
			reg-names = "dbi", "config", "atu", "app";
			ranges = <0x81000000 0x0 0x00000000 0x0 0x6ff00000 0 0x00100000>,
				 <0x82000000 0x0 0x10000000 0x9 0x10000000 0 0x10000000>;
			#address-cells = <3>;
			#size-cells = <2>;
			device_type = "pci";
			linux,pci-domain = <0>;
			msi-map = <0x0 &its 0x10 0x1>,
				  <0x100 &its 0x11 0x7>;
			msi-map-mask = <0x1ff>; /* Suppose only 1 physic device connect */
			bus-range = <0x00 0xff>;
			num-lanes = <1>;
			num-viewport = <8>;
			interrupts = <GIC_SPI 310 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "msi";
			#interrupt-cells = <1>;
			interrupt-map-mask = <0 0 0 0x7>;
			interrupt-map = <0 0 0 1 &gic 0 0 GIC_SPI 306 IRQ_TYPE_LEVEL_HIGH>,
					<0 0 0 2 &gic 0 0 GIC_SPI 307 IRQ_TYPE_LEVEL_HIGH>,
					<0 0 0 3 &gic 0 0 GIC_SPI 308 IRQ_TYPE_LEVEL_HIGH>,
					<0 0 0 4 &gic 0 0 GIC_SPI 309 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&scmi_clk IMX95_CLK_HSIO>,
				 <&scmi_clk IMX95_CLK_HSIOPLL>,
				 <&scmi_clk IMX95_CLK_HSIOPLL_VCO>,
				 <&scmi_clk IMX95_CLK_HSIOPCIEAUX>,
				 <&hsio_blk_ctl 0>;
			clock-names = "pcie", "pcie_bus", "pcie_phy", "pcie_aux", "ref";
			assigned-clocks =<&scmi_clk IMX95_CLK_HSIOPLL_VCO>,
					 <&scmi_clk IMX95_CLK_HSIOPLL>,
					 <&scmi_clk IMX95_CLK_HSIOPCIEAUX>;
			assigned-clock-rates = <3600000000>, <100000000>, <10000000>;
			assigned-clock-parents = <0>, <0>,
						 <&scmi_clk IMX95_CLK_SYSPLL1_PFD1_DIV2>;
			power-domains = <&scmi_devpd IMX95_PD_HSIO_TOP>;
			fsl,max-link-speed = <3>;
			/* 0x10~0x17 stream id for pci0 */
			iommu-map = <0x000 &smmu 0x10 0x1>,
				    <0x100 &smmu 0x11 0x7>;
			iommu-map-mask = <0x1ff>;
			status = "disabled";
		};

		pcie0_ep: pcie-ep@4c300000 {
			compatible = "fsl,imx95-pcie-ep";
			reg = <0 0x4c300000 0 0x10000>,
			      <0 0x4c360000 0 0x1000>,
			      <0 0x4c320000 0 0x1000>,
			      <0 0x4c340000 0 0x4000>,
			      <0 0x4c370000 0 0x10000>,
			      <0x9 0 1 0>;
			reg-names = "dbi","atu", "dbi2", "app", "dma", "addr_space";
			num-lanes = <1>;
			interrupts = <GIC_SPI 317 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "dma";
			clocks = <&scmi_clk IMX95_CLK_HSIO>,
				 <&scmi_clk IMX95_CLK_HSIOPLL>,
				 <&scmi_clk IMX95_CLK_HSIOPLL_VCO>,
				 <&scmi_clk IMX95_CLK_HSIOPCIEAUX>,
				 <&hsio_blk_ctl 0>;
			clock-names = "pcie", "pcie_bus", "pcie_phy", "pcie_aux", "ref";
			assigned-clocks =<&scmi_clk IMX95_CLK_HSIOPLL_VCO>,
					 <&scmi_clk IMX95_CLK_HSIOPLL>,
					 <&scmi_clk IMX95_CLK_HSIOPCIEAUX>;
			assigned-clock-rates = <3600000000>, <100000000>, <10000000>;
			assigned-clock-parents = <0>, <0>,
						 <&scmi_clk IMX95_CLK_SYSPLL1_PFD1_DIV2>;
			power-domains = <&scmi_devpd IMX95_PD_HSIO_TOP>;
			fsl,max-link-speed = <3>;
			status = "disabled";
		};

		pcie1: pcie@4c380000 {
			compatible = "fsl,imx95-pcie";
			reg = <0 0x4c380000 0 0x10000>,
			      <8 0x80100000 0 0xfe00000>,
			      <0 0x4c3e0000 0 0x10000>,
			      <0 0x4c3c0000 0 0x4000>;
			reg-names = "dbi", "config", "atu", "app";
			ranges = <0x81000000 0 0x00000000 0x8 0x8ff00000 0 0x00100000>,
				 <0x82000000 0 0x10000000 0xa 0x10000000 0 0x10000000>;
			#address-cells = <3>;
			#size-cells = <2>;
			device_type = "pci";
			linux,pci-domain = <1>;
			bus-range = <0x00 0xff>;
			num-lanes = <1>;
			num-viewport = <8>;
			interrupts = <GIC_SPI 316 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "msi";
			#interrupt-cells = <1>;
			interrupt-map-mask = <0 0 0 0x7>;
			interrupt-map = <0 0 0 1 &gic 0 0 GIC_SPI 312 IRQ_TYPE_LEVEL_HIGH>,
					<0 0 0 2 &gic 0 0 GIC_SPI 313 IRQ_TYPE_LEVEL_HIGH>,
					<0 0 0 3 &gic 0 0 GIC_SPI 314 IRQ_TYPE_LEVEL_HIGH>,
					<0 0 0 4 &gic 0 0 GIC_SPI 315 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&scmi_clk IMX95_CLK_HSIO>,
				 <&scmi_clk IMX95_CLK_HSIOPLL>,
				 <&scmi_clk IMX95_CLK_HSIOPLL_VCO>,
				 <&scmi_clk IMX95_CLK_HSIOPCIEAUX>,
				 <&hsio_blk_ctl 0>;
			clock-names = "pcie", "pcie_bus", "pcie_phy", "pcie_aux", "ref";
			assigned-clocks =<&scmi_clk IMX95_CLK_HSIOPLL_VCO>,
					 <&scmi_clk IMX95_CLK_HSIOPLL>,
					 <&scmi_clk IMX95_CLK_HSIOPCIEAUX>;
			assigned-clock-rates = <3600000000>, <100000000>, <10000000>;
			assigned-clock-parents = <0>, <0>,
						 <&scmi_clk IMX95_CLK_SYSPLL1_PFD1_DIV2>;
			power-domains = <&scmi_devpd IMX95_PD_HSIO_TOP>;
			fsl,max-link-speed = <3>;
			/* 0x18~0x1F stream id for pci1 */
			iommu-map = <0x000 &smmu 0x18 0x1>,
				    <0x100 &smmu 0x19 0x7>;
			iommu-map-mask = <0x1ff>;
			status = "disabled";
		};

		pcie1_ep: pcie-ep@4c380000 {
			compatible = "fsl,imx95-pcie-ep";
			reg = <0 0x4c380000 0 0x10000>,
			      <0 0x4c3e0000 0 0x1000>,
			      <0 0x4c3a0000 0 0x1000>,
			      <0 0x4c3c0000 0 0x4000>,
			      <0 0x4c3f0000 0 0x10000>,
			      <0xa 0 1 0>;
			reg-names = "dbi", "atu", "dbi2", "app", "dma", "addr_space";
			num-lanes = <1>;
			interrupts = <GIC_SPI 317 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "dma";
			clocks = <&scmi_clk IMX95_CLK_HSIO>,
				 <&scmi_clk IMX95_CLK_HSIOPLL>,
				 <&scmi_clk IMX95_CLK_HSIOPLL_VCO>,
				 <&scmi_clk IMX95_CLK_HSIOPCIEAUX>,
				 <&hsio_blk_ctl 0>;
			clock-names = "pcie", "pcie_bus", "pcie_phy", "pcie_aux", "ref";
			assigned-clocks =<&scmi_clk IMX95_CLK_HSIOPLL_VCO>,
					 <&scmi_clk IMX95_CLK_HSIOPLL>,
					 <&scmi_clk IMX95_CLK_HSIOPCIEAUX>;
			assigned-clock-rates = <3600000000>, <100000000>, <10000000>;
			assigned-clock-parents = <0>, <0>,
						 <&scmi_clk IMX95_CLK_SYSPLL1_PFD1_DIV2>;
			power-domains = <&scmi_devpd IMX95_PD_HSIO_TOP>;
			fsl,max-link-speed = <3>;
			status = "disabled";
		};

		netcmix_blk_ctrl: syscon@4c810000 {
			compatible = "nxp,imx95-netcmix-blk-ctrl", "syscon";
			reg = <0x0 0x4c810000 0x0 0x8>;
			#clock-cells = <1>;
			clocks = <&scmi_clk IMX95_CLK_BUSNETCMIX>;
			assigned-clocks = <&scmi_clk IMX95_CLK_BUSNETCMIX>;
			assigned-clock-parents = <&scmi_clk IMX95_CLK_SYSPLL1_PFD1_DIV2>;
			assigned-clock-rates = <133333333>;
			power-domains = <&scmi_devpd IMX95_PD_NETC>;
		};

		sai2: sai@4c880000 {
			compatible = "fsl,imx95-sai";
			reg = <0x0 0x4c880000 0x0 0x10000>;
			interrupts = <GIC_SPI 169 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&scmi_clk IMX95_CLK_BUSNETCMIX>, <&dummy>,
				 <&scmi_clk IMX95_CLK_SAI2>, <&dummy>,
				 <&dummy>;
			clock-names = "bus", "mclk0", "mclk1", "mclk2", "mclk3";
			power-domains = <&scmi_devpd IMX95_PD_NETC>;
			dmas = <&edma2 59 0 FSL_EDMA_RX>, <&edma2 58 0 0>;
			dma-names = "rx", "tx";
			status = "disabled";
		};

		ddr-pmu@4e090dc0 {
			compatible = "fsl,imx95-ddr-pmu", "fsl,imx93-ddr-pmu";
			reg = <0x0 0x4e090dc0 0x0 0x200>;
			interrupts = <GIC_SPI 91 IRQ_TYPE_LEVEL_HIGH>;
		};

		vpu_blk_ctrl: syscon@4c410000 {
			compatible = "nxp,imx95-vpu-csr", "syscon";
			reg = <0x0 0x4c410000 0x0 0x10000>;
			#clock-cells = <1>;
			clocks = <&scmi_clk IMX95_CLK_VPUAPB>;
			power-domains = <&scmi_devpd IMX95_PD_VPU>;
			assigned-clocks = <&scmi_clk IMX95_CLK_VPUAPB>,
					  <&scmi_clk IMX95_CLK_VPU>,
					  <&scmi_clk IMX95_CLK_VPUJPEG>;
			assigned-clock-parents = <&scmi_clk IMX95_CLK_SYSPLL1_PFD1_DIV2>,
						 <&scmi_clk IMX95_CLK_SYSPLL1_PFD2>,
						 <&scmi_clk IMX95_CLK_SYSPLL1_PFD0>;
			assigned-clock-rates = <133333333>, <667000000>, <500000000>;
		};

		mali: gpu@4d900000 {
			compatible = "arm,mali-valhall", "nxp,imx95-mali";
			reg = <0 0x4d900000 0 0x480000>;
			reg-names = "gpu_base";
			interrupts = <GIC_SPI 288 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 289 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 290 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "gpu", "job", "mmu";
			clocks = <&scmi_clk IMX95_CLK_GPU>;
			clock-names = "gpu";
			power-domains = <&scmi_devpd IMX95_PD_GPU>, <&scmi_perf IMX95_PERF_GPU>;
			power-domain-names = "gpumix", "gpuperf";
			operating-points-v2 = <&gpu_opp_table>;
			#cooling-cells = <2>;
			simple_power: power_model@0 {
			    compatible = "arm,mali-simple-power-model";
			    static-coefficient = <59073>;
			    dynamic-coefficient = <1013>;
			    reference_voltage = <920>;
			    ts = <20000 2000 (-20) 2>;
			    thermal-zone = "ana";
			};

			ipa_power:power_model@1 {
			    compatible = "arm,mali-tvax-power-model";
			    reference_voltage = <920>;
			};
		};

		vpu0: vpu@4c480000 {
			compatible = "fsl,cnm633c-vpu";
			reg = <0x0 0x4c480000 0x0 0x10000>;
			interrupts = <GIC_SPI 299 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&scmi_clk IMX95_CLK_VPU>,
				 <&vpu_blk_ctrl IMX95_CLK_VPUBLK_WAVE>;
			clock-names = "vpu", "vpublk_wave";
			power-domains = <&scmi_devpd IMX95_PD_VPU>;
			cnm,ctrl = <&vpuctrl>;
		};

		vpu1: vpu@4c490000 {
			compatible = "fsl,cnm633c-vpu";
			reg = <0x0 0x4c490000 0x0 0x10000>;
			interrupts = <GIC_SPI 300 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&scmi_clk IMX95_CLK_VPU>,
				 <&vpu_blk_ctrl IMX95_CLK_VPUBLK_WAVE>;
			clock-names = "vpu", "vpublk_wave";
			power-domains = <&scmi_devpd IMX95_PD_VPU>;
			cnm,ctrl = <&vpuctrl>;
			status = "disabled";
		};

		vpu2: vpu@4c4a0000 {
			compatible = "fsl,cnm633c-vpu";
			reg = <0x0 0x4c4a0000 0x0 0x10000>;
			interrupts = <GIC_SPI 301 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&scmi_clk IMX95_CLK_VPU>,
				 <&vpu_blk_ctrl IMX95_CLK_VPUBLK_WAVE>;
			clock-names = "vpu", "vpublk_wave";
			power-domains = <&scmi_devpd IMX95_PD_VPU>;
			cnm,ctrl = <&vpuctrl>;
			status = "disabled";
		};

		vpu3: vpu@4c4b0000 {
			compatible = "fsl,cnm633c-vpu";
			reg = <0x0 0x4c4b0000 0x0 0x10000>;
			interrupts = <GIC_SPI 302 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&scmi_clk IMX95_CLK_VPU>,
				 <&vpu_blk_ctrl IMX95_CLK_VPUBLK_WAVE>;
			clock-names = "vpu", "vpublk_wave";
			power-domains = <&scmi_devpd IMX95_PD_VPU>;
			cnm,ctrl = <&vpuctrl>;
			status = "disabled";
		};

		vpuctrl: vpu-ctrl@4c4c0000 {
			compatible = "fsl,cm633c-vpu-ctrl";
			reg = <0x0 0x4c4c0000 0x0 0x10000>;
			clocks = <&scmi_clk IMX95_CLK_VPU>,
				 <&vpu_blk_ctrl IMX95_CLK_VPUBLK_WAVE>;
			clock-names = "vpu", "vpublk_wave";
			power-domains = <&scmi_devpd IMX95_PD_VPU>, <&scmi_perf IMX95_PERF_VPU>;
			power-domain-names = "vpumix", "vpuperf";
			#cooling-cells = <2>;
		};

		jpegdec: jpegdec@4c500000 {
			compatible = "fsl,imx9-jpgdec";
			reg = <0x0 0x4C500000 0x0 0x00050000>;
			interrupts = <GIC_SPI 295 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 296 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 297 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 298 IRQ_TYPE_LEVEL_HIGH>;
			slot = <0>;
			clocks = <&scmi_clk IMX95_CLK_VPU>,
				 <&scmi_clk IMX95_CLK_VPUJPEG>,
				 <&vpu_blk_ctrl IMX95_CLK_VPUBLK_JPEG_DEC>;
			power-domains = <&scmi_devpd IMX95_PD_VPU>;
			power-domain-names = "pd_dec";
		};

		jpegenc: jpegenc@4c550000 {
			compatible = "fsl,imx9-jpgenc";
			reg = <0x0 0x4C550000 0x0 0x00050000>;
			interrupts = <GIC_SPI 291 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 292 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 293 IRQ_TYPE_LEVEL_HIGH>,
				     <GIC_SPI 294 IRQ_TYPE_LEVEL_HIGH>;
			slot = <0>;
			clocks = <&scmi_clk IMX95_CLK_VPU>,
				 <&scmi_clk IMX95_CLK_VPUJPEG>,
				 <&vpu_blk_ctrl IMX95_CLK_VPUBLK_JPEG_ENC>;
			power-domains = <&scmi_devpd IMX95_PD_VPU>;
			power-domain-names = "pd_enc";
		};

		netc_blk_ctrl: netc-blk-ctrl@4cde0000 {
			compatible = "nxp,imx95-netc-blk-ctrl";
			reg = <0x0 0x4cde0000 0x0 0x10000>,
			      <0x0 0x4cdf0000 0x0 0x10000>,
			      <0x0 0x4c81000c 0x0 0x18>;
			reg-names = "ierb", "prb", "netcmix";
			#address-cells = <2>;
			#size-cells = <2>;
			ranges;
			power-domains = <&scmi_devpd IMX95_PD_NETC>;
			assigned-clocks = <&scmi_clk IMX95_CLK_ENET>,
					  <&scmi_clk IMX95_CLK_ENETREF>;
			assigned-clock-parents = <&scmi_clk IMX95_CLK_SYSPLL1_PFD2>,
						 <&scmi_clk IMX95_CLK_SYSPLL1_PFD0>;
			assigned-clock-rates = <666666666>, <250000000>;
			clocks = <&scmi_clk IMX95_CLK_ENET>;
			clock-names = "ipg_clk";

			pcie_4ca00000: pcie@4ca00000 {
				compatible = "pci-host-ecam-generic";
				reg = <0x0 0x4ca00000 0x0 0x100000>;
				/* Must be 3. */
				#address-cells = <3>;
				/* Must be 2. */
				#size-cells = <2>;
				device_type = "pci";
				bus-range = <0x0 0x0>;
				msi-map = <0x0 &its 0x60 0x1>,	//ENETC0 PF
					  <0x10 &its 0x61 0x1>, //ENETC0 VF0
					  <0x20 &its 0x62 0x1>, //ENETC0 VF1
					  <0x40 &its 0x63 0x1>, //ENETC1 PF
					  <0x80 &its 0x64 0x1>, //ENETC2 PF
					  <0x90 &its 0x65 0x1>, //ENETC2 VF0
					  <0xa0 &its 0x66 0x1>, //ENETC2 VF1
					  <0xc0 &its 0x67 0x1>; //NETC Timer
					 /* ENETC0~2 and Timer BAR0 - non-prefetchable memory */
				ranges = <0x82000000 0x0 0x4cc00000  0x0 0x4cc00000  0x0 0xe0000
					 /* Timer BAR2 - prefetchable memory */
					 0xc2000000 0x0 0x4cd00000  0x0 0x4cd00000  0x0 0x10000
					 /* ENETC0~2: VF0-1 BAR0 - non-prefetchable memory */
					 0x82000000 0x0 0x4cd20000  0x0 0x4cd20000  0x0 0x60000
					 /* ENETC0~2: VF0-1 BAR2 - prefetchable memory */
					 0xc2000000 0x0 0x4cd80000  0x0 0x4cd80000  0x0 0x60000>;

				enetc_port0: ethernet@0,0 {
					compatible = "fsl,imx95-enetc";
					reg = <0x000000 0 0 0 0>;
					clocks = <&scmi_clk IMX95_CLK_ENETREF>;
					clock-names = "enet_ref_clk";
					nvmem-cells = <&eth_mac0>;
					nvmem-cell-names = "mac-address";
					status = "disabled";
				};

				enetc_port1: ethernet@8,0 {
					compatible = "fsl,imx95-enetc";
					reg = <0x004000 0 0 0 0>;
					clocks = <&scmi_clk IMX95_CLK_ENETREF>;
					clock-names = "enet_ref_clk";
					nvmem-cells = <&eth_mac1>;
					nvmem-cell-names = "mac-address";
					status = "disabled";
				};

				enetc_port2: ethernet@10,0 {
					compatible = "fsl,imx95-enetc";
					reg = <0x008000 0 0 0 0>;
					nvmem-cells = <&eth_mac2>;
					nvmem-cell-names = "mac-address";
					status = "disabled";
				};

				netc_timer: ethernet@18,0 {
					compatible = "fsl,imx95-netc-timer";
					reg = <0x00c000 0 0 0 0>;
					status = "disabled";
				};

				rcec@1,0 {
					compatible = "pci1131,e001";
					reg = <0x000800 0 0 0 0>;
					interrupts = <GIC_SPI 304 IRQ_TYPE_LEVEL_HIGH>;
				};
			};

			pcie_4cb00000: pcie@4cb00000 {
				compatible = "pci-host-ecam-generic";
				reg = <0x0 0x4cb00000 0x0 0x100000>;
				#address-cells = <3>;
				#size-cells = <2>;
				device_type = "pci";
				bus-range = <0x1 0x1>;
					 /* EMDIO BAR0 - non-prefetchable memory */
				ranges = <0x82000000 0x0 0x4cce0000  0x0 0x4cce0000  0x0 0x20000
					 /* EMDIO BAR2 - prefetchable memory */
					 0xc2000000 0x0 0x4cd10000  0x0 0x4cd10000  0x0 0x10000>;
				power-domains = <&scmi_devpd IMX95_PD_NETC>;

				netc_emdio: mdio@0,0 {
					compatible = "fsl,imx95-netc-emdio";
					reg = <0x010000 0 0 0 0>;
					#address-cells = <1>;
					#size-cells = <0>;
					status = "disabled";
				};
			};
		};

		neutron_core: imx95-neutron-remoteproc@4ab00000 {
			compatible = "fsl,imx95-neutron-rproc";
			reg = <0x0 0x4ab00000 0x0 0x4>;
			power-domains = <&scmi_devpd IMX95_PD_NPU>;
		};

		neutron: imx95-neutron@4ab00004 {
			compatible = "fsl,imx95-neutron";
			reg = <0x0 0x4ab00004 0x0 0x400>;
			fsl,neutron-rproc = <&neutron_core>;
			interrupts = <GIC_SPI 318 IRQ_TYPE_LEVEL_HIGH>;
			clocks = <&scmi_clk IMX95_CLK_NPU>,
				 <&scmi_clk IMX95_CLK_NPUAPB>;
			clock-names = "npu", "npu_apb";
			power-domains = <&scmi_devpd IMX95_PD_NPU>;
		};
	};

	rpmsg-lifecycle {
		compatible = "nxp,rpmsg-lifecycle";
	};

	ele_if0: secure-enclave-0 {
		 compatible = "fsl,imx95-se";
		 mbox-names = "tx", "rx";
		 mboxes = <&elemu3 0 0>, <&elemu3 1 0>;
	 };

	v2x_dbg: secure-enclave-1 {
		 compatible = "fsl,imx95-se";
		 mbox-names = "tx", "rx";
		 mboxes = <&v2x_mu 0 0>, <&v2x_mu 1 0>;
	 };

	v2x_sv0: secure-enclave-2 {
		 compatible = "fsl,imx95-se";
		 mbox-names = "tx", "rx";
		 mboxes = <&v2x_mu4 0 0>, <&v2x_mu4 1 0>;
	 };

	v2x_she: secure-enclave-3 {
		 compatible = "fsl,imx95-se";
		 mbox-names = "tx", "rx";
		 mboxes = <&v2x_mu6 0 0>, <&v2x_mu6 1 0>;
	 };
};
