// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright 2019 NXP
 */

#include <dt-bindings/clock/imx8mp-clock.h>
#include <dt-bindings/power/imx8mp-power.h>
#include <dt-bindings/reset/imx8mp-reset.h>
#include <dt-bindings/power/imx8mp-power.h>
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/thermal/thermal.h>

#include "imx8mp-pinfunc.h"

/ {
	interrupt-parent = <&gic>;
	#address-cells = <2>;
	#size-cells = <2>;

	aliases {
		ethernet0 = &fec;
		ethernet1 = &eqos;
		gpio0 = &gpio1;
		gpio1 = &gpio2;
		gpio2 = &gpio3;
		gpio3 = &gpio4;
		gpio4 = &gpio5;
		i2c0 = &i2c1;
		i2c1 = &i2c2;
		i2c2 = &i2c3;
		i2c3 = &i2c4;
		i2c4 = &i2c5;
		i2c5 = &i2c6;
		mmc0 = &usdhc1;
		mmc1 = &usdhc2;
		mmc2 = &usdhc3;
		serial0 = &uart1;
		serial1 = &uart2;
		serial2 = &uart3;
		serial3 = &uart4;
		spi0 = &flexspi;
		isi0 = &isi_0;
		isi1 = &isi_1;
		csi0 = &mipi_csi_0;
		csi1 = &mipi_csi_1;
		isp0 = &isp_0;
		isp1 = &isp_1;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		idle-states {
			entry-method = "psci";

			cpu_pd_wait: cpu-pd-wait {
				compatible = "arm,idle-state";
				arm,psci-suspend-param = <0x0010033>;
				local-timer-stop;
				entry-latency-us = <1000>;
				exit-latency-us = <700>;
				min-residency-us = <2700>;
				wakeup-latency-us = <1500>;
			};
		};

		A53_0: cpu@0 {
			device_type = "cpu";
			compatible = "arm,cortex-a53";
			reg = <0x0>;
			clock-latency = <61036>;
			clocks = <&clk IMX8MP_CLK_ARM>;
			enable-method = "psci";
			i-cache-size = <0x8000>;
			i-cache-line-size = <64>;
			i-cache-sets = <256>;
			d-cache-size = <0x8000>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			next-level-cache = <&A53_L2>;
			nvmem-cells = <&cpu_speed_grade>;
			nvmem-cell-names = "speed_grade";
			operating-points-v2 = <&a53_opp_table>;
			#cooling-cells = <2>;
			cpu-idle-states = <&cpu_pd_wait>;
		};

		A53_1: cpu@1 {
			device_type = "cpu";
			compatible = "arm,cortex-a53";
			reg = <0x1>;
			clock-latency = <61036>;
			clocks = <&clk IMX8MP_CLK_ARM>;
			enable-method = "psci";
			i-cache-size = <0x8000>;
			i-cache-line-size = <64>;
			i-cache-sets = <256>;
			d-cache-size = <0x8000>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			next-level-cache = <&A53_L2>;
			operating-points-v2 = <&a53_opp_table>;
			#cooling-cells = <2>;
			cpu-idle-states = <&cpu_pd_wait>;
		};

		A53_2: cpu@2 {
			device_type = "cpu";
			compatible = "arm,cortex-a53";
			reg = <0x2>;
			clock-latency = <61036>;
			clocks = <&clk IMX8MP_CLK_ARM>;
			enable-method = "psci";
			i-cache-size = <0x8000>;
			i-cache-line-size = <64>;
			i-cache-sets = <256>;
			d-cache-size = <0x8000>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			next-level-cache = <&A53_L2>;
			operating-points-v2 = <&a53_opp_table>;
			#cooling-cells = <2>;
			cpu-idle-states = <&cpu_pd_wait>;
		};

		A53_3: cpu@3 {
			device_type = "cpu";
			compatible = "arm,cortex-a53";
			reg = <0x3>;
			clock-latency = <61036>;
			clocks = <&clk IMX8MP_CLK_ARM>;
			enable-method = "psci";
			i-cache-size = <0x8000>;
			i-cache-line-size = <64>;
			i-cache-sets = <256>;
			d-cache-size = <0x8000>;
			d-cache-line-size = <64>;
			d-cache-sets = <128>;
			next-level-cache = <&A53_L2>;
			operating-points-v2 = <&a53_opp_table>;
			#cooling-cells = <2>;
			cpu-idle-states = <&cpu_pd_wait>;
		};

		A53_L2: l2-cache0 {
			compatible = "cache";
			cache-unified;
			cache-level = <2>;
			cache-size = <0x80000>;
			cache-line-size = <64>;
			cache-sets = <512>;
		};
	};

	display-subsystem {
		compatible = "fsl,imx-display-subsystem";
		ports = <&lcdif1_disp>,
			<&lcdif2_disp>,
			<&lcdif3_disp>;
	};

	a53_opp_table: opp-table {
		compatible = "operating-points-v2";
		opp-shared;

		opp-1200000000 {
			opp-hz = /bits/ 64 <1200000000>;
			opp-microvolt = <850000>;
			opp-supported-hw = <0x8a0>, <0x7>;
			clock-latency-ns = <150000>;
			opp-suspend;
		};

		opp-1600000000 {
			opp-hz = /bits/ 64 <1600000000>;
			opp-microvolt = <950000>;
			opp-supported-hw = <0xa0>, <0x7>;
			clock-latency-ns = <150000>;
			opp-suspend;
		};

		/* Only for Special Industrial part with 1.8G at OD */
		opp-sc-1800000000 {
			opp-hz = /bits/ 64 <1800000000>;
			opp-microvolt = <950000>;
			opp-supported-hw = <0x20>, <0x4>;
			clock-latency-ns = <150000>;
			opp-suspend;
		};

		opp-1800000000 {
			opp-hz = /bits/ 64 <1800000000>;
			opp-microvolt = <1000000>;
			opp-supported-hw = <0x20>, <0x3>;
			clock-latency-ns = <150000>;
			opp-suspend;
		};
	};

	osc_32k: clock-osc-32k {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <32768>;
		clock-output-names = "osc_32k";
	};

	osc_24m: clock-osc-24m {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <24000000>;
		clock-output-names = "osc_24m";
	};

	clk_ext1: clock-ext1 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <133000000>;
		clock-output-names = "clk_ext1";
	};

	clk_ext2: clock-ext2 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <133000000>;
		clock-output-names = "clk_ext2";
	};

	clk_ext3: clock-ext3 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <133000000>;
		clock-output-names = "clk_ext3";
	};

	clk_ext4: clock-ext4 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <133000000>;
		clock-output-names = "clk_ext4";
	};

	reserved-memory {
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;
/*
 *		Memory reserved for optee usage. Please do not use.
 *		This will be automaticky added to dtb if OP-TEE is installed.
 *		optee@56000000 {
 *   			reg = <0 0x56000000 0 0x2000000>;
 *			no-map;
 *		};
 */
		/* global autoconfigured region for contiguous allocations */
		linux,cma {
			compatible = "shared-dma-pool";
			reusable;
			size = <0 0x3c000000>;
			alloc-ranges = <0 0x40000000 0 0x40000000>;
			linux,cma-default;
		};

		gpu_reserved: gpu_reserved@b0000000 {
			no-map;
			reg = <0x0 0xb0000000 0 0x10000000>;
		};

		dsp_reserved: dsp@92400000 {
			reg = <0 0x92400000 0 0x1000000>;
			no-map;
		};
		dsp_reserved_heap: dsp_reserved_heap@93400000 {
			reg = <0 0x93400000 0 0xef0000>;
			no-map;
		};
		dsp_vdev0vring0: vdev0vring0@942f0000 {
			reg = <0 0x942f0000 0 0x8000>;
			no-map;
		};

		dsp_vdev0vring1: vdev0vring1@942f8000 {
			reg = <0 0x942f8000 0 0x8000>;
			no-map;
		};

		dsp_vdev0buffer: vdev0buffer@94300000 {
			compatible = "shared-dma-pool";
			reg = <0 0x94300000 0 0x100000>;
			no-map;
		};
	};

	sai1_mclk: sai1-mclk {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency= <0>;
		clock-output-names = "sai1_mclk";
	};

	sai2_mclk: sai2-mclk {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency= <0>;
		clock-output-names = "sai2_mclk";
	};

	sai3_mclk: sai3-mclk {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency= <0>;
		clock-output-names = "sai3_mclk";
	};

	sai5_mclk: sai5-mclk {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency= <0>;
		clock-output-names = "sai5_mclk";
	};

	sai6_mclk: sai6-mclk {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency= <0>;
		clock-output-names = "sai6_mclk";
	};

	sai7_mclk: sai7-mclk {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency= <0>;
		clock-output-names = "sai7_mclk";
	};

	busfreq { /* BUSFREQ */
		compatible = "fsl,imx_busfreq";
		clocks = <&clk IMX8MP_DRAM_PLL_OUT>, <&clk IMX8MP_CLK_DRAM_ALT>,
			 <&clk IMX8MP_CLK_DRAM_APB>, <&clk IMX8MP_CLK_DRAM_APB>,
			 <&clk IMX8MP_CLK_DRAM_CORE>, <&clk IMX8MP_CLK_DRAM_ALT_ROOT>,
			 <&clk IMX8MP_SYS_PLL1_40M>, <&clk IMX8MP_SYS_PLL1_100M>,
			 <&clk IMX8MP_SYS_PLL2_333M>, <&clk IMX8MP_CLK_NOC>,
			 <&clk IMX8MP_CLK_AHB>, <&clk IMX8MP_CLK_MAIN_AXI>,
			 <&clk IMX8MP_CLK_24M>, <&clk IMX8MP_SYS_PLL1_800M>,
			 <&clk IMX8MP_DRAM_PLL>;
		clock-names = "dram_pll", "dram_alt_src", "dram_apb_src", "dram_apb_pre_div",
			      "dram_core", "dram_alt_root", "sys_pll1_40m", "sys_pll1_100m",
			      "sys_pll2_333m", "noc_div", "ahb_div", "main_axi_src", "osc_24m",
			      "sys_pll1_800m", "dram_pll_div";
	};

	audiomix_pd: audiomix-pd {
		compatible = "fsl,imx8m-pm-domain";
		#power-domain-cells = <0>;
		domain-index = <5>;
		domain-name = "audiomix";
		clocks = <&clk IMX8MP_CLK_AUDIO_AHB_ROOT>,
			 <&clk IMX8MP_CLK_AUDIO_AXI_ROOT>;
	};

	pmu {
		compatible = "arm,cortex-a53-pmu";
		interrupts = <GIC_PPI 7
			     (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_HIGH)>;
	};

	psci {
		compatible = "arm,psci-1.0";
		method = "smc";
	};

	thermal-zones {
		cpu-thermal {
			polling-delay-passive = <250>;
			polling-delay = <2000>;
			thermal-sensors = <&tmu 0>;
			trips {
				cpu_alert0: trip0 {
					temperature = <85000>;
					hysteresis = <2000>;
					type = "passive";
				};

				cpu_crit0: trip1 {
					temperature = <95000>;
					hysteresis = <2000>;
					type = "critical";
				};
			};

			cooling-maps {
				map0 {
					trip = <&cpu_alert0>;
					cooling-device =
						<&A53_0 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&A53_1 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&A53_2 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&A53_3 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&mix_gpu_ml 0 1>;
				};
			};
		};

		soc-thermal {
			polling-delay-passive = <250>;
			polling-delay = <2000>;
			thermal-sensors = <&tmu 1>;
			trips {
				soc_alert0: trip0 {
					temperature = <85000>;
					hysteresis = <2000>;
					type = "passive";
				};

				soc_crit0: trip1 {
					temperature = <95000>;
					hysteresis = <2000>;
					type = "critical";
				};
			};

			cooling-maps {
				map0 {
					trip = <&soc_alert0>;
					cooling-device =
						<&A53_0 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&A53_1 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&A53_2 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>,
						<&A53_3 THERMAL_NO_LIMIT THERMAL_NO_LIMIT>;
				};
			};
		};
	};

	timer {
		compatible = "arm,armv8-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(4) | IRQ_TYPE_LEVEL_LOW)>;
		clock-frequency = <8000000>;
		arm,no-tick-in-suspend;
		interrupt-parent = <&gic>;
	};

	clk_dummy: clock-dummy {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <0>;
		clock-output-names = "clk_dummy";
	};

	irq_sec_vio: caam_secvio {
		compatible = "fsl,imx6q-caam-secvio";
		interrupts = <GIC_SPI 20 IRQ_TYPE_LEVEL_HIGH>;
		jtag-tamper = "disabled";
		watchdog-tamper = "enabled";
		internal-boot-tamper = "enabled";
		external-pin-tamper = "disabled";
	};

	soc: soc@0 {
		compatible = "simple-bus";
		#address-cells = <1>;
		#size-cells = <1>;
		ranges = <0x0 0x0 0x0 0x3e000000>;
		nvmem-cells = <&imx8mp_uid>;
		nvmem-cell-names = "soc_unique_id";

		caam_sm: caam-sm@100000 {
			compatible = "fsl,imx6q-caam-sm";
			reg = <0x100000 0x8000>;
		};

		aips1: bus@30000000 {
			compatible = "fsl,aips-bus", "simple-bus";
			reg = <0x30000000 0x400000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges;

			gpio1: gpio@30200000 {
				compatible = "fsl,imx8mp-gpio", "fsl,imx35-gpio";
				reg = <0x30200000 0x10000>;
				interrupts = <GIC_SPI 64 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 65 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_GPIO1_ROOT>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				gpio-ranges = <&iomuxc 0 5 30>;
			};

			gpio2: gpio@30210000 {
				compatible = "fsl,imx8mp-gpio", "fsl,imx35-gpio";
				reg = <0x30210000 0x10000>;
				interrupts = <GIC_SPI 66 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 67 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_GPIO2_ROOT>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				gpio-ranges = <&iomuxc 0 35 21>;
			};

			gpio3: gpio@30220000 {
				compatible = "fsl,imx8mp-gpio", "fsl,imx35-gpio";
				reg = <0x30220000 0x10000>;
				interrupts = <GIC_SPI 68 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 69 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_GPIO3_ROOT>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				gpio-ranges = <&iomuxc 0 56 26>, <&iomuxc 26 144 4>;
			};

			gpio4: gpio@30230000 {
				compatible = "fsl,imx8mp-gpio", "fsl,imx35-gpio";
				reg = <0x30230000 0x10000>;
				interrupts = <GIC_SPI 70 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 71 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_GPIO4_ROOT>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				gpio-ranges = <&iomuxc 0 82 32>;
			};

			gpio5: gpio@30240000 {
				compatible = "fsl,imx8mp-gpio", "fsl,imx35-gpio";
				reg = <0x30240000 0x10000>;
				interrupts = <GIC_SPI 72 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 73 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_GPIO5_ROOT>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				gpio-ranges = <&iomuxc 0 114 30>;
			};

			tmu: tmu@30260000 {
				compatible = "fsl,imx8mp-tmu";
				reg = <0x30260000 0x10000>;
				clocks = <&clk IMX8MP_CLK_TSENSOR_ROOT>;
				nvmem-cells = <&tmu_calib>;
				nvmem-cell-names = "calib";
				#thermal-sensor-cells = <1>;
			};

			wdog1: watchdog@30280000 {
				compatible = "fsl,imx8mp-wdt", "fsl,imx21-wdt";
				reg = <0x30280000 0x10000>;
				interrupts = <GIC_SPI 78 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_WDOG1_ROOT>;
				status = "disabled";
			};

			wdog2: watchdog@30290000 {
				compatible = "fsl,imx8mp-wdt", "fsl,imx21-wdt";
				reg = <0x30290000 0x10000>;
				interrupts = <GIC_SPI 79 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_WDOG2_ROOT>;
				status = "disabled";
			};

			wdog3: watchdog@302a0000 {
				compatible = "fsl,imx8mp-wdt", "fsl,imx21-wdt";
				reg = <0x302a0000 0x10000>;
				interrupts = <GIC_SPI 10 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_WDOG3_ROOT>;
				status = "disabled";
			};

			gpt1: timer@302d0000 {
				compatible = "fsl,imx8mp-gpt", "fsl,imx6dl-gpt";
				reg = <0x302d0000 0x10000>;
				interrupts = <GIC_SPI 55 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_GPT1_ROOT>, <&clk IMX8MP_CLK_GPT1>;
				clock-names = "ipg", "per";
			};

			gpt2: timer@302e0000 {
				compatible = "fsl,imx8mp-gpt", "fsl,imx6dl-gpt";
				reg = <0x302e0000 0x10000>;
				interrupts = <GIC_SPI 54 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_GPT2_ROOT>, <&clk IMX8MP_CLK_GPT2>;
				clock-names = "ipg", "per";
			};

			gpt3: timer@302f0000 {
				compatible = "fsl,imx8mp-gpt", "fsl,imx6dl-gpt";
				reg = <0x302f0000 0x10000>;
				interrupts = <GIC_SPI 53 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_GPT3_ROOT>, <&clk IMX8MP_CLK_GPT3>;
				clock-names = "ipg", "per";
			};

			iomuxc: pinctrl@30330000 {
				compatible = "fsl,imx8mp-iomuxc";
				reg = <0x30330000 0x10000>;
			};

			gpr: syscon@30340000 {
				compatible = "fsl,imx8mp-iomuxc-gpr", "syscon";
				reg = <0x30340000 0x10000>;
			};

			ocotp: efuse@30350000 {
				compatible = "fsl,imx8mp-ocotp", "fsl,imx8mm-ocotp", "syscon", "simple-mfd";
				reg = <0x30350000 0x10000>;
				clocks = <&clk IMX8MP_CLK_OCOTP_ROOT>;
				/* For nvmem subnodes */
				#address-cells = <1>;
				#size-cells = <1>;

				/*
				 * The register address below maps to the MX8M
				 * Fusemap Description Table entries this way.
				 * Assuming
				 *   reg = <ADDR SIZE>;
				 * then
				 *   Fuse Address = (ADDR * 4) + 0x400
				 * Note that if SIZE is greater than 4, then
				 * each subsequent fuse is located at offset
				 * +0x10 in Fusemap Description Table (e.g.
				 * reg = <0x8 0x8> describes fuses 0x420 and
				 * 0x430).
				 */
				imx8mp_uid: unique-id@8 { /* 0x420-0x430 */
					reg = <0x8 0x8>;
				};

				cpu_speed_grade: speed-grade@10 { /* 0x440 */
					reg = <0x10 4>;
				};

				eth_mac1: mac-address@90 { /* 0x640 */
					reg = <0x90 6>;
				};

				eth_mac2: mac-address@96 { /* 0x658 */
					reg = <0x96 6>;
				};

				tmu_calib: calib@264 { /* 0xd90-0xdc0 */
					reg = <0x264 0x10>;
				};

				imx8mp_soc: imx8mp-soc {
					compatible = "fsl,imx8mp-soc";
					nvmem-cells = <&imx8mp_uid>;
					nvmem-cell-names = "soc_unique_id";
				};
			};

			anatop: clock-controller@30360000 {
				compatible = "fsl,imx8mp-anatop", "fsl,imx8mm-anatop";
				reg = <0x30360000 0x10000>;
				#clock-cells = <1>;
			};

			caam_snvs: caam-snvs@30370000 {
				compatible = "fsl,imx6q-caam-snvs";
				reg = <0x30370000 0x10000>;
				clocks = <&clk IMX8MP_CLK_SNVS_ROOT>;
				clock-names = "ipg";
			};

			snvs: snvs@30370000 {
				compatible = "fsl,sec-v4.0-mon","syscon", "simple-mfd";
				reg = <0x30370000 0x10000>;

				snvs_rtc: snvs-rtc-lp {
					compatible = "fsl,sec-v4.0-mon-rtc-lp";
					regmap = <&snvs>;
					offset = <0x34>;
					interrupts = <GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>,
						     <GIC_SPI 20 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clk IMX8MP_CLK_SNVS_ROOT>;
					clock-names = "snvs-rtc";
				};

				snvs_pwrkey: snvs-powerkey {
					compatible = "fsl,sec-v4.0-pwrkey";
					regmap = <&snvs>;
					interrupts = <GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clk IMX8MP_CLK_SNVS_ROOT>;
					clock-names = "snvs-pwrkey";
					linux,keycode = <KEY_POWER>;
					wakeup-source;
					status = "disabled";
				};

				snvs_lpgpr: snvs-lpgpr {
					compatible = "fsl,imx8mp-snvs-lpgpr",
						     "fsl,imx7d-snvs-lpgpr";
				};
			};

			clk: clock-controller@30380000 {
				compatible = "fsl,imx8mp-ccm";
				reg = <0x30380000 0x10000>;
				interrupts = <GIC_SPI 85 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 86 IRQ_TYPE_LEVEL_HIGH>;
				#clock-cells = <1>;
				clocks = <&osc_32k>, <&osc_24m>, <&clk_ext1>, <&clk_ext2>,
					 <&clk_ext3>, <&clk_ext4>, <&sai1_mclk>, <&sai2_mclk>,
					 <&sai3_mclk>, <&sai5_mclk>, <&sai6_mclk>, <&sai7_mclk>;
				clock-names = "osc_32k", "osc_24m", "clk_ext1", "clk_ext2",
					      "clk_ext3", "clk_ext4", "sai1_mclk", "sai2_mclk",
					      "sai3_mclk", "sai5_mclk", "sai6_mclk", "sai7_mclk";
				assigned-clocks = <&clk IMX8MP_CLK_A53_SRC>,
						  <&clk IMX8MP_CLK_A53_CORE>,
						  <&clk IMX8MP_CLK_NOC>,
						  <&clk IMX8MP_CLK_NOC_IO>,
						  <&clk IMX8MP_CLK_GIC>;
				assigned-clock-parents = <&clk IMX8MP_SYS_PLL1_800M>,
							 <&clk IMX8MP_ARM_PLL_OUT>,
							 <&clk IMX8MP_SYS_PLL2_1000M>,
							 <&clk IMX8MP_SYS_PLL1_800M>,
							 <&clk IMX8MP_SYS_PLL2_500M>;
				assigned-clock-rates = <0>, <0>,
						       <1000000000>,
						       <800000000>,
						       <500000000>;
			};

			src: reset-controller@30390000 {
				compatible = "fsl,imx8mp-src", "syscon";
				reg = <0x30390000 0x10000>;
				interrupts = <GIC_SPI 89 IRQ_TYPE_LEVEL_HIGH>;
				#reset-cells = <1>;
			};

			gpc: gpc@303a0000 {
				compatible = "fsl,imx8mp-gpc";
				reg = <0x303a0000 0x1000>;
				interrupt-parent = <&gic>;
				interrupts = <GIC_SPI 87 IRQ_TYPE_LEVEL_HIGH>;
				interrupt-controller;
				#interrupt-cells = <3>;

				pgc {
					#address-cells = <1>;
					#size-cells = <0>;

					pgc_mipi_phy1: power-domain@0 {
						#power-domain-cells = <0>;
						reg = <IMX8MP_POWER_DOMAIN_MIPI_PHY1>;
					};

					pgc_pcie_phy: power-domain@1 {
						#power-domain-cells = <0>;
						reg = <IMX8MP_POWER_DOMAIN_PCIE_PHY>;
					};

					pgc_usb1_phy: power-domain@2 {
						#power-domain-cells = <0>;
						reg = <IMX8MP_POWER_DOMAIN_USB1_PHY>;
					};

					pgc_usb2_phy: power-domain@3 {
						#power-domain-cells = <0>;
						reg = <IMX8MP_POWER_DOMAIN_USB2_PHY>;
					};

					pgc_mlmix: power-domain@4 {
						#power-domain-cells = <0>;
						reg = <IMX8MP_POWER_DOMAIN_MLMIX>;
						clocks = <&clk IMX8MP_CLK_ML_AXI>,
							 <&clk IMX8MP_CLK_ML_AHB>,
							 <&clk IMX8MP_CLK_NPU_ROOT>;
						assigned-clocks = <&clk IMX8MP_CLK_ML_CORE>,
								  <&clk IMX8MP_CLK_ML_AXI>,
								  <&clk IMX8MP_CLK_ML_AHB>;
						assigned-clock-parents = <&clk IMX8MP_SYS_PLL1_800M>,
									 <&clk IMX8MP_SYS_PLL1_800M>,
									 <&clk IMX8MP_SYS_PLL1_800M>;
						assigned-clock-rates = <800000000>,
								       <800000000>,
								       <300000000>;
					};

					pgc_gpumix: power-domain@7 {
						#power-domain-cells = <0>;
						reg = <IMX8MP_POWER_DOMAIN_GPUMIX>;
						clocks = <&clk IMX8MP_CLK_GPU_ROOT>,
							 <&clk IMX8MP_CLK_GPU_AXI>,
							 <&clk IMX8MP_CLK_GPU_AHB>;
						assigned-clocks = <&clk IMX8MP_CLK_GPU_AXI>,
								  <&clk IMX8MP_CLK_GPU_AHB>;
						assigned-clock-parents = <&clk IMX8MP_SYS_PLL1_800M>,
									 <&clk IMX8MP_SYS_PLL1_800M>;
						assigned-clock-rates = <800000000>, <400000000>;
					};

					pgc_vpumix: power-domain@8 {
						#power-domain-cells = <0>;
						reg = <IMX8MP_POWER_DOMAIN_VPUMIX>;
						clocks = <&clk IMX8MP_CLK_VPU_ROOT>;
					};

					/*
					 * gpu driver not handle defer probe, but gpu2d pd will defer probe is
					 *  put above gpumix, so move below gpumix
					 */
					pgc_gpu2d: power-domain@6 {
						#power-domain-cells = <0>;
						reg = <IMX8MP_POWER_DOMAIN_GPU2D>;
						clocks = <&clk IMX8MP_CLK_GPU2D_ROOT>;
						power-domains = <&pgc_gpumix>;
					};

					pgc_gpu3d: power-domain@9 {
						#power-domain-cells = <0>;
						reg = <IMX8MP_POWER_DOMAIN_GPU3D>;
						clocks = <&clk IMX8MP_CLK_GPU3D_ROOT>,
							 <&clk IMX8MP_CLK_GPU3D_SHADER_CORE>;
						power-domains = <&pgc_gpumix>;
					};

					pgc_mediamix: power-domain@10 {
						#power-domain-cells = <0>;
						reg = <IMX8MP_POWER_DOMAIN_MEDIAMIX>;
						clocks = <&clk IMX8MP_CLK_MEDIA_AXI_ROOT>,
							 <&clk IMX8MP_CLK_MEDIA_APB_ROOT>;
					};

					pgc_vpu_g1: power-domain@11 {
						#power-domain-cells = <0>;
						power-domains = <&pgc_vpumix>;
						reg = <IMX8MP_POWER_DOMAIN_VPU_G1>;
						clocks = <&clk IMX8MP_CLK_VPU_G1_ROOT>;
					};

					pgc_vpu_g2: power-domain@12 {
						#power-domain-cells = <0>;
						power-domains = <&pgc_vpumix>;
						reg = <IMX8MP_POWER_DOMAIN_VPU_G2>;
						clocks = <&clk IMX8MP_CLK_VPU_G2_ROOT>;

					};

					pgc_vpu_vc8000e: power-domain@13 {
						#power-domain-cells = <0>;
						power-domains = <&pgc_vpumix>;
						reg = <IMX8MP_POWER_DOMAIN_VPU_VC8000E>;
						clocks = <&clk IMX8MP_CLK_VPU_VC8KE_ROOT>;
					};

					pgc_hdmimix: power-domain@14 {
						#power-domain-cells = <0>;
						reg = <IMX8MP_POWER_DOMAIN_HDMIMIX>;
						clocks = <&clk IMX8MP_CLK_HDMI_ROOT>,
							 <&clk IMX8MP_CLK_HDMI_APB>;
						assigned-clocks = <&clk IMX8MP_CLK_HDMI_AXI>,
								  <&clk IMX8MP_CLK_HDMI_APB>;
						assigned-clock-parents = <&clk IMX8MP_SYS_PLL2_500M>,
									 <&clk IMX8MP_SYS_PLL1_133M>;
						assigned-clock-rates = <500000000>, <133000000>;
					};

					pgc_hdmi_phy: power-domain@15 {
						#power-domain-cells = <0>;
						reg = <IMX8MP_POWER_DOMAIN_HDMI_PHY>;
					};

					pgc_mipi_phy2: power-domain@16 {
						#power-domain-cells = <0>;
						reg = <IMX8MP_POWER_DOMAIN_MIPI_PHY2>;
					};

					pgc_hsiomix: power-domain@17 {
						#power-domain-cells = <0>;
						reg = <IMX8MP_POWER_DOMAIN_HSIOMIX>;
						clocks = <&clk IMX8MP_CLK_HSIO_AXI>,
							 <&clk IMX8MP_CLK_HSIO_ROOT>;
						assigned-clocks = <&clk IMX8MP_CLK_HSIO_AXI>;
						assigned-clock-parents = <&clk IMX8MP_SYS_PLL2_500M>;
						assigned-clock-rates = <500000000>;
					};

					pgc_ispdwp: power-domain@18 {
						#power-domain-cells = <0>;
						reg = <IMX8MP_POWER_DOMAIN_MEDIAMIX_ISPDWP>;
						clocks = <&clk IMX8MP_CLK_MEDIA_ISP_ROOT>;
					};
				};
			};
		};

		aips2: bus@30400000 {
			compatible = "fsl,aips-bus", "simple-bus";
			reg = <0x30400000 0x400000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges;

			pwm1: pwm@30660000 {
				compatible = "fsl,imx8mp-pwm", "fsl,imx27-pwm";
				reg = <0x30660000 0x10000>;
				interrupts = <GIC_SPI 81 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_PWM1_ROOT>,
					 <&clk IMX8MP_CLK_PWM1_ROOT>;
				clock-names = "ipg", "per";
				#pwm-cells = <3>;
				status = "disabled";
			};

			pwm2: pwm@30670000 {
				compatible = "fsl,imx8mp-pwm", "fsl,imx27-pwm";
				reg = <0x30670000 0x10000>;
				interrupts = <GIC_SPI 82 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_PWM2_ROOT>,
					 <&clk IMX8MP_CLK_PWM2_ROOT>;
				clock-names = "ipg", "per";
				#pwm-cells = <3>;
				status = "disabled";
			};

			pwm3: pwm@30680000 {
				compatible = "fsl,imx8mp-pwm", "fsl,imx27-pwm";
				reg = <0x30680000 0x10000>;
				interrupts = <GIC_SPI 83 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_PWM3_ROOT>,
					 <&clk IMX8MP_CLK_PWM3_ROOT>;
				clock-names = "ipg", "per";
				#pwm-cells = <3>;
				status = "disabled";
			};

			pwm4: pwm@30690000 {
				compatible = "fsl,imx8mp-pwm", "fsl,imx27-pwm";
				reg = <0x30690000 0x10000>;
				interrupts = <GIC_SPI 84 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_PWM4_ROOT>,
					 <&clk IMX8MP_CLK_PWM4_ROOT>;
				clock-names = "ipg", "per";
				#pwm-cells = <3>;
				status = "disabled";
			};

			system_counter: timer@306a0000 {
				compatible = "nxp,sysctr-timer";
				reg = <0x306a0000 0x20000>;
				interrupts = <GIC_SPI 47 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&osc_24m>;
				clock-names = "per";
			};

			gpt6: timer@306e0000 {
				compatible = "fsl,imx8mp-gpt", "fsl,imx6dl-gpt";
				reg = <0x306e0000 0x10000>;
				interrupts = <GIC_SPI 51 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_GPT6_ROOT>, <&clk IMX8MP_CLK_GPT6>;
				clock-names = "ipg", "per";
			};

			gpt5: timer@306f0000 {
				compatible = "fsl,imx8mp-gpt", "fsl,imx6dl-gpt";
				reg = <0x306f0000 0x10000>;
				interrupts = <GIC_SPI 51 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_GPT5_ROOT>, <&clk IMX8MP_CLK_GPT5>;
				clock-names = "ipg", "per";
			};

			gpt4: timer@30700000 {
				compatible = "fsl,imx8mp-gpt", "fsl,imx6dl-gpt";
				reg = <0x30700000 0x10000>;
				interrupts = <GIC_SPI 52 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_GPT4_ROOT>, <&clk IMX8MP_CLK_GPT4>;
				clock-names = "ipg", "per";
			};
		};

		aips3: bus@30800000 {
			compatible = "fsl,aips-bus", "simple-bus";
			reg = <0x30800000 0x400000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges;

			spba-bus@30800000 {
				compatible = "fsl,spba-bus", "simple-bus";
				reg = <0x30800000 0x100000>;
				#address-cells = <1>;
				#size-cells = <1>;
				ranges;

				ecspi1: spi@30820000 {
					#address-cells = <1>;
					#size-cells = <0>;
					compatible = "fsl,imx8mp-ecspi", "fsl,imx6ul-ecspi";
					reg = <0x30820000 0x10000>;
					interrupts = <GIC_SPI 31 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clk IMX8MP_CLK_ECSPI1_ROOT>,
						 <&clk IMX8MP_CLK_ECSPI1_ROOT>;
					clock-names = "ipg", "per";
					assigned-clock-rates = <80000000>;
					assigned-clocks = <&clk IMX8MP_CLK_ECSPI1>;
					assigned-clock-parents = <&clk IMX8MP_SYS_PLL1_800M>;
					dmas = <&sdma1 0 7 1>, <&sdma1 1 7 2>;
					dma-names = "rx", "tx";
					status = "disabled";
				};

				ecspi2: spi@30830000 {
					#address-cells = <1>;
					#size-cells = <0>;
					compatible = "fsl,imx8mp-ecspi", "fsl,imx6ul-ecspi";
					reg = <0x30830000 0x10000>;
					interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clk IMX8MP_CLK_ECSPI2_ROOT>,
						 <&clk IMX8MP_CLK_ECSPI2_ROOT>;
					clock-names = "ipg", "per";
					assigned-clock-rates = <80000000>;
					assigned-clocks = <&clk IMX8MP_CLK_ECSPI2>;
					assigned-clock-parents = <&clk IMX8MP_SYS_PLL1_800M>;
					dmas = <&sdma1 2 7 1>, <&sdma1 3 7 2>;
					dma-names = "rx", "tx";
					status = "disabled";
				};

				ecspi3: spi@30840000 {
					#address-cells = <1>;
					#size-cells = <0>;
					compatible = "fsl,imx8mp-ecspi", "fsl,imx6ul-ecspi";
					reg = <0x30840000 0x10000>;
					interrupts = <GIC_SPI 33 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clk IMX8MP_CLK_ECSPI3_ROOT>,
						 <&clk IMX8MP_CLK_ECSPI3_ROOT>;
					clock-names = "ipg", "per";
					assigned-clock-rates = <80000000>;
					assigned-clocks = <&clk IMX8MP_CLK_ECSPI3>;
					assigned-clock-parents = <&clk IMX8MP_SYS_PLL1_800M>;
					dmas = <&sdma1 4 7 1>, <&sdma1 5 7 2>;
					dma-names = "rx", "tx";
					status = "disabled";
				};

				uart1: serial@30860000 {
					compatible = "fsl,imx8mp-uart", "fsl,imx6q-uart";
					reg = <0x30860000 0x10000>;
					interrupts = <GIC_SPI 26 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clk IMX8MP_CLK_UART1_ROOT>,
						 <&clk IMX8MP_CLK_UART1_ROOT>;
					clock-names = "ipg", "per";
					dmas = <&sdma1 22 4 0>, <&sdma1 23 4 0>;
					dma-names = "rx", "tx";
					status = "disabled";
				};

				uart3: serial@30880000 {
					compatible = "fsl,imx8mp-uart", "fsl,imx6q-uart";
					reg = <0x30880000 0x10000>;
					interrupts = <GIC_SPI 28 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clk IMX8MP_CLK_UART3_ROOT>,
						 <&clk IMX8MP_CLK_UART3_ROOT>;
					clock-names = "ipg", "per";
					dmas = <&sdma1 26 4 0>, <&sdma1 27 4 0>;
					dma-names = "rx", "tx";
					status = "disabled";
				};

				uart2: serial@30890000 {
					compatible = "fsl,imx8mp-uart", "fsl,imx6q-uart";
					reg = <0x30890000 0x10000>;
					interrupts = <GIC_SPI 27 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clk IMX8MP_CLK_UART2_ROOT>,
						 <&clk IMX8MP_CLK_UART2_ROOT>;
					clock-names = "ipg", "per";
					dmas = <&sdma1 24 4 0>, <&sdma1 25 4 0>;
					dma-names = "rx", "tx";
					status = "disabled";
				};

				flexcan1: can@308c0000 {
					compatible = "fsl,imx8mp-flexcan";
					reg = <0x308c0000 0x10000>;
					interrupts = <GIC_SPI 142 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clk IMX8MP_CLK_IPG_ROOT>,
						 <&clk IMX8MP_CLK_CAN1_ROOT>;
					clock-names = "ipg", "per";
					assigned-clocks = <&clk IMX8MP_CLK_CAN1>;
					assigned-clock-parents = <&clk IMX8MP_SYS_PLL1_40M>;
					assigned-clock-rates = <40000000>;
					fsl,clk-source = /bits/ 8 <0>;
					fsl,stop-mode = <&gpr 0x10 4>;
					status = "disabled";
				};

				flexcan2: can@308d0000 {
					compatible = "fsl,imx8mp-flexcan";
					reg = <0x308d0000 0x10000>;
					interrupts = <GIC_SPI 144 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clk IMX8MP_CLK_IPG_ROOT>,
						 <&clk IMX8MP_CLK_CAN2_ROOT>;
					clock-names = "ipg", "per";
					assigned-clocks = <&clk IMX8MP_CLK_CAN2>;
					assigned-clock-parents = <&clk IMX8MP_SYS_PLL1_40M>;
					assigned-clock-rates = <40000000>;
					fsl,clk-source = /bits/ 8 <0>;
					fsl,stop-mode = <&gpr 0x10 5>;
					status = "disabled";
				};
			};

			crypto: crypto@30900000 {
				compatible = "fsl,sec-v4.0";
				#address-cells = <1>;
				#size-cells = <1>;
				reg = <0x30900000 0x40000>;
				ranges = <0 0x30900000 0x40000>;
				interrupts = <GIC_SPI 91 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_AHB>,
					 <&clk IMX8MP_CLK_IPG_ROOT>;
				clock-names = "aclk", "ipg";

				sec_jr0: jr@1000 {
					compatible = "fsl,sec-v4.0-job-ring";
					reg = <0x1000 0x1000>;
					interrupts = <GIC_SPI 105 IRQ_TYPE_LEVEL_HIGH>;
					status = "disabled";
				};

				sec_jr1: jr@2000 {
					compatible = "fsl,sec-v4.0-job-ring";
					reg = <0x2000 0x1000>;
					interrupts = <GIC_SPI 106 IRQ_TYPE_LEVEL_HIGH>;
				};

				sec_jr2: jr@3000 {
					compatible = "fsl,sec-v4.0-job-ring";
					reg = <0x3000 0x1000>;
					interrupts = <GIC_SPI 114 IRQ_TYPE_LEVEL_HIGH>;
				};
			};

			i2c1: i2c@30a20000 {
				compatible = "fsl,imx8mp-i2c", "fsl,imx21-i2c";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x30a20000 0x10000>;
				interrupts = <GIC_SPI 35 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_I2C1_ROOT>;
				status = "disabled";
			};

			i2c2: i2c@30a30000 {
				compatible = "fsl,imx8mp-i2c", "fsl,imx21-i2c";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x30a30000 0x10000>;
				interrupts = <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_I2C2_ROOT>;
				status = "disabled";
			};

			i2c3: i2c@30a40000 {
				compatible = "fsl,imx8mp-i2c", "fsl,imx21-i2c";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x30a40000 0x10000>;
				interrupts = <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_I2C3_ROOT>;
				status = "disabled";
			};

			i2c4: i2c@30a50000 {
				compatible = "fsl,imx8mp-i2c", "fsl,imx21-i2c";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x30a50000 0x10000>;
				interrupts = <GIC_SPI 38 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_I2C4_ROOT>;
				status = "disabled";
			};

			uart4: serial@30a60000 {
				compatible = "fsl,imx8mp-uart", "fsl,imx6q-uart";
				reg = <0x30a60000 0x10000>;
				interrupts = <GIC_SPI 29 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_UART4_ROOT>,
					 <&clk IMX8MP_CLK_UART4_ROOT>;
				clock-names = "ipg", "per";
				dmas = <&sdma1 28 4 0>, <&sdma1 29 4 0>;
				dma-names = "rx", "tx";
				status = "disabled";
			};

			mu: mailbox@30aa0000 {
				compatible = "fsl,imx8mp-mu", "fsl,imx6sx-mu";
				reg = <0x30aa0000 0x10000>;
				interrupts = <GIC_SPI 88 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_MU_ROOT>;
				#mbox-cells = <2>;
			};

			mu2: mailbox@30e60000 {
				compatible = "fsl,imx8mp-mu", "fsl,imx6sx-mu";
				reg = <0x30e60000 0x10000>;
				interrupts = <GIC_SPI 136 IRQ_TYPE_LEVEL_HIGH>;
				#mbox-cells = <2>;
				clocks = <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_MU2_ROOT>;
				status = "okay";
			};

			i2c5: i2c@30ad0000 {
				compatible = "fsl,imx8mp-i2c", "fsl,imx21-i2c";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x30ad0000 0x10000>;
				interrupts = <GIC_SPI 76 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_I2C5_ROOT>;
				status = "disabled";
			};

			i2c6: i2c@30ae0000 {
				compatible = "fsl,imx8mp-i2c", "fsl,imx21-i2c";
				#address-cells = <1>;
				#size-cells = <0>;
				reg = <0x30ae0000 0x10000>;
				interrupts = <GIC_SPI 77 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_I2C6_ROOT>;
				status = "disabled";
			};

			usdhc1: mmc@30b40000 {
				compatible = "fsl,imx8mp-usdhc", "fsl,imx8mm-usdhc", "fsl,imx7d-usdhc";
				reg = <0x30b40000 0x10000>;
				interrupts = <GIC_SPI 22 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_IPG_ROOT>,
					 <&clk IMX8MP_CLK_NAND_USDHC_BUS>,
					 <&clk IMX8MP_CLK_USDHC1_ROOT>;
				clock-names = "ipg", "ahb", "per";
				fsl,tuning-start-tap = <20>;
				fsl,tuning-step = <2>;
				bus-width = <4>;
				status = "disabled";
			};

			usdhc2: mmc@30b50000 {
				compatible = "fsl,imx8mp-usdhc", "fsl,imx8mm-usdhc", "fsl,imx7d-usdhc";
				reg = <0x30b50000 0x10000>;
				interrupts = <GIC_SPI 23 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_IPG_ROOT>,
					 <&clk IMX8MP_CLK_NAND_USDHC_BUS>,
					 <&clk IMX8MP_CLK_USDHC2_ROOT>;
				clock-names = "ipg", "ahb", "per";
				fsl,tuning-start-tap = <20>;
				fsl,tuning-step = <2>;
				bus-width = <4>;
				status = "disabled";
			};

			usdhc3: mmc@30b60000 {
				compatible = "fsl,imx8mp-usdhc", "fsl,imx8mm-usdhc", "fsl,imx7d-usdhc";
				reg = <0x30b60000 0x10000>;
				interrupts = <GIC_SPI 24 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_IPG_ROOT>,
					 <&clk IMX8MP_CLK_NAND_USDHC_BUS>,
					 <&clk IMX8MP_CLK_USDHC3_ROOT>;
				clock-names = "ipg", "ahb", "per";
				fsl,tuning-start-tap = <20>;
				fsl,tuning-step = <2>;
				bus-width = <4>;
				status = "disabled";
			};

			flexspi: spi@30bb0000 {
				compatible = "nxp,imx8mp-fspi";
				reg = <0x30bb0000 0x10000>, <0x8000000 0x10000000>;
				reg-names = "fspi_base", "fspi_mmap";
				interrupts = <GIC_SPI 107 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_QSPI_ROOT>,
					 <&clk IMX8MP_CLK_QSPI_ROOT>;
				clock-names = "fspi_en", "fspi";
				assigned-clock-rates = <80000000>;
				assigned-clocks = <&clk IMX8MP_CLK_QSPI>;
				#address-cells = <1>;
				#size-cells = <0>;
				status = "disabled";
			};

			sdma1: dma-controller@30bd0000 {
				compatible = "fsl,imx8mq-sdma";
				reg = <0x30bd0000 0x10000>;
				interrupts = <GIC_SPI 2 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_SDMA1_ROOT>,
					 <&clk IMX8MP_CLK_AHB>;
				clock-names = "ipg", "ahb";
				#dma-cells = <3>;
				fsl,sdma-ram-script-name = "imx/sdma/sdma-imx7d.bin";
			};

			fec: ethernet@30be0000 {
				compatible = "fsl,imx8mp-fec", "fsl,imx8mq-fec", "fsl,imx6sx-fec";
				reg = <0x30be0000 0x10000>;
				interrupts = <GIC_SPI 118 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 119 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 120 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 121 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clk IMX8MP_CLK_ENET1_ROOT>,
					 <&clk IMX8MP_CLK_SIM_ENET_ROOT>,
					 <&clk IMX8MP_CLK_ENET_TIMER>,
					 <&clk IMX8MP_CLK_ENET_REF>,
					 <&clk IMX8MP_CLK_ENET_PHY_REF>;
				clock-names = "ipg", "ahb", "ptp",
					      "enet_clk_ref", "enet_out";
				assigned-clocks = <&clk IMX8MP_CLK_ENET_AXI>,
						  <&clk IMX8MP_CLK_ENET_TIMER>,
						  <&clk IMX8MP_CLK_ENET_REF>,
						  <&clk IMX8MP_CLK_ENET_PHY_REF>;
				assigned-clock-parents = <&clk IMX8MP_SYS_PLL1_266M>,
							 <&clk IMX8MP_SYS_PLL2_100M>,
							 <&clk IMX8MP_SYS_PLL2_125M>,
							 <&clk IMX8MP_SYS_PLL2_50M>;
				assigned-clock-rates = <0>, <100000000>, <125000000>, <0>;
				fsl,num-tx-queues = <3>;
				fsl,num-rx-queues = <3>;
				nvmem-cells = <&eth_mac1>;
				nvmem-cell-names = "mac-address";
				fsl,stop-mode = <&gpr 0x10 3>;
				status = "disabled";
			};

			eqos: ethernet@30bf0000 {
				compatible = "nxp,imx8mp-dwmac-eqos", "snps,dwmac-5.10a";
				reg = <0x30bf0000 0x10000>;
				interrupts = <GIC_SPI 135 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 134 IRQ_TYPE_LEVEL_HIGH>;
				interrupt-names = "macirq", "eth_wake_irq";
				clocks = <&clk IMX8MP_CLK_ENET_QOS_ROOT>,
					 <&clk IMX8MP_CLK_QOS_ENET_ROOT>,
					 <&clk IMX8MP_CLK_ENET_QOS_TIMER>,
					 <&clk IMX8MP_CLK_ENET_QOS>;
				clock-names = "stmmaceth", "pclk", "ptp_ref", "tx";
				assigned-clocks = <&clk IMX8MP_CLK_ENET_AXI>,
						  <&clk IMX8MP_CLK_ENET_QOS_TIMER>,
						  <&clk IMX8MP_CLK_ENET_QOS>;
				assigned-clock-parents = <&clk IMX8MP_SYS_PLL1_266M>,
							 <&clk IMX8MP_SYS_PLL2_100M>,
							 <&clk IMX8MP_SYS_PLL2_125M>;
				assigned-clock-rates = <0>, <100000000>, <125000000>;
				nvmem-cells = <&eth_mac2>;
				nvmem-cell-names = "mac-address";
				intf_mode = <&gpr 0x4>;
				status = "disabled";
			};
		};

		aips5: bus@30c00000 {
			compatible = "fsl,aips-bus", "simple-bus";
			reg = <0x30c00000 0x400000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges;

			spba-bus@30c00000 {
				compatible = "fsl,spba-bus", "simple-bus";
				reg = <0x30c00000 0x100000>;
				#address-cells = <1>;
				#size-cells = <1>;
				ranges;

				sai1: sai@30c10000 {
					compatible = "fsl,imx8mp-sai", "fsl,imx8mq-sai";
					reg = <0x30c10000 0x10000>;
					#sound-dai-cells = <0>;
					clocks = <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI1_IPG>,
						 <&clk IMX8MP_CLK_DUMMY>,
						 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI1_MCLK1>,
						 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI1_MCLK2>,
						 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI1_MCLK3>;
					clock-names = "bus", "mclk0", "mclk1", "mclk2", "mclk3";
					dmas = <&sdma2 0 2 0>, <&sdma2 1 2 0>;
					dma-names = "rx", "tx";
					interrupts = <GIC_SPI 95 IRQ_TYPE_LEVEL_HIGH>;
					power-domains = <&audiomix_pd>;
					status = "disabled";
				};

				sai2: sai@30c20000 {
					compatible = "fsl,imx8mp-sai", "fsl,imx8mq-sai";
					reg = <0x30c20000 0x10000>;
					#sound-dai-cells = <0>;
					clocks = <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI2_IPG>,
						 <&clk IMX8MP_CLK_DUMMY>,
						 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI2_MCLK1>,
						 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI2_MCLK2>,
						 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI2_MCLK3>;
					clock-names = "bus", "mclk0", "mclk1", "mclk2", "mclk3";
					dmas = <&sdma2 2 2 0>, <&sdma2 3 2 0>;
					dma-names = "rx", "tx";
					interrupts = <GIC_SPI 96 IRQ_TYPE_LEVEL_HIGH>;
					power-domains = <&audiomix_pd>;
					status = "disabled";
				};

				sai3: sai@30c30000 {
					compatible = "fsl,imx8mp-sai", "fsl,imx8mq-sai";
					reg = <0x30c30000 0x10000>;
					#sound-dai-cells = <0>;
					clocks = <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI3_IPG>,
						 <&clk IMX8MP_CLK_DUMMY>,
						 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI3_MCLK1>,
						 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI3_MCLK2>,
						 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI3_MCLK3>;
					clock-names = "bus", "mclk0", "mclk1", "mclk2", "mclk3";
					dmas = <&sdma2 4 2 0>, <&sdma2 5 2 0>;
					dma-names = "rx", "tx";
					interrupts = <GIC_SPI 50 IRQ_TYPE_LEVEL_HIGH>;
					power-domains = <&audiomix_pd>;
					status = "disabled";
				};

				sai5: sai@30c50000 {
					compatible = "fsl,imx8mp-sai", "fsl,imx8mq-sai";
					reg = <0x30c50000 0x10000>;
					#sound-dai-cells = <0>;
					clocks = <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI5_IPG>,
						 <&clk IMX8MP_CLK_DUMMY>,
						 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI5_MCLK1>,
						 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI5_MCLK2>,
						 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI5_MCLK3>;
					clock-names = "bus", "mclk0", "mclk1", "mclk2", "mclk3";
					dmas = <&sdma2 8 2 0>, <&sdma2 9 2 0>;
					dma-names = "rx", "tx";
					interrupts = <GIC_SPI 90 IRQ_TYPE_LEVEL_HIGH>;
					power-domains = <&audiomix_pd>;
					status = "disabled";
				};

				sai6: sai@30c60000 {
					compatible = "fsl,imx8mp-sai", "fsl,imx8mq-sai";
					reg = <0x30c60000 0x10000>;
					#sound-dai-cells = <0>;
					clocks = <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI6_IPG>,
						 <&clk IMX8MP_CLK_DUMMY>,
						 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI6_MCLK1>,
						 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI6_MCLK2>,
						 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI6_MCLK3>;
					clock-names = "bus", "mclk0", "mclk1", "mclk2", "mclk3";
					dmas = <&sdma2 10 2 0>, <&sdma2 11 2 0>;
					dma-names = "rx", "tx";
					interrupts = <GIC_SPI 90 IRQ_TYPE_LEVEL_HIGH>;
					power-domains = <&audiomix_pd>;
					status = "disabled";
				};

				sai7: sai@30c80000 {
					compatible = "fsl,imx8mp-sai", "fsl,imx8mq-sai";
					reg = <0x30c80000 0x10000>;
					#sound-dai-cells = <0>;
					clocks = <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI7_IPG>,
						 <&clk IMX8MP_CLK_DUMMY>,
						 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI7_MCLK1>,
						 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI7_MCLK2>,
						 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI7_MCLK3>;
					clock-names = "bus", "mclk0", "mclk1", "mclk2", "mclk3";
					dmas = <&sdma2 12 2 0>, <&sdma2 13 2 0>;
					dma-names = "rx", "tx";
					interrupts = <GIC_SPI 111 IRQ_TYPE_LEVEL_HIGH>;
					power-domains = <&audiomix_pd>;
					status = "disabled";
				};

				easrc: easrc@30c90000 {
					compatible = "fsl,imx8mp-easrc", "fsl,imx8mn-easrc";
					reg = <0x30c90000 0x10000>;
					interrupts = <GIC_SPI 122 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_ASRC_IPG>;
					clock-names = "mem";
					dmas = <&sdma2 16 23 0> , <&sdma2 17 23 0>,
					       <&sdma2 18 23 0> , <&sdma2 19 23 0>,
					       <&sdma2 20 23 0> , <&sdma2 21 23 0>,
					       <&sdma2 22 23 0> , <&sdma2 23 23 0>;
					dma-names = "ctx0_rx", "ctx0_tx",
						    "ctx1_rx", "ctx1_tx",
						    "ctx2_rx", "ctx2_tx",
						    "ctx3_rx", "ctx3_tx";
					firmware-name = "imx/easrc/easrc-imx8mn.bin";
					fsl,asrc-rate = <8000>;
					fsl,asrc-format = <2>;
					power-domains = <&audiomix_pd>;
					status = "disabled";
				};

				micfil: audio-controller@30ca0000 {
					compatible = "fsl,imx8mp-micfil";
					reg = <0x30ca0000 0x10000>;
					#sound-dai-cells = <0>;
					interrupts = <GIC_SPI 109 IRQ_TYPE_LEVEL_HIGH>,
						     <GIC_SPI 110 IRQ_TYPE_LEVEL_HIGH>,
						     <GIC_SPI 44 IRQ_TYPE_LEVEL_HIGH>,
						     <GIC_SPI 45 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_PDM_IPG>,
						 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_PDM_SEL>,
						 <&clk IMX8MP_AUDIO_PLL1_OUT>,
						 <&clk IMX8MP_AUDIO_PLL2_OUT>,
						 <&clk IMX8MP_CLK_EXT3>;
					clock-names = "ipg_clk", "ipg_clk_app",
						      "pll8k", "pll11k", "clkext3";
					dmas = <&sdma2 24 25 0x80000000>;
					dma-names = "rx";
					power-domains = <&audiomix_pd>;
					status = "disabled";
				};

				aud2htx: aud2htx@30cb0000 {
					compatible = "fsl,imx8mp-aud2htx";
					reg = <0x30cb0000 0x10000>;
					interrupts = <GIC_SPI 130 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_AUD2HTX_IPG>;
					clock-names = "bus";
					dmas = <&sdma2 26 2 0>;
					dma-names = "tx";
					power-domains = <&audiomix_pd>;
					status = "disabled";
				};

				xcvr: xcvr@30cc0000 {
					compatible = "fsl,imx8mp-xcvr";
					reg = <0x30cc0000 0x800>,
					      <0x30cc0800 0x400>,
					      <0x30cc0c00 0x080>,
					      <0x30cc0e00 0x080>;
					reg-names = "ram", "regs", "rxfifo",
						    "txfifo";
					interrupts = /* XCVR IRQ 0 */
						     <GIC_SPI 128 IRQ_TYPE_LEVEL_HIGH>,
						     /* XCVR IRQ 1 */
						     <GIC_SPI 129 IRQ_TYPE_LEVEL_HIGH>,
						     /* XCVR PHY - SPDIF wakeup IRQ */
						     <GIC_SPI 146 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_EARC_IPG>,
						 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_EARC_PHY>,
						 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SPBA2_ROOT>,
						 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_AUDPLL_ROOT>;
					clock-names = "ipg", "phy", "spba", "pll_ipg";
					dmas = <&sdma2 30 2 0>, <&sdma2 31 2 0>;
					dma-names = "rx", "tx";
					resets = <&audio_blk_ctrl 0>;
					power-domains = <&audiomix_pd>;
					hdmi-phandle = <&hdmi>;
					status = "disabled";
				};
			};

			sdma3: dma-controller@30e00000 {
				compatible = "fsl,imx8mp-sdma", "fsl,imx8mq-sdma";
				reg = <0x30e00000 0x10000>;
				#dma-cells = <3>;
				clocks = <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SDMA3_ROOT>,
					 <&clk IMX8MP_CLK_AUDIO_ROOT>;
				clock-names = "ipg", "ahb";
				interrupts = <GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>;
				fsl,sdma-ram-script-name = "imx/sdma/sdma-imx7d.bin";
				power-domains = <&audiomix_pd>;
				status = "disabled";
			};

			sdma2: dma-controller@30e10000 {
				compatible = "fsl,imx8mp-sdma", "fsl,imx8mq-sdma";
				reg = <0x30e10000 0x10000>;
				#dma-cells = <3>;
				clocks = <&clk IMX8MP_CLK_AUDIO_ROOT>,
					 <&clk IMX8MP_CLK_AUDIO_ROOT>;
				clock-names = "ipg", "ahb";
				interrupts = <GIC_SPI 103 IRQ_TYPE_LEVEL_HIGH>;
				fsl,sdma-ram-script-name = "imx/sdma/sdma-imx7d.bin";
				power-domains = <&audiomix_pd>;
				status = "disabled";
			};

			audio_blk_ctrl: clock-controller@30e20000 {
				compatible = "fsl,imx8mp-audio-blk-ctrl", "syscon";
				reg = <0x30e20000 0x10000>;
				#clock-cells = <1>;
				#reset-cells = <1>;
				clocks = <&clk IMX8MP_CLK_AUDIO_ROOT>,
					 <&clk IMX8MP_CLK_SAI1>,
					 <&clk IMX8MP_CLK_SAI2>,
					 <&clk IMX8MP_CLK_SAI3>,
					 <&clk IMX8MP_CLK_SAI5>,
					 <&clk IMX8MP_CLK_SAI6>,
					 <&clk IMX8MP_CLK_SAI7>;
				clock-names = "ahb",
					      "sai1", "sai2", "sai3",
					      "sai5", "sai6", "sai7";
				power-domains = <&audiomix_pd>;
				assigned-clocks = <&clk IMX8MP_CLK_AUDIO_AHB>,
						  <&clk IMX8MP_CLK_AUDIO_AXI_SRC>,
						  <&clk IMX8MP_AUDIO_PLL1>,
						  <&clk IMX8MP_AUDIO_PLL2>;
				assigned-clock-parents = <&clk IMX8MP_SYS_PLL1_800M>,
							 <&clk IMX8MP_SYS_PLL1_800M>;
				assigned-clock-rates = <400000000>,
						       <800000000>,
						       <393216000>,
						       <361267200>;
			};
		};

		noc: interconnect@32700000 {
			compatible = "fsl,imx8mp-noc", "fsl,imx8m-noc", "syscon";
			reg = <0x32700000 0x100000>;
		};

		aips4: bus@32c00000 {
			compatible = "fsl,aips-bus", "simple-bus";
			reg = <0x32c00000 0x400000>;
			#address-cells = <1>;
			#size-cells = <1>;
			ranges;

			mipi_dsi: mipi_dsi@32e60000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "fsl,imx8mp-mipi-dsim";
				reg = <0x32e60000 0x10000>;
				clocks = <&clk IMX8MP_CLK_MEDIA_APB_ROOT>,
					 <&clk IMX8MP_CLK_MEDIA_MIPI_PHY1_REF>,
					 <&clk IMX8MP_CLK_MEDIA_APB_ROOT>,
					 <&clk IMX8MP_CLK_MEDIA_DISP1_PIX_ROOT>;
				clock-names = "cfg", "pll-ref", "apb-root", "pixel";
				assigned-clocks = <&clk IMX8MP_CLK_MEDIA_MIPI_PHY1_REF>;
				assigned-clock-parents = <&clk IMX8MP_CLK_24M>;
				assigned-clock-rates = <12000000>;
				interrupts = <GIC_SPI 18 IRQ_TYPE_LEVEL_HIGH>;
				power-domains = <&media_blk_ctrl IMX8MP_MEDIABLK_PD_MIPI_DSI_1>;
				status = "disabled";

				port@0 {
					dsim_from_lcdif: endpoint {
						remote-endpoint = <&lcdif_to_dsim>;
					};
				};
			};

			lcdif1: lcd-controller@32e80000 {
				compatible = "fsl,imx8mp-lcdif1";
				reg = <0x32e80000 0x10000>;
				clocks = <&clk IMX8MP_CLK_MEDIA_DISP1_PIX_ROOT>,
					 <&clk IMX8MP_CLK_MEDIA_AXI_ROOT>,
					 <&clk IMX8MP_CLK_MEDIA_APB_ROOT>;
				clock-names = "pix", "disp-axi", "disp-apb";
				assigned-clocks = <&clk IMX8MP_CLK_MEDIA_DISP1_PIX>,
						  <&clk IMX8MP_CLK_MEDIA_AXI>,
						  <&clk IMX8MP_CLK_MEDIA_APB>;
				assigned-clock-parents = <&clk IMX8MP_VIDEO_PLL1_OUT>,
							 <&clk IMX8MP_SYS_PLL2_1000M>,
							 <&clk IMX8MP_SYS_PLL1_800M>;
				assigned-clock-rates = <0>, <500000000>, <200000000>;
				interrupts = <GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>;
				blk-ctl = <&media_blk_ctrl>;
				power-domains = <&media_blk_ctrl IMX8MP_MEDIABLK_PD_LCDIF_1>;
				status = "disabled";

				lcdif1_disp: port {
					lcdif_to_dsim: endpoint {
						remote-endpoint = <&dsim_from_lcdif>;
					};
				};
			};

			lcdif2: lcd-controller@32e90000 {
				compatible = "fsl,imx8mp-lcdif2";
				reg = <0x32e90000 0x10000>;
				clocks = <&clk IMX8MP_CLK_MEDIA_DISP2_PIX_ROOT>,
					 <&clk IMX8MP_CLK_MEDIA_AXI_ROOT>,
					 <&clk IMX8MP_CLK_MEDIA_APB_ROOT>;
				clock-names = "pix", "disp-axi", "disp-apb";
				assigned-clocks = <&clk IMX8MP_CLK_MEDIA_DISP2_PIX>,
						  <&clk IMX8MP_CLK_MEDIA_AXI>,
						  <&clk IMX8MP_CLK_MEDIA_APB>;
				assigned-clock-parents = <&clk IMX8MP_VIDEO_PLL1_OUT>,
							 <&clk IMX8MP_SYS_PLL2_1000M>,
							 <&clk IMX8MP_SYS_PLL1_800M>;
				assigned-clock-rates = <0>, <500000000>, <200000000>;
				interrupts = <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>;
				power-domains = <&media_blk_ctrl IMX8MP_MEDIABLK_PD_LCDIF_2>;
				status = "disabled";

				lcdif2_disp: port {
					#address-cells = <1>;
					#size-cells = <0>;

					lcdif2_disp_ldb_ch0: endpoint@0 {
						reg = <0>;
						remote-endpoint = <&ldb_ch0>;
					};

					lcdif2_disp_ldb_ch1: endpoint@1 {
						reg = <1>;
						remote-endpoint = <&ldb_ch1>;
					};
				};
			};

			media_blk_ctrl: blk-ctrl@32ec0000 {
				compatible = "fsl,imx8mp-media-blk-ctrl",
					     "syscon";
				reg = <0x32ec0000 0x10000>;
				power-domains = <&pgc_mediamix>,
						<&pgc_mipi_phy1>,
						<&pgc_mipi_phy1>,
						<&pgc_mediamix>,
						<&pgc_mediamix>,
						<&pgc_mipi_phy2>,
						<&pgc_mediamix>,
						<&pgc_ispdwp>,
						<&pgc_ispdwp>,
						<&pgc_mipi_phy2>;
				power-domain-names = "bus", "mipi-dsi1", "mipi-csi1",
						     "lcdif1", "isi", "mipi-csi2",
						     "lcdif2", "isp", "dwe",
						     "mipi-dsi2";
				clocks = <&clk IMX8MP_CLK_MEDIA_APB_ROOT>,
					 <&clk IMX8MP_CLK_MEDIA_AXI_ROOT>,
					 <&clk IMX8MP_CLK_MEDIA_CAM1_PIX_ROOT>,
					 <&clk IMX8MP_CLK_MEDIA_CAM2_PIX_ROOT>,
					 <&clk IMX8MP_CLK_MEDIA_DISP1_PIX_ROOT>,
					 <&clk IMX8MP_CLK_MEDIA_DISP2_PIX_ROOT>,
					 <&clk IMX8MP_CLK_MEDIA_ISP_ROOT>,
					 <&clk IMX8MP_CLK_MEDIA_MIPI_PHY1_REF_ROOT>;
				clock-names = "apb", "axi", "cam1", "cam2",
					      "disp1", "disp2", "isp", "phy";

				assigned-clocks = <&clk IMX8MP_CLK_MEDIA_AXI>,
						  <&clk IMX8MP_CLK_MEDIA_APB>,
						  <&clk IMX8MP_CLK_MEDIA_DISP1_PIX>,
						  <&clk IMX8MP_CLK_MEDIA_DISP2_PIX>,
						  <&clk IMX8MP_VIDEO_PLL1>;
				assigned-clock-parents = <&clk IMX8MP_SYS_PLL2_1000M>,
							 <&clk IMX8MP_SYS_PLL1_800M>,
							 <&clk IMX8MP_VIDEO_PLL1_OUT>,
							 <&clk IMX8MP_VIDEO_PLL1_OUT>;
				assigned-clock-rates = <500000000>, <200000000>,
						       <0>, <0>, <1039500000>;
				#power-domain-cells = <1>;
			};

			pcie_phy: pcie-phy@32f00000 {
				compatible = "fsl,imx8mp-pcie-phy";
				reg = <0x32f00000 0x10000>;
				resets = <&src IMX8MP_RESET_PCIEPHY>,
					 <&src IMX8MP_RESET_PCIEPHY_PERST>;
				reset-names = "pciephy", "perst";
				power-domains = <&hsio_blk_ctrl IMX8MP_HSIOBLK_PD_PCIE_PHY>;
				#phy-cells = <0>;
				status = "disabled";
			};

			hsio_blk_ctrl: blk-ctrl@32f10000 {
				compatible = "fsl,imx8mp-hsio-blk-ctrl", "syscon";
				reg = <0x32f10000 0x24>;
				clocks = <&clk IMX8MP_CLK_USB_ROOT>,
					 <&clk IMX8MP_CLK_PCIE_ROOT>;
				clock-names = "usb", "pcie";
				power-domains = <&pgc_hsiomix>, <&pgc_hsiomix>,
						<&pgc_usb1_phy>, <&pgc_usb2_phy>,
						<&pgc_hsiomix>, <&pgc_pcie_phy>;
				power-domain-names = "bus", "usb", "usb-phy1",
						     "usb-phy2", "pcie", "pcie-phy";
				#power-domain-cells = <1>;
				#clock-cells = <0>;
			};

			hdmi_blk_ctrl: blk-ctrl@32fc0000 {
				compatible = "fsl,imx8mp-hdmi-blk-ctrl", "syscon";
				reg = <0x32fc0000 0x1000>;
				clocks = <&clk IMX8MP_CLK_HDMI_APB>,
					 <&clk IMX8MP_CLK_HDMI_ROOT>,
					 <&clk IMX8MP_CLK_HDMI_REF_266M>,
					 <&clk IMX8MP_CLK_HDMI_24M>,
					 <&clk IMX8MP_CLK_HDMI_FDCC_TST>;
				clock-names = "apb", "axi", "ref_266m", "ref_24m", "fdcc";
				power-domains = <&pgc_hdmimix>, <&pgc_hdmimix>,
						<&pgc_hdmimix>, <&pgc_hdmimix>,
						<&pgc_hdmimix>, <&pgc_hdmimix>,
						<&pgc_hdmimix>, <&pgc_hdmi_phy>,
						<&pgc_hdmimix>, <&pgc_hdmimix>;
				power-domain-names = "bus", "irqsteer", "lcdif",
						     "pai", "pvi", "trng",
						     "hdmi-tx", "hdmi-tx-phy",
						     "hdcp", "hrv";
				#power-domain-cells = <1>;
			};

			irqsteer_hdmi: interrupt-controller@32fc2000 {
				compatible = "fsl,imx8mp-irqsteer", "fsl,imx-irqsteer";
				reg = <0x32fc2000 0x1000>;
				interrupts = <GIC_SPI 43 IRQ_TYPE_LEVEL_HIGH>;
				interrupt-controller;
				#interrupt-cells = <1>;
				fsl,channel = <1>;
				fsl,num-irqs = <64>;
				clocks = <&clk IMX8MP_CLK_HDMI_APB>;
				clock-names = "ipg";
				power-domains = <&hdmi_blk_ctrl IMX8MP_HDMIBLK_PD_IRQSTEER>;
			};

			hdmi_pavi: hdmi-pai-pvi@32fc4000 {
				compatible = "fsl,imx8mp-hdmi-pavi";
				reg = <0x32fc4000 0x1000>;
				clocks = <&clk IMX8MP_CLK_HDMI_APB>;
				power-domains = <&hdmi_blk_ctrl IMX8MP_HDMIBLK_PD_PVI>;
				status = "disabled";
			};

			lcdif3: lcd-controller@32fc6000 {
				compatible = "fsl,imx8mp-lcdif1";
				reg = <0x32fc6000 0x10000>;
				clocks = <&hdmiphy 0>,
						<&clk IMX8MP_CLK_HDMI_AXI>,
						<&clk IMX8MP_CLK_HDMI_APB>;
				clock-names = "pix", "disp-axi", "disp-apb";
				power-domains = <&hdmi_blk_ctrl IMX8MP_HDMIBLK_PD_LCDIF>;
				assigned-clocks =  <&clk IMX8MP_CLK_HDMI_AXI>,
								<&clk IMX8MP_CLK_HDMI_APB>;
				assigned-clock-parents = <&clk IMX8MP_SYS_PLL2_500M>,
							 <&clk IMX8MP_SYS_PLL1_133M>;
				assigned-clock-rates = <500000000>, <133000000>;
				interrupts = <8 IRQ_TYPE_LEVEL_HIGH>;
				interrupt-parent = <&irqsteer_hdmi>;
				status = "disabled";

				lcdif3_disp: port {
					lcdif3_to_hdmi: endpoint {
						remote-endpoint = <&hdmi_from_lcdif3>;
					};
				};
			};

			hdmi: hdmi@32fd8000 {
				compatible = "fsl,imx8mp-hdmi";
				reg = <0x32fd8000 0x7eff>;
				interrupts = <0 IRQ_TYPE_LEVEL_HIGH>;
				interrupt-parent = <&irqsteer_hdmi>;
				power-domains = <&hdmi_blk_ctrl IMX8MP_HDMIBLK_PD_HDMI_TX>;
				clocks = <&clk IMX8MP_CLK_HDMI_APB>,
						<&clk IMX8MP_CLK_HDMI_24M>,
						<&hdmiphy 0>;
				clock-names = "iahb", "isfr", "pix";
				assigned-clocks = <&clk IMX8MP_CLK_HDMI_APB>,
							<&clk IMX8MP_CLK_HDMI_AXI>,
							<&clk IMX8MP_CLK_HDMI_24M>;
				assigned-clock-parents = <&clk IMX8MP_SYS_PLL1_133M>,
							<&clk IMX8MP_SYS_PLL2_500M>,
							<&clk IMX8MP_CLK_24M>;
				assigned-clock-rates = <133000000>, <500000000>, <24000000>;
				status = "disabled";

				port {
					hdmi_from_lcdif3: endpoint {
						remote-endpoint = <&lcdif3_to_hdmi>;
					};
				};
			};

			hdmiphy: hdmiphy@32fdff00 {
				compatible = "fsl,imx8mp-hdmi-phy";
				reg = <0x32fdff00 0x100>;
				clocks = <&clk IMX8MP_CLK_HDMI_APB>,
					 <&clk IMX8MP_CLK_HDMI_24M>;
				clock-names = "apb", "ref";
				assigned-clocks = <&clk IMX8MP_CLK_HDMI_24M>;
				assigned-clock-parents = <&clk IMX8MP_CLK_24M>;
				#clock-cells = <1>;
				clock-output-names = "hdmi_phy";
				#phy-cells = <0>;
				power-domains = <&hdmi_blk_ctrl IMX8MP_HDMIBLK_PD_HDMI_TX_PHY>;
				status = "disabled";
			};

			mediamix_gpr: media_gpr@32ec0008 {
				compatible = "fsl,imx8mp-iomuxc-gpr", "syscon";
				reg = <0x32ec0008 0x4>;
			};

			mediamix_gasket0: gasket@32ec0060 {
				compatible = "fsl,imx8mp-iomuxc-gpr", "syscon";
				reg = <0x32ec0060 0x28>;
			};

			mediamix_gasket1: gasket@32ec0090 {
				compatible = "fsl,imx8mp-iomuxc-gpr", "syscon";
				reg = <0x32ec0090 0x28>;
			};

			isi_chain_buf: isi_chain@32e02000{
				compatible = "fsl,imx8mp-iomuxc-gpr", "syscon";
				reg = <0x32e02000 0x4>;
			};

			cameradev: camera {
				compatible = "fsl,mxc-md", "simple-bus";
				#address-cells = <1>;
				#size-cells = <1>;
				ranges;
				status = "disabled";

				isi_0: isi@32e00000 {
					compatible = "nxp,imx8mp-isi", "nxp,imx8mn-isi";
					reg = <0x32e00000 0x2000>;
					interrupts = <GIC_SPI 16 IRQ_TYPE_LEVEL_HIGH>;
					interface = <2 0 2>;
					clocks = <&clk IMX8MP_CLK_MEDIA_AXI>,
						 <&clk IMX8MP_CLK_MEDIA_APB>,
						 <&clk IMX8MP_CLK_MEDIA_AXI_ROOT>,
						 <&clk IMX8MP_CLK_MEDIA_APB_ROOT>;
					clock-names = "disp_axi",
						      "disp_apb",
						      "disp_axi_root",
						      "disp_apb_root";
					assigned-clocks = <&clk IMX8MP_CLK_MEDIA_AXI_ROOT>,
							  <&clk IMX8MP_CLK_MEDIA_APB_ROOT>;
					assigned-clock-rates = <500000000>, <200000000>;
					power-domains = <&media_blk_ctrl IMX8MP_MEDIABLK_PD_ISI>;
					isi_chain = <&isi_chain_buf>;
					status = "disabled";

					cap_device {
						compatible = "imx-isi-capture";
						status = "disabled";
					};

					m2m_device{
						compatible = "imx-isi-m2m";
						fsl,gpr = <&media_blk_ctrl>;
						status = "disabled";
					};
				};

				isi_1: isi@32e02000 {
					compatible = "nxp,imx8mp-isi", "nxp,imx8mn-isi";
					reg = <0x32e02000 0x2000>;
					interrupts = <GIC_SPI 42 IRQ_TYPE_LEVEL_HIGH>;
					interface = <3 0 2>;
					clocks = <&clk IMX8MP_CLK_MEDIA_AXI>,
						 <&clk IMX8MP_CLK_MEDIA_APB>,
						 <&clk IMX8MP_CLK_MEDIA_AXI_ROOT>,
						 <&clk IMX8MP_CLK_MEDIA_APB_ROOT>;
					clock-names = "disp_axi",
						      "disp_apb",
						      "disp_axi_root",
						      "disp_apb_root";
					assigned-clocks = <&clk IMX8MP_CLK_MEDIA_AXI_ROOT>,
							  <&clk IMX8MP_CLK_MEDIA_APB_ROOT>;
					assigned-clock-rates = <500000000>, <200000000>;
					power-domains = <&media_blk_ctrl IMX8MP_MEDIABLK_PD_ISI>;
					status = "disabled";

					cap_device {
						compatible = "imx-isi-capture";
						status = "disabled";
					};
				};

				dewarp: dwe@32e30000 {
					compatible = "fsl,imx8mp-dwe";
					clocks = <&clk IMX8MP_CLK_MEDIA_AXI_ROOT>,
						 <&clk IMX8MP_CLK_MEDIA_AXI_ROOT>,
						 <&clk IMX8MP_CLK_MEDIA_APB_ROOT>;
					clock-names = "core", "axi", "ahb";
					reg = <0x32e30000 0x10000>;
					interrupts = <GIC_SPI 100 IRQ_TYPE_LEVEL_HIGH>;
					power-domains = <&media_blk_ctrl IMX8MP_MEDIABLK_PD_DWE>;
					status = "disabled";
				};

				isp_0: isp@32e10000 {
					compatible = "fsl,imx8mp-isp";
					reg = <0x32e10000 0x10000>;
					interrupts = <GIC_SPI 74 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clk IMX8MP_CLK_MEDIA_ISP_ROOT>,
						 <&clk IMX8MP_CLK_MEDIA_AXI_ROOT>,
						 <&clk IMX8MP_CLK_MEDIA_APB_ROOT>,
						 <&clk IMX8MP_CLK_MEDIA_CAM1_PIX_ROOT>;
					clock-names = "core", "axi", "ahb", "sensor";
					assigned-clocks = <&clk IMX8MP_CLK_MEDIA_ISP>;
					assigned-clock-parents = <&clk IMX8MP_SYS_PLL2_500M>;
					assigned-clock-rates = <500000000>;
					power-domains = <&media_blk_ctrl IMX8MP_MEDIABLK_PD_MIPI_CSI2_1>,
							<&media_blk_ctrl IMX8MP_MEDIABLK_PD_ISP>;
					id = <0>;
					gpr = <&media_blk_ctrl>;
					status = "disabled";
				};

				isp_1: isp@32e20000 {
					compatible = "fsl,imx8mp-isp";
					reg = <0x32e20000 0x10000>;
					interrupts = <GIC_SPI 75 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clk IMX8MP_CLK_MEDIA_ISP_ROOT>,
						 <&clk IMX8MP_CLK_MEDIA_AXI_ROOT>,
						 <&clk IMX8MP_CLK_MEDIA_APB_ROOT>,
						 <&clk IMX8MP_CLK_MEDIA_CAM1_PIX_ROOT>;
					clock-names = "core", "axi", "ahb", "sensor";
					assigned-clocks = <&clk IMX8MP_CLK_MEDIA_ISP>;
					assigned-clock-parents = <&clk IMX8MP_SYS_PLL2_500M>;
					assigned-clock-rates = <500000000>;
					power-domains = <&media_blk_ctrl IMX8MP_MEDIABLK_PD_MIPI_CSI2_1>,
							<&media_blk_ctrl IMX8MP_MEDIABLK_PD_ISP>;
					id = <1>;
					gpr = <&media_blk_ctrl>;
					status = "disabled";
				};

				mipi_csi_0: csi@32e40000 {
					compatible = "fsl,imx8mp-mipi-csi", "fsl,imx8mn-mipi-csi";
					reg = <0x32e40000 0x10000>;
					interrupts = <GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>;
					clock-frequency = <500000000>;
					clocks = <&clk IMX8MP_CLK_MEDIA_CAM1_PIX_ROOT>,
						 <&clk IMX8MP_CLK_MEDIA_AXI>,
						 <&clk IMX8MP_CLK_MEDIA_APB_ROOT>;
					clock-names = "mipi_clk",
						      "disp_axi",
						      "disp_apb";
					assigned-clocks = <&clk IMX8MP_CLK_MEDIA_CAM1_PIX>;
					assigned-clock-parents = <&clk IMX8MP_SYS_PLL2_1000M>;
					assigned-clock-rates = <500000000>;
					bus-width = <4>;
					csi-gpr = <&mediamix_gasket0>;
					gpr = <&media_blk_ctrl>;
					power-domains = <&media_blk_ctrl IMX8MP_MEDIABLK_PD_MIPI_CSI2_1>;
					status = "disabled";
				};

				mipi_csi_1: csi@32e50000 {
					compatible = "fsl,imx8mp-mipi-csi", "fsl,imx8mn-mipi-csi";
					reg = <0x32e50000 0x10000>;
					interrupts = <GIC_SPI 80 IRQ_TYPE_LEVEL_HIGH>;
					clock-frequency = <500000000>;
					clocks = <&clk IMX8MP_CLK_MEDIA_CAM2_PIX_ROOT>,
						 <&clk IMX8MP_CLK_MEDIA_AXI>,
						 <&clk IMX8MP_CLK_MEDIA_APB_ROOT>;
					clock-names = "mipi_clk",
						      "disp_axi",
						      "disp_apb";
					assigned-clocks = <&clk IMX8MP_CLK_MEDIA_CAM2_PIX>;
					assigned-clock-parents = <&clk IMX8MP_SYS_PLL2_1000M>;
					assigned-clock-rates = <500000000>;
					bus-width = <4>;
					csi-gpr = <&mediamix_gasket1>;
					gpr = <&media_blk_ctrl>;
					power-domains = <&media_blk_ctrl IMX8MP_MEDIABLK_PD_MIPI_CSI2_2>;
					status = "disabled";
				};
			};
		};

		dma_apbh: dma-apbh@33000000 {
			compatible = "fsl,imx7d-dma-apbh", "fsl,imx28-dma-apbh";
			reg = <0x33000000 0x2000>;
			interrupts = <GIC_SPI 12 IRQ_TYPE_LEVEL_HIGH>,
				<GIC_SPI 12 IRQ_TYPE_LEVEL_HIGH>,
				<GIC_SPI 12 IRQ_TYPE_LEVEL_HIGH>,
				<GIC_SPI 12 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "gpmi0", "gpmi1", "gpmi2", "gpmi3";
			#dma-cells = <1>;
			dma-channels = <4>;
			clocks = <&clk IMX8MP_CLK_NAND_USDHC_BUS_RAWNAND_CLK>;
		};

		gpmi: gpmi-nand@33002000{
			compatible = "fsl,imx7d-gpmi-nand";
			#address-cells = <1>;
			#size-cells = <1>;
			reg = <0x33002000 0x2000>, <0x33004000 0x4000>;
			reg-names = "gpmi-nand", "bch";
			interrupts = <GIC_SPI 14 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "bch";
			clocks = <&clk IMX8MP_CLK_NAND_ROOT>,
				<&clk IMX8MP_CLK_NAND_USDHC_BUS_RAWNAND_CLK>;
			clock-names = "gpmi_io", "gpmi_bch_apb";
			dmas = <&dma_apbh 0>;
			dma-names = "rx-tx";
			status = "disabled";
		};

		pcie: pcie@33800000 {
			compatible = "fsl,imx8mp-pcie";
			reg = <0x33800000 0x400000>, <0x1ff00000 0x80000>;
			reg-names = "dbi", "config";
			clocks = <&clk IMX8MP_CLK_HSIO_ROOT>,
				 <&clk IMX8MP_CLK_HSIO_AXI>,
				 <&clk IMX8MP_CLK_PCIE_ROOT>;
			clock-names = "pcie", "pcie_bus", "pcie_aux";
			assigned-clocks = <&clk IMX8MP_CLK_PCIE_AUX>;
			assigned-clock-rates = <10000000>;
			assigned-clock-parents = <&clk IMX8MP_SYS_PLL2_50M>;
			#address-cells = <3>;
			#size-cells = <2>;
			device_type = "pci";
			bus-range = <0x00 0xff>;
			ranges = <0x81000000 0 0x00000000 0x1ff80000 0 0x00010000>, /* downstream I/O 64KB */
				 <0x82000000 0 0x18000000 0x18000000 0 0x07f00000>; /* non-prefetchable memory */
			num-lanes = <1>;
			num-viewport = <4>;
			interrupts = <GIC_SPI 140 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "msi";
			#interrupt-cells = <1>;
			interrupt-map-mask = <0 0 0 0x7>;
			interrupt-map = <0 0 0 1 &gic GIC_SPI 126 IRQ_TYPE_LEVEL_HIGH>,
					<0 0 0 2 &gic GIC_SPI 125 IRQ_TYPE_LEVEL_HIGH>,
					<0 0 0 3 &gic GIC_SPI 124 IRQ_TYPE_LEVEL_HIGH>,
					<0 0 0 4 &gic GIC_SPI 123 IRQ_TYPE_LEVEL_HIGH>;
			fsl,max-link-speed = <3>;
			linux,pci-domain = <0>;
			power-domains = <&hsio_blk_ctrl IMX8MP_HSIOBLK_PD_PCIE>;
			resets = <&src IMX8MP_RESET_PCIE_CTRL_APPS_EN>,
				 <&src IMX8MP_RESET_PCIE_CTRL_APPS_TURNOFF>;
			reset-names = "apps", "turnoff";
			phys = <&pcie_phy>;
			phy-names = "pcie-phy";
			status = "disabled";
		};

		pcie_ep: pcie-ep@33800000 {
			compatible = "fsl,imx8mp-pcie-ep";
			reg = <0x33800000 0x100000>,
			      <0x33900000 0x100000>,
			      <0x33b00000 0x100000>,
			      <0x18000000 0x8000000>;
			reg-names = "dbi", "dbi2", "atu", "addr_space";
			clocks = <&clk IMX8MP_CLK_HSIO_ROOT>,
				 <&clk IMX8MP_CLK_HSIO_AXI>,
				 <&clk IMX8MP_CLK_PCIE_ROOT>;
			clock-names = "pcie", "pcie_bus", "pcie_aux";
			assigned-clocks = <&clk IMX8MP_CLK_PCIE_AUX>;
			assigned-clock-rates = <10000000>;
			assigned-clock-parents = <&clk IMX8MP_SYS_PLL2_50M>;
			num-lanes = <1>;
			interrupts = <GIC_SPI 127 IRQ_TYPE_LEVEL_HIGH>; /* eDMA */
			interrupt-names = "dma";
			fsl,max-link-speed = <3>;
			power-domains = <&hsio_blk_ctrl IMX8MP_HSIOBLK_PD_PCIE>;
			resets = <&src IMX8MP_RESET_PCIE_CTRL_APPS_EN>,
				 <&src IMX8MP_RESET_PCIE_CTRL_APPS_TURNOFF>;
			reset-names = "apps", "turnoff";
			phys = <&pcie_phy>;
			phy-names = "pcie-phy";
			num-ib-windows = <4>;
			num-ob-windows = <4>;
			status = "disabled";
		};

		vpumix_blk_ctrl: blk-ctrl@38330000 {
			compatible = "fsl,imx8mp-vpu-blk-ctrl", "syscon";
			reg = <0x38330000 0x100>;
			#power-domain-cells = <1>;
			power-domains = <&pgc_vpumix>, <&pgc_vpu_g1>,
					<&pgc_vpu_g2>, <&pgc_vpu_vc8000e>;
			power-domain-names = "bus", "g1", "g2", "vc8000e";
			clocks = <&clk IMX8MP_CLK_VPU_G1_ROOT>,
				 <&clk IMX8MP_CLK_VPU_G2_ROOT>,
				 <&clk IMX8MP_CLK_VPU_VC8KE_ROOT>;
			clock-names = "g1", "g2", "vc8000e";
		};

		gic: interrupt-controller@38800000 {
			compatible = "arm,gic-v3";
			reg = <0x38800000 0x10000>,
			      <0x38880000 0xc0000>;
			#interrupt-cells = <3>;
			interrupt-controller;
			interrupts = <GIC_PPI 9 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-parent = <&gic>;
		};

		edacmc: memory-controller@3d400000 {
			compatible = "snps,ddrc-3.80a";
			reg = <0x3d400000 0x400000>;
			interrupts = <GIC_SPI 147 IRQ_TYPE_LEVEL_HIGH>;
		};

		ddr-pmu@3d800000 {
			compatible = "fsl,imx8mp-ddr-pmu", "fsl,imx8m-ddr-pmu";
			reg = <0x3d800000 0x400000>;
			interrupts = <GIC_SPI 98 IRQ_TYPE_LEVEL_HIGH>;
		};

		usb3_phy0: usb-phy@381f0040 {
			compatible = "fsl,imx8mp-usb-phy";
			reg = <0x381f0040 0x40>;
			clocks = <&clk IMX8MP_CLK_USB_PHY_ROOT>;
			clock-names = "phy";
			assigned-clocks = <&clk IMX8MP_CLK_USB_PHY_REF>;
			assigned-clock-parents = <&clk IMX8MP_CLK_24M>;
			power-domains = <&hsio_blk_ctrl IMX8MP_HSIOBLK_PD_USB_PHY1>;
			#phy-cells = <0>;
			status = "disabled";
		};

		usb3_0: usb@32f10100 {
			compatible = "fsl,imx8mp-dwc3";
			reg = <0x32f10100 0x8>,
			      <0x381f0000 0x20>;
			clocks = <&clk IMX8MP_CLK_HSIO_ROOT>,
				 <&clk IMX8MP_CLK_USB_SUSP>;
			clock-names = "hsio", "suspend";
			interrupts = <GIC_SPI 148 IRQ_TYPE_LEVEL_HIGH>;
			power-domains = <&hsio_blk_ctrl IMX8MP_HSIOBLK_PD_USB>;
			#address-cells = <1>;
			#size-cells = <1>;
			dma-ranges = <0x40000000 0x40000000 0xc0000000>;
			ranges;
			status = "disabled";

			usb_dwc3_0: usb@38100000 {
				compatible = "snps,dwc3";
				reg = <0x38100000 0x10000>;
				clocks = <&clk IMX8MP_CLK_USB_ROOT>,
					 <&clk IMX8MP_CLK_USB_CORE_REF>,
					 <&clk IMX8MP_CLK_USB_SUSP>;
				clock-names = "bus_early", "ref", "suspend";
				interrupts = <GIC_SPI 40 IRQ_TYPE_LEVEL_HIGH>;
				phys = <&usb3_phy0>, <&usb3_phy0>;
				phy-names = "usb2-phy", "usb3-phy";
				snps,gfladj-refclk-lpm-sel-quirk;
				snps,parkmode-disable-ss-quirk;
			};

		};

		usb3_phy1: usb-phy@382f0040 {
			compatible = "fsl,imx8mp-usb-phy";
			reg = <0x382f0040 0x40>;
			clocks = <&clk IMX8MP_CLK_USB_PHY_ROOT>;
			clock-names = "phy";
			assigned-clocks = <&clk IMX8MP_CLK_USB_PHY_REF>;
			assigned-clock-parents = <&clk IMX8MP_CLK_24M>;
			power-domains = <&hsio_blk_ctrl IMX8MP_HSIOBLK_PD_USB_PHY2>;
			#phy-cells = <0>;
			status = "disabled";
		};

		usb3_1: usb@32f10108 {
			compatible = "fsl,imx8mp-dwc3";
			reg = <0x32f10108 0x8>,
			      <0x382f0000 0x20>;
			clocks = <&clk IMX8MP_CLK_HSIO_ROOT>,
				 <&clk IMX8MP_CLK_USB_SUSP>;
			clock-names = "hsio", "suspend";
			interrupts = <GIC_SPI 149 IRQ_TYPE_LEVEL_HIGH>;
			power-domains = <&hsio_blk_ctrl IMX8MP_HSIOBLK_PD_USB>;
			#address-cells = <1>;
			#size-cells = <1>;
			dma-ranges = <0x40000000 0x40000000 0xc0000000>;
			ranges;
			status = "disabled";

			usb_dwc3_1: usb@38200000 {
				compatible = "snps,dwc3";
				reg = <0x38200000 0x10000>;
				clocks = <&clk IMX8MP_CLK_USB_ROOT>,
					 <&clk IMX8MP_CLK_USB_CORE_REF>,
					 <&clk IMX8MP_CLK_USB_SUSP>;
				clock-names = "bus_early", "ref", "suspend";
				interrupts = <GIC_SPI 41 IRQ_TYPE_LEVEL_HIGH>;
				phys = <&usb3_phy1>, <&usb3_phy1>;
				phy-names = "usb2-phy", "usb3-phy";
				snps,gfladj-refclk-lpm-sel-quirk;
				snps,parkmode-disable-ss-quirk;
			};
		};

		dsp: dsp@3b6e8000 {
			compatible = "fsl,imx8mp-hifi4";
			reg = <0x3B6E8000 0x88000>;
			clocks = <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_OCRAMA_IPG>,
				 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_DSP_ROOT>,
				 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_DSPDBG_ROOT>;
			clock-names = "ocram", "core", "debug";
			firmware-name = "imx/dsp/hifi4.bin";
			power-domains = <&audiomix_pd>;
			mbox-names = "tx", "rx", "rxdb";
			mboxes = <&mu2 0 0>,
				 <&mu2 1 0>,
				 <&mu2 3 0>;
			memory-region = <&dsp_vdev0buffer>, <&dsp_vdev0vring0>,
					<&dsp_vdev0vring1>, <&dsp_reserved>;
			fsl,dsp-ctrl = <&audio_blk_ctrl>;
			status = "disabled";
		};
	};

	ldb: ldb-display-controller {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "fsl,imx8mp-ldb";
		clocks = <&clk IMX8MP_CLK_MEDIA_LDB_ROOT>;
		clock-names = "ldb";
		assigned-clocks = <&clk IMX8MP_CLK_MEDIA_LDB>;
		assigned-clock-parents = <&clk IMX8MP_VIDEO_PLL1_OUT>;
		gpr = <&media_blk_ctrl>;
		power-domains = <&pgc_mediamix>;
		status = "disabled";

		lvds-channel@0 {
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0>;
			phys = <&ldb_phy1>;
			phy-names = "ldb_phy";
			status = "disabled";

			port@0 {
				reg = <0>;

				ldb_ch0: endpoint {
					remote-endpoint = <&lcdif2_disp_ldb_ch0>;
				};
			};
		};

		lvds-channel@1 {
			reg = <1>;
			phys = <&ldb_phy2>;
			phy-names = "ldb_phy";
			status = "disabled";

			port {
				ldb_ch1: endpoint {
					remote-endpoint = <&lcdif2_disp_ldb_ch1>;
				};
			};
		};
	};

	ldb_phy: phy-lvds {
		compatible = "fsl,imx8mp-lvds-phy";
		#address-cells = <1>;
		#size-cells = <0>;
		gpr = <&media_blk_ctrl>;
		clocks = <&clk IMX8MP_CLK_MEDIA_APB_ROOT>;
		clock-names = "apb";
		power-domains = <&pgc_mediamix>;
		status = "disabled";

		ldb_phy1: port@0 {
			reg = <0>;
			#phy-cells = <0>;
		};

		ldb_phy2: port@1 {
			reg = <1>;
			#phy-cells = <0>;
		};
	};

	vpu_g1: vpu_g1@38300000 {
		compatible = "nxp,imx8mm-hantro","nxp,imx8mp-hantro";
		reg = <0x0 0x38300000 0x0 0x100000>;
		reg-names = "regs_hantro";
		interrupts = <GIC_SPI 7 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_hantro";
		clocks = <&clk IMX8MP_CLK_VPU_G1_ROOT>, <&clk IMX8MP_CLK_VPU_ROOT>;
		clock-names = "clk_hantro", "clk_hantro_bus";
		assigned-clocks = <&clk IMX8MP_CLK_VPU_G1>, <&clk IMX8MP_CLK_VPU_BUS>;
		assigned-clock-parents = <&clk IMX8MP_SYS_PLL1_800M>, <&clk IMX8MP_SYS_PLL1_800M>;
		assigned-clock-rates = <800000000>, <800000000>;
		power-domains = <&vpumix_blk_ctrl IMX8MP_VPUBLK_PD_G1>;
		status = "disabled";
	};

	vpu_g2: vpu_g2@38310000 {
		compatible = "nxp,imx8mm-hantro","nxp,imx8mp-hantro";
		reg = <0x0 0x38310000 0x0 0x100000>;
		reg-names = "regs_hantro";
		interrupts = <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_hantro";
		clocks = <&clk IMX8MP_CLK_VPU_G2_ROOT>, <&clk IMX8MP_CLK_VPU_ROOT>;
		clock-names = "clk_hantro", "clk_hantro_bus";
		assigned-clocks = <&clk IMX8MP_CLK_VPU_G2>, <&clk IMX8MP_CLK_VPU_BUS>, <&clk IMX8MP_VPU_PLL>;
		assigned-clock-parents = <&clk IMX8MP_VPU_PLL_OUT>, <&clk IMX8MP_SYS_PLL1_800M>;
		assigned-clock-rates = <700000000>, <800000000>, <700000000>;
		power-domains = <&vpumix_blk_ctrl IMX8MP_VPUBLK_PD_G2>;
		status = "disabled";
	};

	vpu_vc8000e: vpu_vc8000e@38320000 {
		compatible = "nxp,imx8mp-hantro-vc8000e";
		reg = <0x0 0x38320000 0x0 0x10000>;
		reg-names = "regs_hantro_vc8000e";
		interrupts = <GIC_SPI 30 IRQ_TYPE_LEVEL_HIGH>;
		interrupt-names = "irq_hantro_vc8000e";
		clocks = <&clk IMX8MP_CLK_VPU_VC8KE_ROOT>, <&clk IMX8MP_CLK_VPU_ROOT>;
		clock-names = "clk_hantro_vc8000e", "clk_hantro_vc8000e_bus";
		assigned-clocks = <&clk IMX8MP_CLK_VPU_VC8000E>,<&clk IMX8MP_CLK_VPU_BUS>;
		assigned-clock-parents = <&clk IMX8MP_SYS_PLL2_1000M>, <&clk IMX8MP_SYS_PLL1_800M>;
		assigned-clock-rates = <500000000>, <800000000>;
		power-domains = <&vpumix_blk_ctrl IMX8MP_VPUBLK_PD_VC8000E>;
		status = "disabled";
	};

	vpu_v4l2: vpu_v4l2 {
		compatible = "nxp,imx8m-vsiv4l2";
		status = "disabled";
	};

	gpu_3d: gpu3d@38000000 {
		compatible = "fsl,imx8-gpu";
		reg = <0x0 0x38000000 0x0 0x8000>;
		interrupts = <GIC_SPI 3 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&clk IMX8MP_CLK_GPU3D_ROOT>,
			 <&clk IMX8MP_CLK_GPU3D_SHADER_CORE>,
			 <&clk IMX8MP_CLK_GPU_AXI>,
			 <&clk IMX8MP_CLK_GPU_AHB>;
		clock-names = "core", "shader", "axi", "ahb";
		assigned-clocks = <&clk IMX8MP_CLK_GPU3D_CORE>,
				  <&clk IMX8MP_CLK_GPU3D_SHADER_CORE>,
				  <&clk IMX8MP_CLK_GPU_AXI>,
				  <&clk IMX8MP_CLK_GPU_AHB>;
		assigned-clock-parents = <&clk IMX8MP_SYS_PLL2_1000M>,
					 <&clk IMX8MP_SYS_PLL2_1000M>,
					 <&clk IMX8MP_SYS_PLL1_800M>,
					 <&clk IMX8MP_SYS_PLL1_800M>;
		assigned-clock-rates = <1000000000>, <1000000000>,
				       <800000000>, <400000000>;
		power-domains = <&pgc_gpu3d>;
		status = "disabled";
	};

	gpu_2d: gpu2d@38008000 {
		compatible = "fsl,imx8-gpu";
		reg = <0x0 0x38008000 0x0 0x8000>;
		interrupts = <GIC_SPI 25 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&clk IMX8MP_CLK_GPU2D_ROOT>,
			 <&clk IMX8MP_CLK_GPU_AXI>,
			 <&clk IMX8MP_CLK_GPU_AHB>;
		clock-names = "core", "axi", "ahb";
		assigned-clocks = <&clk IMX8MP_CLK_GPU2D_CORE>,
				  <&clk IMX8MP_CLK_GPU_AXI>,
				  <&clk IMX8MP_CLK_GPU_AHB>;
		assigned-clock-parents = <&clk IMX8MP_SYS_PLL2_1000M>,
					 <&clk IMX8MP_SYS_PLL1_800M>,
					 <&clk IMX8MP_SYS_PLL1_800M>;
		assigned-clock-rates = <1000000000>, <800000000>, <400000000>;
		power-domains = <&pgc_gpu2d>;
		status = "disabled";
	};

	ml_vipsi: vipsi@38500000 {
		compatible = "fsl,imx8-gpu", "fsl,imx8-vipsi";
		reg = <0x0 0x38500000 0x0 0x20000>;
		interrupts = <GIC_SPI 13 IRQ_TYPE_LEVEL_HIGH>;
		clocks = <&clk IMX8MP_CLK_NPU_ROOT>,
				<&clk IMX8MP_CLK_NPU_ROOT>,
				<&clk IMX8MP_CLK_ML_AXI>,
				<&clk IMX8MP_CLK_ML_AHB>;
		clock-names = "core", "shader", "axi", "ahb";
		assigned-clocks = <&clk IMX8MP_CLK_ML_CORE>,
				  <&clk IMX8MP_CLK_ML_AXI>,
				  <&clk IMX8MP_CLK_ML_AHB>;
		assigned-clock-parents = <&clk IMX8MP_SYS_PLL2_1000M>,
					 <&clk IMX8MP_SYS_PLL1_800M>,
					 <&clk IMX8MP_SYS_PLL1_800M>;
		assigned-clock-rates = <1000000000>, <800000000>, <400000000>;
		power-domains = <&pgc_mlmix>;
		status = "disabled";
	};

	mix_gpu_ml: mix_gpu_ml@40000000 {
		compatible = "fsl,imx8mp-gpu", "fsl,imx8-gpu-ss";
		cores = <&gpu_3d>, <&ml_vipsi>, <&gpu_2d>;
		reg = <0x0 0x40000000 0x0 0xC0000000>, <0x0 0x0 0x0 0x10000000>;
		reg-names = "phys_baseaddr", "contiguous_mem";
		memory-region=<&gpu_reserved>;
		status = "disabled";

		throttle,max_state = <1>;
		#cooling-cells = <2>;
	};

	i2c_rpbus_3: i2c-rpbus-3 {
		compatible = "fsl,i2c-rpbus";
		status = "disabled";
	};
};
