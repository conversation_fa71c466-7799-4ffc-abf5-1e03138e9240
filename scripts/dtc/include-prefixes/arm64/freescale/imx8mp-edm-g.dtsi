// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright 2020 Technexion Ltd.
 *
 * Author: <PERSON> <<EMAIL>>
 *
 */

/dts-v1/;

#include <dt-bindings/usb/pd.h>
#include "imx8mp.dtsi"

/ {
	memory@40000000 {
		device_type = "memory";
		reg = <0x0 0x40000000 0 0xc0000000>,
		      <0x1 0x00000000 0 0xc0000000>;
	};

	chosen {
		stdout-path = &uart2;
	};

	enet_power_en: enet_power_en {
		pinctrl-names = "default";
		pinctrl-0 = <&pinctrl_enet_pwr>;
		compatible = "regulator-fixed";
		regulator-name = "enet_pwr";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio1 11 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		regulator-always-on;
	};

	reg_usdhc2_vmmc: regulator-usdhc2 {
		compatible = "regulator-fixed";
		regulator-name = "VSD_3V3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio2 19 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		startup-delay-us = <100>;
		off-on-delay-us = <12000>;
	};

	wl_reg_on: wlreg_on {
		compatible = "regulator-fixed";
		pinctrl-names = "default";
		pinctrl-0 = <&pinctrl_wifi_ctrl>;
		regulator-name = "WL_REG_ON";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio1 0 GPIO_ACTIVE_HIGH>;
		off-on-delay = <20000>;
		startup-delay-us = <100>;
		enable-active-high;
	};

	bt_reg_on: btreg_on {
		compatible = "gpio-reset";
		pinctrl-names = "default";
		pinctrl-0 = <&pinctrl_bt_ctrl>;
		reset-gpios = <&gpio1 5 GPIO_ACTIVE_LOW>;
		reset-delay-us = <2000>;
		reset-post-delay-ms = <200>;
		#reset-cells = <0>;
	};

	sound-hdmi {
		compatible = "fsl,imx-audio-hdmi";
		model = "audio-hdmi";
		audio-cpu = <&aud2htx>;
		hdmi-out;
		constraint-rate = <44100>,
				<88200>,
				<176400>,
				<32000>,
				<48000>,
				<96000>,
				<192000>;
		status = "okay";
	};

	i2c_gpio_brd_conf: i2c@0 {
		compatible = "i2c-gpio";
		#address-cells = <1>;
		#size-cells = <0>;
		pinctrl-names = "default";
		pinctrl-0 = <&pinctrl_i2c_brd_conf>;
		gpios = <&gpio4 29 GPIO_ACTIVE_HIGH /* SDA */
				&gpio4 28 GPIO_ACTIVE_HIGH /* SCL */
		>;
		clock-frequency = <100000>;
		status = "okay";
	};
};

&aud2htx {
	status = "okay";
};

&A53_0 {
	cpu-supply = <&buck2>;
};

&A53_1 {
	cpu-supply = <&buck2>;
};

&A53_2 {
	cpu-supply = <&buck2>;
};

&A53_3 {
	cpu-supply = <&buck2>;
};

&pwm1 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm1>;
	status = "okay";
};

&pwm2 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm2>;
	status = "okay";
};

&pwm3 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm3>;
	status = "okay";
};

&pwm4 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pwm4>;
	status = "okay";
};

&ecspi1 {
	#address-cells = <1>;
	#size-cells = <0>;
	num-cs = <1>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_ecspi1 &pinctrl_ecspi1_cs>;
	cs-gpios = <&gpio5 9 GPIO_ACTIVE_LOW>;
	status = "okay";

	spidev1: spi@0 {
		reg = <0>;
		compatible = "rohm,dh2228fv";
		spi-max-frequency = <500000>;
	};
};

&eqos {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_eqos>;
	phy-mode = "rgmii-id";
	phy-handle = <&ethphy0>;
	status = "okay";

	mdio {
		compatible = "snps,dwmac-mdio";
		#address-cells = <1>;
		#size-cells = <0>;

		ethphy0: ethernet-phy@1 {
			/* Atheros AR8035 PHY */
			compatible = "ethernet-phy-ieee802.3-c22";
			reg = <1>;
			reset-gpios = <&gpio1 9 GPIO_ACTIVE_LOW>;
			reset-assert-us = <35000>;
			reset-deassert-us = <75000>;
			eee-broken-1000t;
			at803x,eee-disabled;
			at803x,vddio-1p8v;
			realtek,aldps-disable;
			realtek,clkout-disable;
		};
	};
};

&i2c1 {
	clock-frequency = <100000>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_i2c1>;
	status = "okay";

	pmic: pca9450@25 {
		reg = <0x25>;
		compatible = "nxp,pca9450c";
		/* PMIC PCA9450 PMIC_nINT GPIO1_IO3 */
		pinctrl-0 = <&pinctrl_pmic>;
		interrupt-parent = <&gpio1>;
		interrupts = <3 GPIO_ACTIVE_LOW>;

		regulators {
			buck1: BUCK1 {
				regulator-name = "BUCK1";
				regulator-min-microvolt = <600000>;
				regulator-max-microvolt = <2187500>;
				regulator-boot-on;
				regulator-always-on;
				regulator-ramp-delay = <3125>;
			};

			buck2: BUCK2 {
				regulator-name = "BUCK2";
				regulator-min-microvolt = <600000>;
				regulator-max-microvolt = <2187500>;
				regulator-boot-on;
				regulator-always-on;
				regulator-ramp-delay = <3125>;
				nxp,dvs-run-voltage = <950000>;
				nxp,dvs-standby-voltage = <850000>;
			};

			buck4: BUCK4{
				regulator-name = "BUCK4";
				regulator-min-microvolt = <600000>;
				regulator-max-microvolt = <3400000>;
				regulator-boot-on;
				regulator-always-on;
			};

			buck5: BUCK5{
				regulator-name = "BUCK5";
				regulator-min-microvolt = <600000>;
				regulator-max-microvolt = <3400000>;
				regulator-boot-on;
				regulator-always-on;
			};

			buck6: BUCK6 {
				regulator-name = "BUCK6";
				regulator-min-microvolt = <600000>;
				regulator-max-microvolt = <3400000>;
				regulator-boot-on;
				regulator-always-on;
			};

			ldo1: LDO1 {
				regulator-name = "LDO1";
				regulator-min-microvolt = <1600000>;
				regulator-max-microvolt = <3300000>;
				regulator-boot-on;
				regulator-always-on;
			};

			ldo2: LDO2 {
				regulator-name = "LDO2";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <1150000>;
				regulator-boot-on;
				regulator-always-on;
			};

			ldo3: LDO3 {
				regulator-name = "LDO3";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3300000>;
				regulator-boot-on;
				regulator-always-on;
			};

			ldo4: LDO4 {
				regulator-name = "LDO4";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3300000>;
				regulator-boot-on;
				regulator-always-on;
			};

			ldo5: LDO5 {
				regulator-name = "LDO5";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <3300000>;
				regulator-boot-on;
				regulator-always-on;
			};
		};
	};
};

/* I2C_B on EDMG */
&i2c2 {
	clock-frequency = <400000>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_i2c2>;
	status = "okay";
};

/* MCORE on EDMG */
&i2c3 {
	clock-frequency = <100000>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_i2c3>;
	status = "okay";
};

/* I2C_A on EDMG */
&i2c4 {
	clock-frequency = <100000>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_i2c4>;
	status = "okay";
};

/* I2C_C on EDMG */
&i2c5 {
	clock-frequency = <400000>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_i2c5>;
	status = "okay";
};

&irqsteer_hdmi {
	status = "okay";
};

&hdmi_blk_ctrl {
	status = "okay";
};

&flexcan1 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_flexcan1>;
	status = "disabled";
};

&flexcan2 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_flexcan2>;
	status = "disabled";
};

&hdmi_pavi {
	status = "okay";
};

&hdmi {
	status = "okay";
};

&hdmiphy {
	status = "okay";
};

&lcdif1 {
	status = "okay";
};

&lcdif2 {
	status = "okay";
};

&lcdif3 {
	status = "okay";
};

&ldb {
	status = "disabled";
};

&ldb_phy {
	status = "okay";
};

&mipi_dsi {
	status = "okay";
};

&easrc {
	fsl,asrc-rate  = <48000>;
	status = "okay";
};

&pcie{
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_pcie>;
	reset-gpio = <&gpio1 1 GPIO_ACTIVE_LOW>;
	clocks = <&clk IMX8MP_CLK_HSIO_ROOT>,
		 <&clk IMX8MP_CLK_PCIE_ROOT>,
		 <&clk IMX8MP_CLK_HSIO_AXI>;
	clock-names = "pcie", "pcie_aux", "pcie_bus";
	assigned-clocks = <&clk IMX8MP_CLK_PCIE_AUX>;
	assigned-clock-rates = <10000000>;
	assigned-clock-parents = <&clk IMX8MP_SYS_PLL2_50M>;
	status = "okay";
};

&pcie_phy{
	status = "okay";
};

/* AUD_B on EDMG */
&sai2 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_sai2>;
	assigned-clocks = <&clk IMX8MP_CLK_SAI2>;
	assigned-clock-parents = <&clk IMX8MP_AUDIO_PLL1_OUT>;
	assigned-clock-rates = <12288000>;
	clocks = <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI2_IPG>, <&clk IMX8MP_CLK_DUMMY>,
		 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI2_MCLK1>, <&clk IMX8MP_CLK_DUMMY>,
		 <&clk IMX8MP_CLK_DUMMY>;
	clock-names = "bus", "mclk0", "mclk1", "mclk2", "mclk3";
	fsl,sai-mclk-direction-output;
	status = "okay";
};

/* AUD_A on EDMG */
&sai3 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_sai3>;
	assigned-clocks = <&clk IMX8MP_CLK_SAI3>;
	assigned-clock-parents = <&clk IMX8MP_AUDIO_PLL1_OUT>;
	assigned-clock-rates = <12288000>;
	clocks = <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI3_IPG>, <&clk IMX8MP_CLK_DUMMY>,
		 <&audio_blk_ctrl IMX8MP_CLK_AUDIOMIX_SAI3_MCLK1>, <&clk IMX8MP_CLK_DUMMY>,
		 <&clk IMX8MP_CLK_DUMMY>;
	clock-names = "bus", "mclk0", "mclk1", "mclk2", "mclk3";
	fsl,sai-mclk-direction-output;
	status = "okay";
};

&sdma2 {
	status = "okay";
};

/* BT */
&uart1 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_uart1>;
	assigned-clocks = <&clk IMX8MP_CLK_UART1>;
	assigned-clock-parents = <&clk IMX8MP_SYS_PLL1_80M>;
	fsl,uart-has-rtscts;
	resets = <&bt_reg_on>;
	status = "okay";
};

/* UART_A on EDMG, console */
&uart2 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_uart2>;
	status = "okay";
};

/* UART_C on EDMG */
&uart3 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_uart3>;
	assigned-clocks = <&clk IMX8MP_CLK_UART3>;
	assigned-clock-parents = <&clk IMX8MP_SYS_PLL1_80M>;
	fsl,uart-has-rtscts;
	status = "okay";
};

/* UART_B on EDMG */
&uart4 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_uart4>;
	assigned-clocks = <&clk IMX8MP_CLK_UART4>;
	assigned-clock-parents = <&clk IMX8MP_SYS_PLL1_80M>;
	fsl,uart-has-rtscts;
	status = "okay";
};

&usb3_phy0 {
	fsl,phy-tx-vref-tune = <0xe>;
	fsl,phy-tx-preemp-amp-tune = <3>;
	fsl,phy-tx-vboost-level = <5>;
	fsl,phy-comp-dis-tune = <7>;
	fsl,pcs-tx-deemph-3p5db = <0x21>;
	fsl,phy-pcs-tx-swing-full = <0x7f>;
	status = "okay";
};

&usb3_0 {
	status = "okay";
};

&usb_dwc3_0 {
	dr_mode = "otg";
	hnp-disable;
	srp-disable;
	adp-disable;
	status = "okay";
};

&usb3_phy1 {
	fsl,phy-tx-preemp-amp-tune = <3>;
	fsl,phy-tx-vref-tune = <0xb>;
	status = "okay";
};

&usb3_1 {
	status = "okay";
};

&usb_dwc3_1 {
	dr_mode = "host";
	status = "okay";
};

/* WIFI SDIO */
&usdhc1 {
	assigned-clocks = <&clk IMX8MP_CLK_USDHC1>;
	assigned-clock-rates = <133333333>;
	pinctrl-names = "default", "state_100mhz", "state_200mhz";
	pinctrl-0 = <&pinctrl_usdhc1>;
	pinctrl-1 = <&pinctrl_usdhc1_100mhz>;
	pinctrl-2 = <&pinctrl_usdhc1_200mhz>;
	vmmc-supply = <&wl_reg_on>;
	bus-width = <4>;
	pm-ignore-notify;
	keep-power-in-suspend;
	non-removable;
	fsl,sdio-async-interrupt-enabled;
	status = "okay";
};

/* SD card on baseboard */
&usdhc2 {
	assigned-clocks = <&clk IMX8MP_CLK_USDHC2>;
	assigned-clock-rates = <400000000>;
	pinctrl-names = "default", "state_100mhz", "state_200mhz";
	pinctrl-0 = <&pinctrl_usdhc2>, <&pinctrl_usdhc2_gpio>;
	pinctrl-1 = <&pinctrl_usdhc2_100mhz>, <&pinctrl_usdhc2_gpio>;
	pinctrl-2 = <&pinctrl_usdhc2_200mhz>, <&pinctrl_usdhc2_gpio>;
	cd-gpios = <&gpio2 12 GPIO_ACTIVE_LOW>;
	vmmc-supply = <&reg_usdhc2_vmmc>;
	bus-width = <4>;
	status = "okay";
};

/* eMMC on SOM */
&usdhc3 {
	assigned-clocks = <&clk IMX8MP_CLK_USDHC3>;
	assigned-clock-rates = <400000000>;
	pinctrl-names = "default", "state_100mhz", "state_200mhz";
	pinctrl-0 = <&pinctrl_usdhc3>;
	pinctrl-1 = <&pinctrl_usdhc3_100mhz>;
	pinctrl-2 = <&pinctrl_usdhc3_200mhz>;
	bus-width = <8>;
	non-removable;
	status = "okay";
};

&wdog1 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_wdog>;
	fsl,ext-reset-output;
	status = "okay";
};

&iomuxc {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_hog>;

	pinctrl_hog: hoggrp {
		fsl,pins = <
			MX8MP_IOMUXC_HDMI_DDC_SCL__HDMIMIX_HDMI_SCL	0x400001c3
			MX8MP_IOMUXC_HDMI_DDC_SDA__HDMIMIX_HDMI_SDA	0x400001c3
			MX8MP_IOMUXC_HDMI_HPD__HDMIMIX_HDMI_HPD		0x40000019
			MX8MP_IOMUXC_HDMI_CEC__HDMIMIX_HDMI_CEC		0x40000019
		>;
	};

	pinctrl_pwm1: pwm1grp {
		fsl,pins = <
			MX8MP_IOMUXC_SPDIF_EXT_CLK__PWM1_OUT	0x116
		>;
	};

	pinctrl_pwm2: pwm2grp {
		fsl,pins = <
			MX8MP_IOMUXC_SAI5_RXD0__PWM2_OUT	0x116
		>;
	};

	pinctrl_pwm3: pwm3grp {
		fsl,pins = <
			MX8MP_IOMUXC_SAI5_RXC__PWM3_OUT		0x116
		>;
	};

	pinctrl_pwm4: pwm4grp {
		fsl,pins = <
			MX8MP_IOMUXC_SAI5_RXFS__PWM4_OUT	0x116
		>;
	};

	pinctrl_ecspi1: ecspi1grp {
		fsl,pins = <
			MX8MP_IOMUXC_ECSPI1_SCLK__ECSPI1_SCLK		0x82
			MX8MP_IOMUXC_ECSPI1_MOSI__ECSPI1_MOSI		0x82
			MX8MP_IOMUXC_ECSPI1_MISO__ECSPI1_MISO		0x82
		>;
	};

	pinctrl_ecspi1_cs: ecspi1cs {
		fsl,pins = <
			MX8MP_IOMUXC_ECSPI1_SS0__GPIO5_IO09		0x40000
		>;
	};

	pinctrl_eqos: eqosgrp {
		fsl,pins = <
			MX8MP_IOMUXC_ENET_MDC__ENET_QOS_MDC	0x3
			MX8MP_IOMUXC_ENET_MDIO__ENET_QOS_MDIO	0x23
			MX8MP_IOMUXC_ENET_RD0__ENET_QOS_RGMII_RD0	0x91
			MX8MP_IOMUXC_ENET_RD1__ENET_QOS_RGMII_RD1	0x91
			MX8MP_IOMUXC_ENET_RD2__ENET_QOS_RGMII_RD2	0x91
			MX8MP_IOMUXC_ENET_RD3__ENET_QOS_RGMII_RD3	0x91
			MX8MP_IOMUXC_ENET_RXC__CCM_ENET_QOS_CLOCK_GENERATE_RX_CLK	0x91
			MX8MP_IOMUXC_ENET_RX_CTL__ENET_QOS_RGMII_RX_CTL	0x91
			MX8MP_IOMUXC_ENET_TD0__ENET_QOS_RGMII_TD0		0x1f
			MX8MP_IOMUXC_ENET_TD1__ENET_QOS_RGMII_TD1		0x1f
			MX8MP_IOMUXC_ENET_TD2__ENET_QOS_RGMII_TD2		0x1f
			MX8MP_IOMUXC_ENET_TD3__ENET_QOS_RGMII_TD3		0x1f
			MX8MP_IOMUXC_ENET_TX_CTL__ENET_QOS_RGMII_TX_CTL	0x1f
			MX8MP_IOMUXC_ENET_TXC__CCM_ENET_QOS_CLOCK_GENERATE_TX_CLK	0x1f
			MX8MP_IOMUXC_GPIO1_IO09__GPIO1_IO09		0x19
			MX8MP_IOMUXC_GPIO1_IO12__GPIO1_IO12		0x19
		>;
	};

	pinctrl_flexcan1: flexcan1grp {
		fsl,pins = <
			MX8MP_IOMUXC_SAI5_RXD2__CAN1_RX		0x154
			MX8MP_IOMUXC_SAI5_RXD1__CAN1_TX		0x154
		>;
	};

	pinctrl_flexcan2: flexcan2grp {
		fsl,pins = <
			MX8MP_IOMUXC_SAI5_MCLK__CAN2_RX		0x154
			MX8MP_IOMUXC_SAI5_RXD3__CAN2_TX		0x154
		>;
	};

	pinctrl_i2c1: i2c1grp {
		fsl,pins = <
			MX8MP_IOMUXC_I2C1_SCL__I2C1_SCL		0x400001a3
			MX8MP_IOMUXC_I2C1_SDA__I2C1_SDA		0x400001a3
		>;
	};

	pinctrl_i2c2: i2c2grp {
		fsl,pins = <
			MX8MP_IOMUXC_I2C2_SCL__I2C2_SCL			0x400001a3
			MX8MP_IOMUXC_I2C2_SDA__I2C2_SDA			0x400001a3
		>;
	};

	pinctrl_i2c3: i2c3grp {
		fsl,pins = <
			MX8MP_IOMUXC_I2C3_SCL__I2C3_SCL			0x400001c3
			MX8MP_IOMUXC_I2C3_SDA__I2C3_SDA			0x400001c3
		>;
	};

	pinctrl_i2c4: i2c4grp {
		fsl,pins = <
			MX8MP_IOMUXC_I2C4_SCL__I2C4_SCL			0x400001c3
			MX8MP_IOMUXC_I2C4_SDA__I2C4_SDA			0x400001c3
		>;
	};

	pinctrl_i2c5: i2c5grp {
		fsl,pins = <
			MX8MP_IOMUXC_SPDIF_TX__I2C5_SCL			0x400001a3
			MX8MP_IOMUXC_SPDIF_RX__I2C5_SDA			0x400001a3
		>;
	};

	pinctrl_pcie: pciegrp {
		fsl,pins = <
			MX8MP_IOMUXC_GPIO1_IO13__GPIO1_IO13		0x41 /* PCIE CLKREQ */
			MX8MP_IOMUXC_SAI2_RXFS__GPIO4_IO21		0x41 /* PCIE WAKE */
			MX8MP_IOMUXC_GPIO1_IO01__GPIO1_IO01		0x41 /* PCIE RST */
		>;
	};

	pinctrl_pmic: pmicirq {
		fsl,pins = <
			MX8MP_IOMUXC_GPIO1_IO03__GPIO1_IO03	0x41
		>;
	};

	pinctrl_typec_mux: typec1muxgrp {
		fsl,pins = <
			MX8MP_IOMUXC_SAI1_RXC__GPIO4_IO01	0x16
		>;
	};

	pinctrl_sai2: sai2grp {
		fsl,pins = <
			MX8MP_IOMUXC_SAI2_MCLK__AUDIOMIX_SAI2_MCLK		0xd6
			MX8MP_IOMUXC_SAI2_TXFS__AUDIOMIX_SAI2_TX_SYNC	0xd6
			MX8MP_IOMUXC_SAI2_TXC__AUDIOMIX_SAI2_TX_BCLK	0xd6
			MX8MP_IOMUXC_SAI2_RXD0__AUDIOMIX_SAI2_RX_DATA00	0xd6
			MX8MP_IOMUXC_SAI2_TXD0__AUDIOMIX_SAI2_TX_DATA00	0xd6
		>;
	};

	pinctrl_sai3: sai3grp {
		fsl,pins = <
			MX8MP_IOMUXC_SAI3_MCLK__AUDIOMIX_SAI3_MCLK		0xd6
			MX8MP_IOMUXC_SAI3_TXFS__AUDIOMIX_SAI3_TX_SYNC	0xd6
			MX8MP_IOMUXC_SAI3_TXC__AUDIOMIX_SAI3_TX_BCLK	0xd6
			MX8MP_IOMUXC_SAI3_RXD__AUDIOMIX_SAI3_RX_DATA00	0xd6
			MX8MP_IOMUXC_SAI3_TXD__AUDIOMIX_SAI3_TX_DATA00	0xd6
		>;
	};

	pinctrl_uart1: uart1grp {
		fsl,pins = <
			MX8MP_IOMUXC_UART1_RXD__UART1_DCE_RX	0x140
			MX8MP_IOMUXC_UART1_TXD__UART1_DCE_TX	0x140
			MX8MP_IOMUXC_UART3_RXD__UART1_DCE_CTS	0x140
			MX8MP_IOMUXC_UART3_TXD__UART1_DCE_RTS	0x140
		>;
	};

	pinctrl_uart2: uart2grp {
		fsl,pins = <
			MX8MP_IOMUXC_UART2_RXD__UART2_DCE_RX	0x140
			MX8MP_IOMUXC_UART2_TXD__UART2_DCE_TX	0x140
			MX8MP_IOMUXC_UART4_RXD__UART2_DCE_CTS	0x140
			MX8MP_IOMUXC_UART4_TXD__UART2_DCE_RTS	0x140
		>;
	};

	pinctrl_uart3: uart3grp {
		fsl,pins = <
			MX8MP_IOMUXC_SD1_DATA7__UART3_DCE_RX		0x140
			MX8MP_IOMUXC_SD1_DATA6__UART3_DCE_TX		0x140
			MX8MP_IOMUXC_SD1_STROBE__UART3_DCE_CTS		0x140
			MX8MP_IOMUXC_SD1_RESET_B__UART3_DCE_RTS		0x140
		>;
	};

	pinctrl_uart4: uart4grp {
		fsl,pins = <
			MX8MP_IOMUXC_ECSPI2_SCLK__UART4_DCE_RX		0x140
			MX8MP_IOMUXC_ECSPI2_MOSI__UART4_DCE_TX		0x140
			MX8MP_IOMUXC_ECSPI2_MISO__UART4_DCE_CTS		0x140
			MX8MP_IOMUXC_ECSPI2_SS0__UART4_DCE_RTS		0x140
		>;
	};

	pinctrl_usdhc1: usdhc1grp {
		fsl,pins = <
			MX8MP_IOMUXC_SD1_CLK__USDHC1_CLK	0x190
			MX8MP_IOMUXC_SD1_CMD__USDHC1_CMD	0x1d0
			MX8MP_IOMUXC_SD1_DATA0__USDHC1_DATA0	0x1d0
			MX8MP_IOMUXC_SD1_DATA1__USDHC1_DATA1	0x1d0
			MX8MP_IOMUXC_SD1_DATA2__USDHC1_DATA2	0x1d0
			MX8MP_IOMUXC_SD1_DATA3__USDHC1_DATA3	0x1d0
		>;
	};

	pinctrl_usdhc1_100mhz: usdhc1grp-100mhz {
		fsl,pins = <
			MX8MP_IOMUXC_SD1_CLK__USDHC1_CLK	0x194
			MX8MP_IOMUXC_SD1_CMD__USDHC1_CMD	0x1d4
			MX8MP_IOMUXC_SD1_DATA0__USDHC1_DATA0	0x1d4
			MX8MP_IOMUXC_SD1_DATA1__USDHC1_DATA1	0x1d4
			MX8MP_IOMUXC_SD1_DATA2__USDHC1_DATA2	0x1d4
			MX8MP_IOMUXC_SD1_DATA3__USDHC1_DATA3	0x1d4
		>;
	};

	pinctrl_usdhc1_200mhz: usdhc1grp-200mhz {
		fsl,pins = <
			MX8MP_IOMUXC_SD1_CLK__USDHC1_CLK	0x196
			MX8MP_IOMUXC_SD1_CMD__USDHC1_CMD	0x1d6
			MX8MP_IOMUXC_SD1_DATA0__USDHC1_DATA0	0x1d6
			MX8MP_IOMUXC_SD1_DATA1__USDHC1_DATA1	0x1d6
			MX8MP_IOMUXC_SD1_DATA2__USDHC1_DATA2	0x1d6
			MX8MP_IOMUXC_SD1_DATA3__USDHC1_DATA3	0x1d6
		>;
	};

	pinctrl_usdhc2_gpio: usdhc2grp-gpio {
		fsl,pins = <
			MX8MP_IOMUXC_SD2_CD_B__GPIO2_IO12 	0x1c4
			MX8MP_IOMUXC_SD2_RESET_B__GPIO2_IO19	0x41
		>;
	};

	pinctrl_usdhc2: usdhc2grp {
		fsl,pins = <
			MX8MP_IOMUXC_SD2_CLK__USDHC2_CLK	0x190
			MX8MP_IOMUXC_SD2_CMD__USDHC2_CMD	0x1d0
			MX8MP_IOMUXC_SD2_DATA0__USDHC2_DATA0	0x1d0
			MX8MP_IOMUXC_SD2_DATA1__USDHC2_DATA1	0x1d0
			MX8MP_IOMUXC_SD2_DATA2__USDHC2_DATA2	0x1d0
			MX8MP_IOMUXC_SD2_DATA3__USDHC2_DATA3	0x1d0
			MX8MP_IOMUXC_GPIO1_IO04__USDHC2_VSELECT	0xc1
		>;
	};

	pinctrl_usdhc2_100mhz: usdhc2grp-100mhz {
		fsl,pins = <
			MX8MP_IOMUXC_SD2_CLK__USDHC2_CLK	0x194
			MX8MP_IOMUXC_SD2_CMD__USDHC2_CMD	0x1d4
			MX8MP_IOMUXC_SD2_DATA0__USDHC2_DATA0	0x1d4
			MX8MP_IOMUXC_SD2_DATA1__USDHC2_DATA1	0x1d4
			MX8MP_IOMUXC_SD2_DATA2__USDHC2_DATA2	0x1d4
			MX8MP_IOMUXC_SD2_DATA3__USDHC2_DATA3	0x1d4
			MX8MP_IOMUXC_GPIO1_IO04__USDHC2_VSELECT 0xc1
		>;
	};

	pinctrl_usdhc2_200mhz: usdhc2grp-200mhz {
		fsl,pins = <
			MX8MP_IOMUXC_SD2_CLK__USDHC2_CLK	0x196
			MX8MP_IOMUXC_SD2_CMD__USDHC2_CMD	0x1d6
			MX8MP_IOMUXC_SD2_DATA0__USDHC2_DATA0	0x1d6
			MX8MP_IOMUXC_SD2_DATA1__USDHC2_DATA1	0x1d6
			MX8MP_IOMUXC_SD2_DATA2__USDHC2_DATA2	0x1d6
			MX8MP_IOMUXC_SD2_DATA3__USDHC2_DATA3	0x1d6
			MX8MP_IOMUXC_GPIO1_IO04__USDHC2_VSELECT 0xc1
		>;
	};

	pinctrl_usdhc3: usdhc3grp {
		fsl,pins = <
			MX8MP_IOMUXC_NAND_WE_B__USDHC3_CLK	0x190
			MX8MP_IOMUXC_NAND_WP_B__USDHC3_CMD	0x1d0
			MX8MP_IOMUXC_NAND_DATA04__USDHC3_DATA0	0x1d0
			MX8MP_IOMUXC_NAND_DATA05__USDHC3_DATA1	0x1d0
			MX8MP_IOMUXC_NAND_DATA06__USDHC3_DATA2	0x1d0
			MX8MP_IOMUXC_NAND_DATA07__USDHC3_DATA3	0x1d0
			MX8MP_IOMUXC_NAND_RE_B__USDHC3_DATA4	0x1d0
			MX8MP_IOMUXC_NAND_CE2_B__USDHC3_DATA5	0x1d0
			MX8MP_IOMUXC_NAND_CE3_B__USDHC3_DATA6	0x1d0
			MX8MP_IOMUXC_NAND_CLE__USDHC3_DATA7	0x1d0
			MX8MP_IOMUXC_NAND_CE1_B__USDHC3_STROBE	0x190
		>;
	};

	pinctrl_usdhc3_100mhz: usdhc3grp-100mhz {
		fsl,pins = <
			MX8MP_IOMUXC_NAND_WE_B__USDHC3_CLK	0x194
			MX8MP_IOMUXC_NAND_WP_B__USDHC3_CMD	0x1d4
			MX8MP_IOMUXC_NAND_DATA04__USDHC3_DATA0	0x1d4
			MX8MP_IOMUXC_NAND_DATA05__USDHC3_DATA1	0x1d4
			MX8MP_IOMUXC_NAND_DATA06__USDHC3_DATA2	0x1d4
			MX8MP_IOMUXC_NAND_DATA07__USDHC3_DATA3	0x1d4
			MX8MP_IOMUXC_NAND_RE_B__USDHC3_DATA4	0x1d4
			MX8MP_IOMUXC_NAND_CE2_B__USDHC3_DATA5	0x1d4
			MX8MP_IOMUXC_NAND_CE3_B__USDHC3_DATA6	0x1d4
			MX8MP_IOMUXC_NAND_CLE__USDHC3_DATA7	0x1d4
			MX8MP_IOMUXC_NAND_CE1_B__USDHC3_STROBE	0x194
		>;
	};

	pinctrl_usdhc3_200mhz: usdhc3grp-200mhz {
		fsl,pins = <
			MX8MP_IOMUXC_NAND_WE_B__USDHC3_CLK	0x196
			MX8MP_IOMUXC_NAND_WP_B__USDHC3_CMD	0x1d6
			MX8MP_IOMUXC_NAND_DATA04__USDHC3_DATA0	0x1d6
			MX8MP_IOMUXC_NAND_DATA05__USDHC3_DATA1	0x1d6
			MX8MP_IOMUXC_NAND_DATA06__USDHC3_DATA2	0x1d6
			MX8MP_IOMUXC_NAND_DATA07__USDHC3_DATA3	0x1d6
			MX8MP_IOMUXC_NAND_RE_B__USDHC3_DATA4	0x1d6
			MX8MP_IOMUXC_NAND_CE2_B__USDHC3_DATA5	0x1d6
			MX8MP_IOMUXC_NAND_CE3_B__USDHC3_DATA6	0x1d6
			MX8MP_IOMUXC_NAND_CLE__USDHC3_DATA7	0x1d6
			MX8MP_IOMUXC_NAND_CE1_B__USDHC3_STROBE	0x196
		>;
	};

	pinctrl_wdog: wdoggrp {
		fsl,pins = <
			MX8MP_IOMUXC_GPIO1_IO02__WDOG1_WDOG_B	0xc6
		>;
	};

	pinctrl_csi0_pwn: csi0_pwn_grp {
		fsl,pins = <
			MX8MP_IOMUXC_GPIO1_IO07__GPIO1_IO07	0x9
		>;
	};

	pinctrl_csi0_rst: csi0_rst_grp {
		fsl,pins = <
			MX8MP_IOMUXC_GPIO1_IO08__GPIO1_IO08	0x9
			MX8MP_IOMUXC_GPIO1_IO14__CCM_CLKO1	0x59
		>;
	};

	pinctrl_csi1_pwn: csi1_pwn_grp {
		fsl,pins = <
			MX8MP_IOMUXC_SAI1_RXD3__GPIO4_IO05	0x9
		>;
	};

	pinctrl_csi1_rst: csi1_rst_grp {
		fsl,pins = <
			MX8MP_IOMUXC_SAI1_RXD2__GPIO4_IO04	0x9
			MX8MP_IOMUXC_GPIO1_IO15__CCM_CLKO2	0x59
		>;
	};

	pinctrl_enet_pwr: enetpwrgrp {
		fsl,pins = <
			MX8MP_IOMUXC_GPIO1_IO11__GPIO1_IO11		0x11
		>;
	};

	pinctrl_wifi_ctrl: wifi_ctrlgrp {
		fsl,pins = <
			MX8MP_IOMUXC_GPIO1_IO00__GPIO1_IO00	0x41 /* WL_REG_ON */
			MX8MP_IOMUXC_SAI1_TXD6__GPIO4_IO18	0x41 /* WL_WAKE_HOST */
		>;
	};

	pinctrl_bt_ctrl: bt_ctrlgrp {
		fsl,pins = <
			MX8MP_IOMUXC_GPIO1_IO05__GPIO1_IO05	0x41 /* BT_REG_ON */
			MX8MP_IOMUXC_SAI1_TXD7__GPIO4_IO19	0x41 /* BT_WAKE_HOST */
		>;
	};

	pinctrl_i2c_brd_conf: i2cbrdconfgrp {
		fsl,pins = <
			MX8MP_IOMUXC_SAI3_RXFS__GPIO4_IO28	0x1c3 /* BRD_CONF_SCL, bitbang */
			MX8MP_IOMUXC_SAI3_RXC__GPIO4_IO29	0x1c3 /* BRD_CONF_SDA, bitbang */
		>;
	};
};

&vpu_g1 {
	status = "okay";
};

&vpu_g2 {
	status = "okay";
};

&vpu_vc8000e {
	status = "okay";
};

&vpu_v4l2 {
	status = "okay";
};

&gpu_3d {
	status = "okay";
};

&gpu_2d {
	status = "okay";
};

&ml_vipsi {
	status = "okay";
};

&mix_gpu_ml {
	status = "okay";
};

&cameradev {
	status = "okay";
};

&isi_0 {
	status = "okay";

	cap_device {
		status = "okay";
	};

	m2m_device {
		status = "disabled";
	};
};

&isi_1 {
	status = "okay";

	cap_device {
		status = "okay";
	};
};

&dsp {
	status = "okay";
};
