// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright 2024 NXP
 */

/dts-v1/;

#include <dt-bindings/usb/pd.h>
#include "imx93.dtsi"

&ele_fw2 {
	memory-region = <&ele_reserved>;
};

/ {
	model = "NXP i.MX93 14X14 EVK board";
	compatible = "fsl,imx93-14x14-evk", "fsl,imx93";

	chosen {
		stdout-path = &lpuart1;
	};

	reserved-memory {
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;

		linux,cma {
			compatible = "shared-dma-pool";
			reusable;
			alloc-ranges = <0 0x80000000 0 0x40000000>;
			size = <0 0x10000000>;
			linux,cma-default;
		};

		ethosu_mem: ethosu_region@******** {
			compatible = "shared-dma-pool";
			reusable;
			reg = <0x0 0x******** 0x0 0x10000000>;
		};

		vdev0vring0: vdev0vring0@a4000000 {
			reg = <0 0xa4000000 0 0x8000>;
			no-map;
		};

		vdev0vring1: vdev0vring1@a4008000 {
			reg = <0 0xa4008000 0 0x8000>;
			no-map;
		};

		vdev1vring0: vdev1vring0@a4010000 {
			reg = <0 0xa4010000 0 0x8000>;
			no-map;
		};

		vdev1vring1: vdev1vring1@a4018000 {
			reg = <0 0xa4018000 0 0x8000>;
			no-map;
		};

		rsc_table: rsc-table@2021e000 {
			reg = <0 0x2021e000 0 0x1000>;
			no-map;
		};

		vdevbuffer: vdevbuffer@a4020000 {
			compatible = "shared-dma-pool";
			reg = <0 0xa4020000 0 0x100000>;
			no-map;
		};

		ele_reserved: ele-reserved@a4120000 {
			compatible = "shared-dma-pool";
			reg = <0 0xa4120000 0 0x100000>;
			no-map;
		};
	};

	reg_can1_stby: regulator-can1-stby {
		compatible = "regulator-fixed";
		regulator-name = "can1-stby";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&pcal6524_2 10 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		vin-supply = <&reg_can1_en>;
	};

	reg_can1_en: regulator-can1-en {
		compatible = "regulator-fixed";
		regulator-name = "can1-en";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&pcal6524_2 12 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	reg_can2_stby: regulator-can2-stby {
		compatible = "regulator-fixed";
		regulator-name = "can2-stby";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&pcal6524_2 11 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		vin-supply = <&reg_can2_en>;
	};

	reg_can2_en: regulator-can2-en {
		compatible = "regulator-fixed";
		regulator-name = "can2-en";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&pcal6524_2 13 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	reg_usdhc2_vmmc: regulator-usdhc2 {
		compatible = "regulator-fixed";
		pinctrl-names = "default";
		pinctrl-0 = <&pinctrl_reg_usdhc2_vmmc>;
		regulator-name = "VSD_3V3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio3 7 GPIO_ACTIVE_HIGH>;
		enable-active-high;
		off-on-delay-us = <12000>;
	};

	reg_vdd_12v: regulator-vdd-12v {
		compatible = "regulator-fixed";
		regulator-name = "reg_vdd_12v";
		regulator-min-microvolt = <12000000>;
		regulator-max-microvolt = <12000000>;
		gpio = <&pcal6524 14 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	reg_vref_1v8: regulator-adc-vref {
		compatible = "regulator-fixed";
		regulator-name = "vref_1v8";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
	};

	usdhc3_pwrseq: usdhc3_pwrseq {
		compatible = "mmc-pwrseq-simple";
		reset-gpios = <&pcal6524 12 GPIO_ACTIVE_LOW>;
	};

	reg_usdhc3_vmmc: usdhc3-vmmc {
		compatible = "regulator-fixed";
		regulator-name = "M2_WiFi_ON";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpios = <&pcal6524 20 GPIO_ACTIVE_HIGH>;
		enable-active-high;
	};

	reg_dvdd_1v2: regulator-dvdd {
		compatible = "regulator-fixed";
		regulator-name = "DVDD_1V2";
		gpio = <&adp5585_isp 7 GPIO_ACTIVE_HIGH>;
		regulator-min-microvolt = <1200000>;
		regulator-max-microvolt = <1200000>;
		enable-active-high;
	};

	reg_vddio_1v8: regulator-vddo {
		compatible = "regulator-fixed";
		regulator-name = "VDDIO_1V8";
		gpio = <&adp5585_isp 6 GPIO_ACTIVE_HIGH>;
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		enable-active-high;
	};

	reg_avdd_2v8: regulator-avdd {
		compatible = "regulator-fixed";
		regulator-name = "AVDD_2V8";
		gpio = <&adp5585_isp 8 GPIO_ACTIVE_HIGH>;
		regulator-min-microvolt = <2800000>;
		regulator-max-microvolt = <2800000>;
		enable-active-high;
	};

	reg_hmisc_vddio: regulator-hmisc-vddio {
		compatible = "regulator-fixed";
		regulator-name = "HMISC_VDDIO_1V8";
		gpio = <&adp5585_isp 10 GPIO_ACTIVE_HIGH>;
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		enable-active-high;
	};

	ethosu {
		compatible = "arm,ethosu";
		fsl,cm33-proc = <&cm33>;
		memory-region = <&ethosu_mem>;
		power-domains = <&mlmix>;
	};

	bt_sco_codec: bt_sco_codec {
		#sound-dai-cells = <1>;
		compatible = "linux,bt-sco";
	};

	sound-bt-sco {
		compatible = "simple-audio-card";
		simple-audio-card,name = "bt-sco-audio";
		simple-audio-card,format = "dsp_a";
		simple-audio-card,bitclock-inversion;
		simple-audio-card,frame-master = <&btcpu>;
		simple-audio-card,bitclock-master = <&btcpu>;

		btcpu: simple-audio-card,cpu {
			sound-dai = <&sai1>;
			dai-tdm-slot-num = <2>;
			dai-tdm-slot-width = <16>;
		};

		simple-audio-card,codec {
			sound-dai = <&bt_sco_codec 1>;
		};
	};

	sound-xcvr {
		compatible = "fsl,imx-audio-card";
		model = "imx-audio-xcvr";
		pri-dai-link {
			link-name = "XCVR PCM";
			cpu {
				sound-dai = <&xcvr>;
			};
		};
	};
};

&sai1 {
	#sound-dai-cells = <0>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_sai1>;
	assigned-clocks = <&clk IMX93_CLK_SAI1>;
	assigned-clock-parents = <&clk IMX93_CLK_AUDIO_PLL>;
	assigned-clock-rates = <12288000>;
	fsl,sai-mclk-direction-output;
	status = "okay";
};

&xcvr {
	#sound-dai-cells = <0>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_spdif>;
	clocks = <&clk IMX93_CLK_SPDIF_IPG>,
		<&clk IMX93_CLK_SPDIF_GATE>,
		<&clk IMX93_CLK_DUMMY>,
		<&clk IMX93_CLK_AUD_XCVR_GATE>,
		<&clk IMX93_CLK_AUDIO_PLL>;
	clock-names = "ipg", "phy", "spba", "pll_ipg", "pll8k";
	assigned-clocks = <&clk IMX93_CLK_SPDIF>,
			 <&clk IMX93_CLK_AUDIO_XCVR>;
	assigned-clock-parents = <&clk IMX93_CLK_AUDIO_PLL>,
			 <&clk IMX93_CLK_SYS_PLL_PFD1_DIV2>;
	assigned-clock-rates = <12288000>, <200000000>;
	status = "okay";
};

&adc1 {
	vref-supply = <&reg_vref_1v8>;
	status = "okay";
};

&cm33 {
	mbox-names = "tx", "rx", "rxdb";
	mboxes = <&mu1 0 1>,
		 <&mu1 1 1>,
		 <&mu1 3 1>;
	memory-region = <&vdevbuffer>, <&vdev0vring0>, <&vdev0vring1>,
			<&vdev1vring0>, <&vdev1vring1>, <&rsc_table>;
	fsl,startup-delay-ms = <500>;
	status = "okay";
};

&dphy {
	status = "okay";
};

&dsi {
	status = "okay";

	ports {
		port@1 {
			reg = <1>;

			dsi_to_adv7535: endpoint {
				remote-endpoint = <&adv7535_to_dsi>;
			};
		};
	};
};

&fec {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_fec>;
	phy-mode = "rgmii-id";
	phy-handle = <&ethphy2>;
	fsl,magic-packet;
	status = "okay";

	mdio {
		#address-cells = <1>;
		#size-cells = <0>;
		clock-frequency = <5000000>;

		ethphy2: ethernet-phy@2 {
			compatible = "ethernet-phy-ieee802.3-c22";
			reg = <2>;
			eee-broken-1000t;
			reset-gpios = <&pcal6524 16 GPIO_ACTIVE_LOW>;
			reset-assert-us = <10000>;
			reset-deassert-us = <80000>;
			realtek,clkout-disable;
		};
	};
};

&flexcan1 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_flexcan1>;
	xceiver-supply = <&reg_can1_stby>;
	status = "okay";
};

&flexcan2 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_flexcan2>;
	xceiver-supply = <&reg_can2_stby>;
	status = "okay";
};

&lcdif {
	status = "okay";
	assigned-clock-rates = <445333333>, <148444444>, <400000000>, <133333333>;
};

&lpi2c1 {
	clock-frequency = <400000>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_lpi2c1>;
	status = "okay";

	adv7535: hdmi@3d {
		compatible = "adi,adv7535";
		reg = <0x3d>;
		adi,addr-cec = <0x3b>;
		adi,dsi-lanes = <4>;
		status = "okay";

		port {
			adv7535_to_dsi: endpoint {
				remote-endpoint = <&dsi_to_adv7535>;
			};
		};
	};

	lsm6dsm@6a {
		compatible = "st,lsm6dso";
		reg = <0x6a>;
	};
};

&lpi2c2 {
	clock-frequency = <400000>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_lpi2c2>;
	status = "okay";

	pcal6524_2: gpio@20 {
		compatible = "nxp,pcal6524";
		reg = <0x20>;
		gpio-controller;
		#gpio-cells = <2>;
	};

	pmic@25 {
		compatible = "nxp,pca9452";
		reg = <0x25>;
		interrupt-parent = <&pcal6524>;
		interrupts = <11 IRQ_TYPE_EDGE_FALLING>;

		regulators {
			buck1: BUCK1 {
				regulator-name = "BUCK1";
				regulator-min-microvolt = <650000>;
				regulator-max-microvolt = <2237500>;
				regulator-boot-on;
				regulator-always-on;
				regulator-ramp-delay = <3125>;
			};

			buck2: BUCK2 {
				regulator-name = "BUCK2";
				regulator-min-microvolt = <600000>;
				regulator-max-microvolt = <2187500>;
				regulator-boot-on;
				regulator-always-on;
				regulator-ramp-delay = <3125>;
			};

			buck4: BUCK4{
				regulator-name = "BUCK4";
				regulator-min-microvolt = <600000>;
				regulator-max-microvolt = <3400000>;
				regulator-boot-on;
				regulator-always-on;
			};

			buck5: BUCK5{
				regulator-name = "BUCK5";
				regulator-min-microvolt = <600000>;
				regulator-max-microvolt = <3400000>;
				regulator-boot-on;
				regulator-always-on;
			};

			buck6: BUCK6 {
				regulator-name = "BUCK6";
				regulator-min-microvolt = <600000>;
				regulator-max-microvolt = <3400000>;
				regulator-boot-on;
				regulator-always-on;
			};

			ldo1: LDO1 {
				regulator-name = "LDO1";
				regulator-min-microvolt = <1600000>;
				regulator-max-microvolt = <3300000>;
				regulator-boot-on;
				regulator-always-on;
			};

			ldo3: LDO3 {
				regulator-name = "LDO3";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3300000>;
				regulator-boot-on;
				regulator-always-on;
			};

			ldo4: LDO4 {
				regulator-name = "LDO4";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3300000>;
				regulator-boot-on;
				regulator-always-on;
			};

			ldo5: LDO5 {
				regulator-name = "LDO5";
				regulator-min-microvolt = <1800000>;
				regulator-max-microvolt = <3300000>;
				regulator-boot-on;
				regulator-always-on;
			};
		};
	};

	pcal6524: gpio@22 {
		compatible = "nxp,pcal6524";
		pinctrl-names = "default";
		pinctrl-0 = <&pinctrl_pcal6524>;
		reg = <0x22>;
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-controller;
		#interrupt-cells = <2>;
		interrupt-parent = <&gpio3>;
		interrupts = <27 IRQ_TYPE_LEVEL_LOW>;
	};
};

&lpi2c3 {
	clock-frequency = <400000>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_lpi2c3>;
	status = "okay";

	adp5585_isp: io-expander-isp@34 {
		compatible = "adi,adp5585-01", "adi,adp5585";
		reg = <0x34>;
		gpio-controller;
		#gpio-cells = <2>;
		#pwm-cells = <3>;
	};

	ap1302: ap1302_mipi@3c {
		compatible = "onnn,ap1302";
		reg = <0x3c>;
		clocks = <&clk IMX93_CLK_24M>;
		reset-gpios   = <&pcal6524_2 0 GPIO_ACTIVE_LOW>;
		isp_en-gpios  = <&adp5585_isp 2 GPIO_ACTIVE_HIGH>;
		orientation = <2>;
		rotation = <0>;
		DVDD-supply   = <&reg_dvdd_1v2>;
		VDDIO_HMISC-supply  = <&reg_hmisc_vddio>;
		VDDIO_SMISC-supply   = <&reg_hmisc_vddio>;
		status = "okay";

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;
			};

			port@1 {
				reg = <1>;
			};

			port@2 {
				reg = <2>;

				isp_out: endpoint {
					remote-endpoint = <&mipi_csi_in>;
					data-lanes = <1 2>;
				};
			};
		};

		sensors {
			#address-cells = <1>;
			#size-cells = <0>;

			onnn,model = "onnn,ar0144";

			sensor@0 {
				reg = <0>;

				vdd-supply = <&reg_dvdd_1v2>;
				vddio-supply = <&reg_vddio_1v8>;
				vaa-supply = <&reg_avdd_2v8>;
			};
		};
	};
};

&lpuart1 { /* console */
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_uart1>;
	status = "okay";
};

&lpuart5 {
	/* BT */
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_uart5>;
	status = "okay";

	bluetooth {
		compatible = "nxp,88w8987-bt";
	};
};

&media_blk_ctrl {
	status = "okay";
};

&mu1 {
	status = "okay";
};

&mu2 {
	status = "okay";
};

&usbotg1 {
	dr_mode = "otg";
	hnp-disable;
	srp-disable;
	adp-disable;
	disable-over-current;
	samsung,picophy-pre-emp-curr-control = <3>;
	samsung,picophy-dc-vol-level-adjust = <7>;
	status = "okay";
};

&usbotg2 {
	dr_mode = "host";
	disable-over-current;
	samsung,picophy-pre-emp-curr-control = <3>;
	samsung,picophy-dc-vol-level-adjust = <7>;
	status = "okay";
};

&usdhc1 {
	pinctrl-names = "default", "state_100mhz", "state_200mhz";
	pinctrl-0 = <&pinctrl_usdhc1>;
	pinctrl-1 = <&pinctrl_usdhc1_100mhz>;
	pinctrl-2 = <&pinctrl_usdhc1_200mhz>;
	bus-width = <8>;
	non-removable;
	status = "okay";
};

&usdhc2 {
	pinctrl-names = "default", "state_100mhz", "state_200mhz";
	pinctrl-0 = <&pinctrl_usdhc2>, <&pinctrl_usdhc2_gpio>;
	pinctrl-1 = <&pinctrl_usdhc2_100mhz>, <&pinctrl_usdhc2_gpio>;
	pinctrl-2 = <&pinctrl_usdhc2_200mhz>, <&pinctrl_usdhc2_gpio>;
	cd-gpios = <&gpio3 00 GPIO_ACTIVE_LOW>;
	fsl,cd-gpio-wakeup-disable;
	vmmc-supply = <&reg_usdhc2_vmmc>;
	bus-width = <4>;
	no-mmc;
	status = "okay";
};

&usdhc3 {
	pinctrl-names = "default", "state_100mhz", "state_200mhz";
	pinctrl-0 = <&pinctrl_usdhc3>, <&pinctrl_usdhc3_wlan>;
	pinctrl-1 = <&pinctrl_usdhc3_100mhz>, <&pinctrl_usdhc3_wlan>;
	pinctrl-2 = <&pinctrl_usdhc3_200mhz>, <&pinctrl_usdhc3_wlan>;
	mmc-pwrseq = <&usdhc3_pwrseq>;
	vmmc-supply = <&reg_usdhc3_vmmc>;
	bus-width = <4>;
	keep-power-in-suspend;
	non-removable;
	wakeup-source;
	status = "okay";

	wifi_wake_host {
		compatible = "nxp,wifi-wake-host";
		interrupt-parent = <&gpio3>;
		interrupts = <26 IRQ_TYPE_LEVEL_LOW>;
		interrupt-names = "host-wake";
	};
};

&wdog3 {
	status = "okay";
};

&iomuxc {
	pinctrl_flexcan1: flexcan1grp {
		fsl,pins = <
			MX93_PAD_PDM_CLK__CAN1_TX		0x139e
			MX93_PAD_PDM_BIT_STREAM0__CAN1_RX	0x139e
		>;
	};

	pinctrl_flexcan2: flexcan2grp {
		fsl,pins = <
			MX93_PAD_GPIO_IO25__CAN2_TX	0x139e
			MX93_PAD_GPIO_IO27__CAN2_RX	0x139e
		>;
	};

	pinctrl_lpi2c1: lpi2c1grp {
		fsl,pins = <
			MX93_PAD_I2C1_SCL__LPI2C1_SCL			0x40000b9e
			MX93_PAD_I2C1_SDA__LPI2C1_SDA			0x40000b9e
		>;
	};

	pinctrl_lpi2c2: lpi2c2grp {
		fsl,pins = <
			MX93_PAD_I2C2_SCL__LPI2C2_SCL			0x40000b9e
			MX93_PAD_I2C2_SDA__LPI2C2_SDA			0x40000b9e
		>;
	};

	pinctrl_lpi2c3: lpi2c3grp {
		fsl,pins = <
			MX93_PAD_GPIO_IO28__LPI2C3_SDA			0x40000b9e
			MX93_PAD_GPIO_IO29__LPI2C3_SCL			0x40000b9e
		>;
	};

	pinctrl_pcal6524: pcal6524grp {
		fsl,pins = <
			MX93_PAD_CCM_CLKO2__GPIO3_IO27			0x31e
		>;
	};

	pinctrl_fec: fecgrp {
		fsl,pins = <
			MX93_PAD_ENET2_MDC__ENET1_MDC			0x57e
			MX93_PAD_ENET2_MDIO__ENET1_MDIO			0x57e
			MX93_PAD_ENET2_RD0__ENET1_RGMII_RD0		0x57e
			MX93_PAD_ENET2_RD1__ENET1_RGMII_RD1		0x57e
			MX93_PAD_ENET2_RD2__ENET1_RGMII_RD2		0x57e
			MX93_PAD_ENET2_RD3__ENET1_RGMII_RD3		0x57e
			MX93_PAD_ENET2_RXC__ENET1_RGMII_RXC		0x58e
			MX93_PAD_ENET2_RX_CTL__ENET1_RGMII_RX_CTL	0x57e
			MX93_PAD_ENET2_TD0__ENET1_RGMII_TD0		0x57e
			MX93_PAD_ENET2_TD1__ENET1_RGMII_TD1		0x57e
			MX93_PAD_ENET2_TD2__ENET1_RGMII_TD2		0x57e
			MX93_PAD_ENET2_TD3__ENET1_RGMII_TD3		0x57e
			MX93_PAD_ENET2_TXC__ENET1_RGMII_TXC		0x58e
			MX93_PAD_ENET2_TX_CTL__ENET1_RGMII_TX_CTL	0x57e
		>;
	};

	pinctrl_uart1: uart1grp {
		fsl,pins = <
			MX93_PAD_UART1_RXD__LPUART1_RX			0x31e
			MX93_PAD_UART1_TXD__LPUART1_TX			0x31e
		>;
	};

	pinctrl_uart5: uart5grp {
		fsl,pins = <
			MX93_PAD_DAP_TDO_TRACESWO__LPUART5_TX	0x31e
			MX93_PAD_DAP_TDI__LPUART5_RX		0x31e
			MX93_PAD_DAP_TMS_SWDIO__LPUART5_RTS_B	0x31e
			MX93_PAD_DAP_TCLK_SWCLK__LPUART5_CTS_B	0x31e
		>;
	};

	/* need to config the SION for data and cmd pad, refer to ERR052021 */
	pinctrl_usdhc1: usdhc1grp {
		fsl,pins = <
			MX93_PAD_SD1_CLK__USDHC1_CLK		0x1582
			MX93_PAD_SD1_CMD__USDHC1_CMD		0x40001382
			MX93_PAD_SD1_DATA0__USDHC1_DATA0	0x40001382
			MX93_PAD_SD1_DATA1__USDHC1_DATA1	0x40001382
			MX93_PAD_SD1_DATA2__USDHC1_DATA2	0x40001382
			MX93_PAD_SD1_DATA3__USDHC1_DATA3	0x40001382
			MX93_PAD_SD1_DATA4__USDHC1_DATA4	0x40001382
			MX93_PAD_SD1_DATA5__USDHC1_DATA5	0x40001382
			MX93_PAD_SD1_DATA6__USDHC1_DATA6	0x40001382
			MX93_PAD_SD1_DATA7__USDHC1_DATA7	0x40001382
			MX93_PAD_SD1_STROBE__USDHC1_STROBE	0x1582
		>;
	};

	/* need to config the SION for data and cmd pad, refer to ERR052021 */
	pinctrl_usdhc1_100mhz: usdhc1-100mhzgrp {
		fsl,pins = <
			MX93_PAD_SD1_CLK__USDHC1_CLK		0x158e
			MX93_PAD_SD1_CMD__USDHC1_CMD		0x4000138e
			MX93_PAD_SD1_DATA0__USDHC1_DATA0	0x4000138e
			MX93_PAD_SD1_DATA1__USDHC1_DATA1	0x4000138e
			MX93_PAD_SD1_DATA2__USDHC1_DATA2	0x4000138e
			MX93_PAD_SD1_DATA3__USDHC1_DATA3	0x4000138e
			MX93_PAD_SD1_DATA4__USDHC1_DATA4	0x4000138e
			MX93_PAD_SD1_DATA5__USDHC1_DATA5	0x4000138e
			MX93_PAD_SD1_DATA6__USDHC1_DATA6	0x4000138e
			MX93_PAD_SD1_DATA7__USDHC1_DATA7	0x4000138e
			MX93_PAD_SD1_STROBE__USDHC1_STROBE	0x158e
		>;
	};

	/* need to config the SION for data and cmd pad, refer to ERR052021 */
	pinctrl_usdhc1_200mhz: usdhc1-200mhzgrp {
		fsl,pins = <
			MX93_PAD_SD1_CLK__USDHC1_CLK		0x15fe
			MX93_PAD_SD1_CMD__USDHC1_CMD		0x400013fe
			MX93_PAD_SD1_DATA0__USDHC1_DATA0	0x400013fe
			MX93_PAD_SD1_DATA1__USDHC1_DATA1	0x400013fe
			MX93_PAD_SD1_DATA2__USDHC1_DATA2	0x400013fe
			MX93_PAD_SD1_DATA3__USDHC1_DATA3	0x400013fe
			MX93_PAD_SD1_DATA4__USDHC1_DATA4	0x400013fe
			MX93_PAD_SD1_DATA5__USDHC1_DATA5	0x400013fe
			MX93_PAD_SD1_DATA6__USDHC1_DATA6	0x400013fe
			MX93_PAD_SD1_DATA7__USDHC1_DATA7	0x400013fe
			MX93_PAD_SD1_STROBE__USDHC1_STROBE	0x15fe
		>;
	};

	pinctrl_reg_usdhc2_vmmc: regusdhc2vmmcgrp {
		fsl,pins = <
			MX93_PAD_SD2_RESET_B__GPIO3_IO07	0x31e
		>;
	};

	pinctrl_usdhc2_gpio: usdhc2gpiogrp {
		fsl,pins = <
			MX93_PAD_SD2_CD_B__GPIO3_IO00		0x31e
		>;
	};

	/* need to config the SION for data and cmd pad, refer to ERR052021 */
	pinctrl_usdhc2: usdhc2grp {
		fsl,pins = <
			MX93_PAD_SD2_CLK__USDHC2_CLK		0x1582
			MX93_PAD_SD2_CMD__USDHC2_CMD		0x40001382
			MX93_PAD_SD2_DATA0__USDHC2_DATA0	0x40001382
			MX93_PAD_SD2_DATA1__USDHC2_DATA1	0x40001382
			MX93_PAD_SD2_DATA2__USDHC2_DATA2	0x40001382
			MX93_PAD_SD2_DATA3__USDHC2_DATA3	0x40001382
			MX93_PAD_SD2_VSELECT__USDHC2_VSELECT	0x51e
		>;
	};

	/* need to config the SION for data and cmd pad, refer to ERR052021 */
	pinctrl_usdhc2_100mhz: usdhc2-100mhzgrp {
		fsl,pins = <
			MX93_PAD_SD2_CLK__USDHC2_CLK		0x158e
			MX93_PAD_SD2_CMD__USDHC2_CMD		0x4000138e
			MX93_PAD_SD2_DATA0__USDHC2_DATA0	0x4000138e
			MX93_PAD_SD2_DATA1__USDHC2_DATA1	0x4000138e
			MX93_PAD_SD2_DATA2__USDHC2_DATA2	0x4000138e
			MX93_PAD_SD2_DATA3__USDHC2_DATA3	0x4000138e
			MX93_PAD_SD2_VSELECT__USDHC2_VSELECT	0x51e
		>;
	};

	/* need to config the SION for data and cmd pad, refer to ERR052021 */
	pinctrl_usdhc2_200mhz: usdhc2-200mhzgrp {
		fsl,pins = <
			MX93_PAD_SD2_CLK__USDHC2_CLK		0x15fe
			MX93_PAD_SD2_CMD__USDHC2_CMD		0x400013fe
			MX93_PAD_SD2_DATA0__USDHC2_DATA0	0x400013fe
			MX93_PAD_SD2_DATA1__USDHC2_DATA1	0x400013fe
			MX93_PAD_SD2_DATA2__USDHC2_DATA2	0x400013fe
			MX93_PAD_SD2_DATA3__USDHC2_DATA3	0x400013fe
			MX93_PAD_SD2_VSELECT__USDHC2_VSELECT	0x51e
		>;
	};

	/* need to config the SION for data and cmd pad, refer to ERR052021 */
	pinctrl_usdhc3: usdhc3grp {
		fsl,pins = <
			MX93_PAD_SD3_CLK__USDHC3_CLK		0x1582
			MX93_PAD_SD3_CMD__USDHC3_CMD		0x40001382
			MX93_PAD_SD3_DATA0__USDHC3_DATA0	0x40001382
			MX93_PAD_SD3_DATA1__USDHC3_DATA1	0x40001382
			MX93_PAD_SD3_DATA2__USDHC3_DATA2	0x40001382
			MX93_PAD_SD3_DATA3__USDHC3_DATA3	0x40001382
		>;
	};

	/* need to config the SION for data and cmd pad, refer to ERR052021 */
	pinctrl_usdhc3_100mhz: usdhc3-100mhzgrp {
		fsl,pins = <
			MX93_PAD_SD3_CLK__USDHC3_CLK		0x158e
			MX93_PAD_SD3_CMD__USDHC3_CMD		0x4000138e
			MX93_PAD_SD3_DATA0__USDHC3_DATA0	0x4000138e
			MX93_PAD_SD3_DATA1__USDHC3_DATA1	0x4000138e
			MX93_PAD_SD3_DATA2__USDHC3_DATA2	0x4000138e
			MX93_PAD_SD3_DATA3__USDHC3_DATA3	0x4000138e
		>;
	};

	/* need to config the SION for data and cmd pad, refer to ERR052021 */
	pinctrl_usdhc3_200mhz: usdhc3-200mhzgrp {
		fsl,pins = <
			MX93_PAD_SD3_CLK__USDHC3_CLK		0x15fe
			MX93_PAD_SD3_CMD__USDHC3_CMD		0x400013fe
			MX93_PAD_SD3_DATA0__USDHC3_DATA0	0x400013fe
			MX93_PAD_SD3_DATA1__USDHC3_DATA1	0x400013fe
			MX93_PAD_SD3_DATA2__USDHC3_DATA2	0x400013fe
			MX93_PAD_SD3_DATA3__USDHC3_DATA3	0x400013fe
		>;
	};

	pinctrl_usdhc3_wlan: usdhc3wlangrp {
		fsl,pins = <
			MX93_PAD_CCM_CLKO1__GPIO3_IO26		0x31e
		>;
	};

	pinctrl_sai1: sai1grp {
		fsl,pins = <
			MX93_PAD_SAI1_TXC__SAI1_TX_BCLK			0x31e
			MX93_PAD_SAI1_TXFS__SAI1_TX_SYNC		0x31e
			MX93_PAD_SAI1_TXD0__SAI1_TX_DATA00		0x31e
			MX93_PAD_SAI1_RXD0__SAI1_RX_DATA00		0x31e
		>;
	};

	pinctrl_spdif: spdifgrp {
		fsl,pins = <
			MX93_PAD_GPIO_IO22__SPDIF_IN		0x3fe
			MX93_PAD_GPIO_IO23__SPDIF_OUT		0x3fe
		>;
	};
};

&epxp {
	status = "okay";
};

&isi {
	status = "okay";

	port {
		isi_in: endpoint {
			remote-endpoint = <&mipi_csi_out>;
		};
	};
};

&mipi_csi {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;

			mipi_csi_in: endpoint {
				remote-endpoint = <&isp_out>;
				data-lanes = <1 2>;
				clock-lanes = <0>;
			};
		};

		port@1 {
			reg = <1>;

			mipi_csi_out: endpoint {
				remote-endpoint = <&isi_in>;
			};
		};
	};
};

&dphy_rx {
	status = "okay";
};
