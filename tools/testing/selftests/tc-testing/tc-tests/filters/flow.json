[{"id": "5294", "name": "Add flow filter with map key and ops", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress"], "cmdUnderTest": "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key dst and 0xff", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 protocol ip prio 1 flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys dst and 0x000000ff baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "3514", "name": "Add flow filter with map key or ops", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress"], "cmdUnderTest": "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key dst or 0xff", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 protocol ip prio 1 flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys dst.*or 0x000000ff baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "7534", "name": "Add flow filter with map key xor ops", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress"], "cmdUnderTest": "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key dst xor 0xff", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 protocol ip prio 1 flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys dst xor 0x000000ff baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "4524", "name": "Add flow filter with map key rshift ops", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress"], "cmdUnderTest": "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key dst rshift 0x1f", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 protocol ip prio 1 flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys dst rshift 31 baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "0230", "name": "Add flow filter with map key addend ops", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress"], "cmdUnderTest": "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key dst addend 0xff", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 protocol ip prio 1 flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys dst addend 0xff baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "2344", "name": "Add flow filter with src map key", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress"], "cmdUnderTest": "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key src addend 0xff", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 protocol ip prio 1 flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys src addend 0xff baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "9304", "name": "Add flow filter with proto map key", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress"], "cmdUnderTest": "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key proto addend 0xff", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 protocol ip prio 1 flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys proto addend 0xff baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "9038", "name": "Add flow filter with proto-src map key", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress"], "cmdUnderTest": "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key proto-src addend 0xff", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 protocol ip prio 1 flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys proto-src addend 0xff baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "2a03", "name": "Add flow filter with proto-dst map key", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress"], "cmdUnderTest": "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key proto-dst addend 0xff", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 protocol ip prio 1 flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys proto-dst addend 0xff baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "a073", "name": "Add flow filter with iif map key", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress"], "cmdUnderTest": "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key iif addend 0xff", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 protocol ip prio 1 flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys iif addend 0xff baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "3b20", "name": "Add flow filter with priority map key", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress"], "cmdUnderTest": "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key priority addend 0xff", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 protocol ip prio 1 flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys priority addend 0xff baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "8945", "name": "Add flow filter with mark map key", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress"], "cmdUnderTest": "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key mark addend 0xff", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 protocol ip prio 1 flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys mark addend 0xff baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "c034", "name": "Add flow filter with nfct map key", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress"], "cmdUnderTest": "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key nfct addend 0xff", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 protocol ip prio 1 flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys nfct addend 0xff baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "0205", "name": "Add flow filter with nfct-src map key", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress"], "cmdUnderTest": "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key nfct-dst addend 0xff", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 protocol ip prio 1 flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys nfct-dst addend 0xff baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "5315", "name": "Add flow filter with nfct-src map key", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress"], "cmdUnderTest": "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key nfct-src addend 0xff", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 protocol ip prio 1 flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys nfct-src addend 0xff baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "7849", "name": "Add flow filter with nfct-proto-src map key", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress"], "cmdUnderTest": "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key nfct-proto-src addend 0xff", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 protocol ip prio 1 flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys nfct-proto-src addend 0xff baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "9902", "name": "Add flow filter with nfct-proto-dst map key", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress"], "cmdUnderTest": "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key nfct-proto-dst addend 0xff", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 protocol ip prio 1 flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys nfct-proto-dst addend 0xff baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "6742", "name": "Add flow filter with rt-classid map key", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress"], "cmdUnderTest": "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key rt-classid addend 0xff", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 protocol ip prio 1 flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys rt-classid addend 0xff baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "5432", "name": "Add flow filter with sk-uid map key", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress"], "cmdUnderTest": "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key sk-uid addend 0xff", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 protocol ip prio 1 flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys sk-uid addend 0xff baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "4134", "name": "Add flow filter with sk-gid map key", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress"], "cmdUnderTest": "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key sk-gid addend 0xff", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 protocol ip prio 1 flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys sk-gid addend 0xff baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "4522", "name": "Add flow filter with vlan-tag map key", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress"], "cmdUnderTest": "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key vlan-tag addend 0xff", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 protocol ip prio 1 flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys vlan-tag addend 0xff baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "4253", "name": "Add flow filter with rxhash map key", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress"], "cmdUnderTest": "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key rxhash addend 0xff", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 protocol ip prio 1 flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys rxhash addend 0xff baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "4452", "name": "Add flow filter with hash key list", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress"], "cmdUnderTest": "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow hash keys src", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 protocol ip prio 1 flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 hash keys src baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "4341", "name": "Add flow filter with muliple ops", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress"], "cmdUnderTest": "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow hash keys src divisor 1024 baseclass 1:1 match 'cmp(u8 at 0 layer link mask 0xff gt 10)' action drop", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 protocol ip prio 1 flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 hash keys src divisor 1024 baseclass 1:1.*cmp\\(u8 at 0 layer 0 mask 0xff gt 10\\)", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "4392", "name": "List flow filters", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress", "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key rxhash addend 0xff", "$TC filter add dev $DEV1 parent ffff: handle 2 prio 1 protocol ip flow map key rxhash or 0xff"], "cmdUnderTest": "$TC filter show dev $DEV1 parent ffff:", "expExitCode": "0", "verifyCmd": "$TC filter show dev $DEV1 parent ffff:", "matchPattern": "filter protocol ip pref 1 flow chain 0 handle 0x[0-9]+ map keys rxhash", "matchCount": "2", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "4322", "name": "Change flow filter with map key num", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress", "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key rxhash addend 0xff"], "cmdUnderTest": "$TC filter change dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key rxhash addend 0x22", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys rxhash addend 0x22 baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "2320", "name": "Replace flow filter with map key num", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress", "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key rxhash addend 0xff"], "cmdUnderTest": "$TC filter replace dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key rxhash addend 0x88", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys rxhash addend 0x88 baseclass", "matchCount": "1", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}, {"id": "3213", "name": "Delete flow filter with map key num", "category": ["filter", "flow"], "plugins": {"requires": "nsPlugin"}, "setup": ["$TC qdisc add dev $DEV1 ingress", "$TC filter add dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key rxhash addend 0xff"], "cmdUnderTest": "$TC filter delete dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow map key rxhash addend 0xff", "expExitCode": "0", "verifyCmd": "$TC filter get dev $DEV1 parent ffff: handle 1 prio 1 protocol ip flow", "matchPattern": "filter parent ffff: protocol ip pref 1 flow chain [0-9]+ handle 0x1 map keys rxhash addend 0x88 baseclass", "matchCount": "0", "teardown": ["$TC qdisc del dev $DEV1 ingress"]}]