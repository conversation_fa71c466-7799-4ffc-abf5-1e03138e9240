/*
 * Copyright 2019 Advanced Micro Devices, Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
 * THE COPYRIGHT HOLDER(S) OR AUTHOR(S) BE LIABLE FOR ANY CLAIM, DAMAGES OR
 * OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 * OTHER DEALINGS IN THE SOFTWARE.
 *
 * Authors: <AUTHORS>
 *
 */

#ifndef __DAL_DCN31_HPO_DP_STREAM_ENCODER_H__
#define __DAL_DCN31_HPO_DP_STREAM_ENCODER_H__

#include "dcn30/dcn30_vpg.h"
#include "dcn31/dcn31_apg.h"
#include "stream_encoder.h"


#define DCN3_1_HPO_DP_STREAM_ENC_FROM_HPO_STREAM_ENC(hpo_dp_stream_encoder)\
	container_of(hpo_dp_stream_encoder, struct dcn31_hpo_dp_stream_encoder, base)


/* Define MSA_DATA_LANE_[0-3] fields to make programming easier */
#define DP_SYM32_ENC_VID_MSA__MSA_DATA_LANE_0__SHIFT   0x0
#define DP_SYM32_ENC_VID_MSA__MSA_DATA_LANE_1__SHIFT   0x8
#define DP_SYM32_ENC_VID_MSA__MSA_DATA_LANE_2__SHIFT   0x10
#define DP_SYM32_ENC_VID_MSA__MSA_DATA_LANE_3__SHIFT   0x18
#define DP_SYM32_ENC_VID_MSA__MSA_DATA_LANE_0_MASK     0x000000FFL
#define DP_SYM32_ENC_VID_MSA__MSA_DATA_LANE_1_MASK     0x0000FF00L
#define DP_SYM32_ENC_VID_MSA__MSA_DATA_LANE_2_MASK     0x00FF0000L
#define DP_SYM32_ENC_VID_MSA__MSA_DATA_LANE_3_MASK     0xFF000000L


#define DCN3_1_HPO_DP_STREAM_ENC_REG_LIST(id) \
	SR(DP_STREAM_MAPPER_CONTROL0),\
	SR(DP_STREAM_MAPPER_CONTROL1),\
	SR(DP_STREAM_MAPPER_CONTROL2),\
	SR(DP_STREAM_MAPPER_CONTROL3),\
	SRI(DP_STREAM_ENC_CLOCK_CONTROL, DP_STREAM_ENC, id),\
	SRI(DP_STREAM_ENC_INPUT_MUX_CONTROL, DP_STREAM_ENC, id),\
	SRI(DP_STREAM_ENC_AUDIO_CONTROL, DP_STREAM_ENC, id),\
	SRI(DP_STREAM_ENC_CLOCK_RAMP_ADJUSTER_FIFO_STATUS_CONTROL0, DP_STREAM_ENC, id),\
	SRI(DP_SYM32_ENC_CONTROL, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_VID_PIXEL_FORMAT, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_VID_PIXEL_FORMAT_DOUBLE_BUFFER_CONTROL, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_VID_MSA0, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_VID_MSA1, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_VID_MSA2, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_VID_MSA3, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_VID_MSA4, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_VID_MSA5, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_VID_MSA6, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_VID_MSA7, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_VID_MSA8, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_VID_MSA_CONTROL, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_VID_MSA_DOUBLE_BUFFER_CONTROL, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_VID_FIFO_CONTROL, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_VID_STREAM_CONTROL, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_VID_VBID_CONTROL, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_SDP_CONTROL, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_SDP_GSP_CONTROL0, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_SDP_GSP_CONTROL2, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_SDP_GSP_CONTROL3, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_SDP_GSP_CONTROL5, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_SDP_GSP_CONTROL11, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_SDP_METADATA_PACKET_CONTROL, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_SDP_AUDIO_CONTROL0, DP_SYM32_ENC, id),\
	SRI(DP_SYM32_ENC_VID_CRC_CONTROL, DP_SYM32_ENC, id), \
	SRI(DP_SYM32_ENC_HBLANK_CONTROL, DP_SYM32_ENC, id)

#define DCN3_1_HPO_DP_STREAM_ENC_REGS \
	uint32_t DP_STREAM_MAPPER_CONTROL0;\
	uint32_t DP_STREAM_MAPPER_CONTROL1;\
	uint32_t DP_STREAM_MAPPER_CONTROL2;\
	uint32_t DP_STREAM_MAPPER_CONTROL3;\
	uint32_t DP_STREAM_ENC_CLOCK_CONTROL;\
	uint32_t DP_STREAM_ENC_INPUT_MUX_CONTROL;\
	uint32_t DP_STREAM_ENC_AUDIO_CONTROL;\
	uint32_t DP_STREAM_ENC_CLOCK_RAMP_ADJUSTER_FIFO_STATUS_CONTROL0;\
	uint32_t DP_SYM32_ENC_CONTROL;\
	uint32_t DP_SYM32_ENC_VID_PIXEL_FORMAT;\
	uint32_t DP_SYM32_ENC_VID_PIXEL_FORMAT_DOUBLE_BUFFER_CONTROL;\
	uint32_t DP_SYM32_ENC_VID_MSA0;\
	uint32_t DP_SYM32_ENC_VID_MSA1;\
	uint32_t DP_SYM32_ENC_VID_MSA2;\
	uint32_t DP_SYM32_ENC_VID_MSA3;\
	uint32_t DP_SYM32_ENC_VID_MSA4;\
	uint32_t DP_SYM32_ENC_VID_MSA5;\
	uint32_t DP_SYM32_ENC_VID_MSA6;\
	uint32_t DP_SYM32_ENC_VID_MSA7;\
	uint32_t DP_SYM32_ENC_VID_MSA8;\
	uint32_t DP_SYM32_ENC_VID_MSA_CONTROL;\
	uint32_t DP_SYM32_ENC_VID_MSA_DOUBLE_BUFFER_CONTROL;\
	uint32_t DP_SYM32_ENC_VID_FIFO_CONTROL;\
	uint32_t DP_SYM32_ENC_VID_STREAM_CONTROL;\
	uint32_t DP_SYM32_ENC_VID_VBID_CONTROL;\
	uint32_t DP_SYM32_ENC_SDP_CONTROL;\
	uint32_t DP_SYM32_ENC_SDP_GSP_CONTROL0;\
	uint32_t DP_SYM32_ENC_SDP_GSP_CONTROL2;\
	uint32_t DP_SYM32_ENC_SDP_GSP_CONTROL3;\
	uint32_t DP_SYM32_ENC_SDP_GSP_CONTROL5;\
	uint32_t DP_SYM32_ENC_SDP_GSP_CONTROL11;\
	uint32_t DP_SYM32_ENC_SDP_METADATA_PACKET_CONTROL;\
	uint32_t DP_SYM32_ENC_SDP_AUDIO_CONTROL0;\
	uint32_t DP_SYM32_ENC_VID_CRC_CONTROL;\
	uint32_t DP_SYM32_ENC_HBLANK_CONTROL


#define DCN3_1_HPO_DP_STREAM_ENC_MASK_SH_LIST(mask_sh)\
	SE_SF(DP_STREAM_MAPPER_CONTROL0, DP_STREAM_LINK_TARGET, mask_sh),\
	SE_SF(DP_STREAM_ENC0_DP_STREAM_ENC_CLOCK_CONTROL, DP_STREAM_ENC_CLOCK_EN, mask_sh),\
	SE_SF(DP_STREAM_ENC0_DP_STREAM_ENC_INPUT_MUX_CONTROL, DP_STREAM_ENC_INPUT_MUX_PIXEL_STREAM_SOURCE_SEL, mask_sh),\
	SE_SF(DP_STREAM_ENC0_DP_STREAM_ENC_AUDIO_CONTROL, DP_STREAM_ENC_INPUT_MUX_AUDIO_STREAM_SOURCE_SEL, mask_sh),\
	SE_SF(DP_STREAM_ENC0_DP_STREAM_ENC_CLOCK_RAMP_ADJUSTER_FIFO_STATUS_CONTROL0, FIFO_RESET, mask_sh),\
	SE_SF(DP_STREAM_ENC0_DP_STREAM_ENC_CLOCK_RAMP_ADJUSTER_FIFO_STATUS_CONTROL0, FIFO_RESET_DONE, mask_sh),\
	SE_SF(DP_STREAM_ENC0_DP_STREAM_ENC_CLOCK_RAMP_ADJUSTER_FIFO_STATUS_CONTROL0, FIFO_ENABLE, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_CONTROL, DP_SYM32_ENC_RESET, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_CONTROL, DP_SYM32_ENC_RESET_DONE, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_CONTROL, DP_SYM32_ENC_ENABLE, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_VID_PIXEL_FORMAT, PIXEL_ENCODING_TYPE, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_VID_PIXEL_FORMAT, UNCOMPRESSED_PIXEL_ENCODING, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_VID_PIXEL_FORMAT, UNCOMPRESSED_COMPONENT_DEPTH, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_VID_PIXEL_FORMAT_DOUBLE_BUFFER_CONTROL, PIXEL_FORMAT_DOUBLE_BUFFER_ENABLE, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_VID_MSA_DOUBLE_BUFFER_CONTROL, MSA_DOUBLE_BUFFER_ENABLE, mask_sh),\
	SE_SF(DP_SYM32_ENC_VID_MSA, MSA_DATA_LANE_0, mask_sh),\
	SE_SF(DP_SYM32_ENC_VID_MSA, MSA_DATA_LANE_1, mask_sh),\
	SE_SF(DP_SYM32_ENC_VID_MSA, MSA_DATA_LANE_2, mask_sh),\
	SE_SF(DP_SYM32_ENC_VID_MSA, MSA_DATA_LANE_3, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_VID_FIFO_CONTROL, PIXEL_TO_SYMBOL_FIFO_RESET, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_VID_FIFO_CONTROL, PIXEL_TO_SYMBOL_FIFO_RESET_DONE, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_VID_FIFO_CONTROL, PIXEL_TO_SYMBOL_FIFO_ENABLE, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_VID_STREAM_CONTROL, VID_STREAM_ENABLE, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_VID_STREAM_CONTROL, VID_STREAM_STATUS, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_VID_VBID_CONTROL, VBID_6_COMPRESSEDSTREAM_FLAG_SOF_REFERENCE, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_VID_VBID_CONTROL, VBID_6_COMPRESSEDSTREAM_FLAG_LINE_NUMBER, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_SDP_CONTROL, SDP_STREAM_ENABLE, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_SDP_GSP_CONTROL0, GSP_VIDEO_CONTINUOUS_TRANSMISSION_ENABLE, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_SDP_GSP_CONTROL0, GSP_PAYLOAD_SIZE, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_SDP_GSP_CONTROL0, GSP_TRANSMISSION_LINE_NUMBER, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_SDP_GSP_CONTROL5, GSP_VIDEO_CONTINUOUS_TRANSMISSION_ENABLE, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_SDP_GSP_CONTROL5, GSP_TRANSMISSION_LINE_NUMBER, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_SDP_GSP_CONTROL5, GSP_SOF_REFERENCE, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_SDP_METADATA_PACKET_CONTROL, METADATA_PACKET_ENABLE, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_SDP_AUDIO_CONTROL0, AUDIO_MUTE, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_SDP_AUDIO_CONTROL0, ASP_ENABLE, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_SDP_AUDIO_CONTROL0, ATP_ENABLE, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_SDP_AUDIO_CONTROL0, AIP_ENABLE, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_SDP_AUDIO_CONTROL0, ACM_ENABLE, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_VID_CRC_CONTROL, CRC_ENABLE, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_VID_CRC_CONTROL, CRC_CONT_MODE_ENABLE, mask_sh),\
	SE_SF(DP_SYM32_ENC0_DP_SYM32_ENC_HBLANK_CONTROL, HBLANK_MINIMUM_SYMBOL_WIDTH, mask_sh)


#define DCN3_1_HPO_DP_STREAM_ENC_REG_FIELD_LIST(type) \
	type DP_STREAM_LINK_TARGET;\
	type DP_STREAM_ENC_CLOCK_EN;\
	type DP_STREAM_ENC_INPUT_MUX_PIXEL_STREAM_SOURCE_SEL;\
	type DP_STREAM_ENC_INPUT_MUX_AUDIO_STREAM_SOURCE_SEL;\
	type FIFO_RESET;\
	type FIFO_RESET_DONE;\
	type FIFO_ENABLE;\
	type DP_SYM32_ENC_RESET;\
	type DP_SYM32_ENC_RESET_DONE;\
	type DP_SYM32_ENC_ENABLE;\
	type PIXEL_ENCODING_TYPE;\
	type UNCOMPRESSED_PIXEL_ENCODING;\
	type UNCOMPRESSED_COMPONENT_DEPTH;\
	type PIXEL_FORMAT_DOUBLE_BUFFER_ENABLE;\
	type MSA_DOUBLE_BUFFER_ENABLE;\
	type MSA_DATA_LANE_0;\
	type MSA_DATA_LANE_1;\
	type MSA_DATA_LANE_2;\
	type MSA_DATA_LANE_3;\
	type PIXEL_TO_SYMBOL_FIFO_RESET;\
	type PIXEL_TO_SYMBOL_FIFO_RESET_DONE;\
	type PIXEL_TO_SYMBOL_FIFO_ENABLE;\
	type VID_STREAM_ENABLE;\
	type VID_STREAM_STATUS;\
	type VBID_6_COMPRESSEDSTREAM_FLAG_SOF_REFERENCE;\
	type VBID_6_COMPRESSEDSTREAM_FLAG_LINE_NUMBER;\
	type SDP_STREAM_ENABLE;\
	type AUDIO_MUTE;\
	type ASP_ENABLE;\
	type ATP_ENABLE;\
	type AIP_ENABLE;\
	type ACM_ENABLE;\
	type GSP_VIDEO_CONTINUOUS_TRANSMISSION_ENABLE;\
	type GSP_PAYLOAD_SIZE;\
	type GSP_TRANSMISSION_LINE_NUMBER;\
	type GSP_SOF_REFERENCE;\
	type METADATA_PACKET_ENABLE;\
	type CRC_ENABLE;\
	type CRC_CONT_MODE_ENABLE;\
	type HBLANK_MINIMUM_SYMBOL_WIDTH


struct dcn31_hpo_dp_stream_encoder_registers {
	DCN3_1_HPO_DP_STREAM_ENC_REGS;
};

struct dcn31_hpo_dp_stream_encoder_shift {
	DCN3_1_HPO_DP_STREAM_ENC_REG_FIELD_LIST(uint8_t);
};

struct dcn31_hpo_dp_stream_encoder_mask {
	DCN3_1_HPO_DP_STREAM_ENC_REG_FIELD_LIST(uint32_t);
};

struct dcn31_hpo_dp_stream_encoder {
	struct hpo_dp_stream_encoder base;
	const struct dcn31_hpo_dp_stream_encoder_registers *regs;
	const struct dcn31_hpo_dp_stream_encoder_shift *hpo_se_shift;
	const struct dcn31_hpo_dp_stream_encoder_mask *hpo_se_mask;
};


void dcn31_hpo_dp_stream_encoder_construct(
	struct dcn31_hpo_dp_stream_encoder *enc3,
	struct dc_context *ctx,
	struct dc_bios *bp,
	uint32_t inst,
	enum engine_id eng_id,
	struct vpg *vpg,
	struct apg *apg,
	const struct dcn31_hpo_dp_stream_encoder_registers *regs,
	const struct dcn31_hpo_dp_stream_encoder_shift *hpo_se_shift,
	const struct dcn31_hpo_dp_stream_encoder_mask *hpo_se_mask);


#endif   // __DAL_DCN31_HPO_STREAM_ENCODER_H__
