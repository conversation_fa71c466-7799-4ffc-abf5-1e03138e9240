
# Copyright 2022 Advanced Micro Devices, Inc.
#
# Permission is hereby granted, free of charge, to any person obtaining a
# copy of this software and associated documentation files (the "Software"),
# to deal in the Software without restriction, including without limitation
# the rights to use, copy, modify, merge, publish, distribute, sublicense,
# and/or sell copies of the Software, and to permit persons to whom the
# Software is furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in
# all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
# THE COPYRIGHT HOLDER(S) OR AUTHOR(S) BE LIABLE FOR ANY CLAIM, DAMAGES OR
# OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
# ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
# OTHER DEALINGS IN THE SOFTWARE.
#
# Makefile for the 'hubp' sub-component of DAL.
#
ifdef CONFIG_DRM_AMD_DC_FP
###############################################################################
# DCN
###############################################################################

HUBP_DCN10 = dcn10_hubp.o

AMD_DAL_HUBP_DCN10 = $(addprefix $(AMDDALPATH)/dc/hubp/dcn10/,$(HUBP_DCN10))

AMD_DISPLAY_FILES += $(AMD_DAL_HUBP_DCN10)
###############################################################################

HUBP_DCN20 = dcn20_hubp.o

AMD_DAL_HUBP_DCN20 = $(addprefix $(AMDDALPATH)/dc/hubp/dcn20/,$(HUBP_DCN20))

AMD_DISPLAY_FILES += $(AMD_DAL_HUBP_DCN20)

###############################################################################

HUBP_DCN201 = dcn201_hubp.o

AMD_DAL_HUBP_DCN201 = $(addprefix $(AMDDALPATH)/dc/hubp/dcn201/,$(HUBP_DCN201))

AMD_DISPLAY_FILES += $(AMD_DAL_HUBP_DCN201)

###############################################################################

HUBP_DCN21 = dcn21_hubp.o

AMD_DAL_HUBP_DCN21 = $(addprefix $(AMDDALPATH)/dc/hubp/dcn21/,$(HUBP_DCN21))

AMD_DISPLAY_FILES += $(AMD_DAL_HUBP_DCN21)

###############################################################################
HUBP_DCN30 = dcn30_hubp.o

AMD_DAL_HUBP_DCN30 = $(addprefix $(AMDDALPATH)/dc/hubp/dcn30/,$(HUBP_DCN30))

AMD_DISPLAY_FILES += $(AMD_DAL_HUBP_DCN30)

###############################################################################

HUBP_DCN31 = dcn31_hubp.o

AMD_DAL_HUBP_DCN31 = $(addprefix $(AMDDALPATH)/dc/hubp/dcn31/,$(HUBP_DCN31))

AMD_DISPLAY_FILES += $(AMD_DAL_HUBP_DCN31)

###############################################################################

HUBP_DCN32 = dcn32_hubp.o

AMD_DAL_HUBP_DCN32 = $(addprefix $(AMDDALPATH)/dc/hubp/dcn32/,$(HUBP_DCN32))

AMD_DISPLAY_FILES += $(AMD_DAL_HUBP_DCN32)

###############################################################################

HUBP_DCN35 = dcn35_hubp.o

AMD_DAL_HUBP_DCN35 = $(addprefix $(AMDDALPATH)/dc/hubp/dcn35/,$(HUBP_DCN35))

AMD_DISPLAY_FILES += $(AMD_DAL_HUBP_DCN35)

###############################################################################

HUBP_DCN401 = dcn401_hubp.o

AMD_DAL_HUBP_DCN401 = $(addprefix $(AMDDALPATH)/dc/hubp/dcn401/,$(HUBP_DCN401))

AMD_DISPLAY_FILES += $(AMD_DAL_HUBP_DCN401)

endif
