
# Copyright 2022 Advanced Micro Devices, Inc.
#
# Permission is hereby granted, free of charge, to any person obtaining a
# copy of this software and associated documentation files (the "Software"),
# to deal in the Software without restriction, including without limitation
# the rights to use, copy, modify, merge, publish, distribute, sublicense,
# and/or sell copies of the Software, and to permit persons to whom the
# Software is furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in
# all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
# THE COPYRIGHT HOLDER(S) OR AUTHOR(S) BE LIABLE FOR ANY CLAIM, DAMAGES OR
# OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
# ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
# OTHER DEALINGS IN THE SOFTWARE.
#
# Makefile for the 'resource' sub-component of DAL.
#


###############################################################################
#  DCE
###############################################################################

RESOURCE_DCE100 = dce100_resource.o

AMD_DAL_RESOURCE_DCE100 = $(addprefix $(AMDDALPATH)/dc/resource/dce100/,$(RESOURCE_DCE100))

AMD_DISPLAY_FILES += $(AMD_DAL_RESOURCE_DCE100)

###############################################################################

RESOURCE_DCE110 = dce110_resource.o

AMD_DAL_RESOURCE_DCE110 = $(addprefix $(AMDDALPATH)/dc/resource/dce110/,$(RESOURCE_DCE110))

AMD_DISPLAY_FILES += $(AMD_DAL_RESOURCE_DCE110)

###############################################################################

RESOURCE_DCE112 = dce112_resource.o

AMD_DAL_RESOURCE_DCE112 = $(addprefix $(AMDDALPATH)/dc/resource/dce112/,$(RESOURCE_DCE112))

AMD_DISPLAY_FILES += $(AMD_DAL_RESOURCE_DCE112)

###############################################################################

RESOURCE_DCE120 = dce120_resource.o

AMD_DAL_RESOURCE_DCE120 = $(addprefix $(AMDDALPATH)/dc/resource/dce120/,$(RESOURCE_DCE120))

AMD_DISPLAY_FILES += $(AMD_DAL_RESOURCE_DCE120)

###############################################################################

RESOURCE_DCE80 = dce80_resource.o

AMD_DAL_RESOURCE_DCE80 = $(addprefix $(AMDDALPATH)/dc/resource/dce80/,$(RESOURCE_DCE80))

AMD_DISPLAY_FILES += $(AMD_DAL_RESOURCE_DCE80)

ifdef CONFIG_DRM_AMD_DC_FP
###############################################################################
# DCN
###############################################################################

RESOURCE_DCN10 = dcn10_resource.o

AMD_DAL_RESOURCE_DCN10 = $(addprefix $(AMDDALPATH)/dc/resource/dcn10/,$(RESOURCE_DCN10))

AMD_DISPLAY_FILES += $(AMD_DAL_RESOURCE_DCN10)

###############################################################################

RESOURCE_DCN20 = dcn20_resource.o

AMD_DAL_RESOURCE_DCN20 = $(addprefix $(AMDDALPATH)/dc/resource/dcn20/,$(RESOURCE_DCN20))

AMD_DISPLAY_FILES += $(AMD_DAL_RESOURCE_DCN20)

###############################################################################

RESOURCE_DCN201 = dcn201_resource.o

AMD_DAL_RESOURCE_DCN201 = $(addprefix $(AMDDALPATH)/dc/resource/dcn201/,$(RESOURCE_DCN201))

AMD_DISPLAY_FILES += $(AMD_DAL_RESOURCE_DCN201)

###############################################################################

RESOURCE_DCN21 = dcn21_resource.o

AMD_DAL_RESOURCE_DCN21 = $(addprefix $(AMDDALPATH)/dc/resource/dcn21/,$(RESOURCE_DCN21))

AMD_DISPLAY_FILES += $(AMD_DAL_RESOURCE_DCN21)

###############################################################################

RESOURCE_DCN30 = dcn30_resource.o

AMD_DAL_RESOURCE_DCN30 = $(addprefix $(AMDDALPATH)/dc/resource/dcn30/,$(RESOURCE_DCN30))

AMD_DISPLAY_FILES += $(AMD_DAL_RESOURCE_DCN30)

###############################################################################

RESOURCE_DCN301 = dcn301_resource.o

AMD_DAL_RESOURCE_DCN301 = $(addprefix $(AMDDALPATH)/dc/resource/dcn301/,$(RESOURCE_DCN301))

AMD_DISPLAY_FILES += $(AMD_DAL_RESOURCE_DCN301)

###############################################################################

RESOURCE_DCN302 = dcn302_resource.o

AMD_DAL_RESOURCE_DCN302 = $(addprefix $(AMDDALPATH)/dc/resource/dcn302/,$(RESOURCE_DCN302))

AMD_DISPLAY_FILES += $(AMD_DAL_RESOURCE_DCN302)

###############################################################################

RESOURCE_DCN303 = dcn303_resource.o

AMD_DAL_RESOURCE_DCN303 = $(addprefix $(AMDDALPATH)/dc/resource/dcn303/,$(RESOURCE_DCN303))

AMD_DISPLAY_FILES += $(AMD_DAL_RESOURCE_DCN303)

###############################################################################

RESOURCE_DCN31 = dcn31_resource.o

AMD_DAL_RESOURCE_DCN31 = $(addprefix $(AMDDALPATH)/dc/resource/dcn31/,$(RESOURCE_DCN31))

AMD_DISPLAY_FILES += $(AMD_DAL_RESOURCE_DCN31)

###############################################################################

RESOURCE_DCN314 = dcn314_resource.o

AMD_DAL_RESOURCE_DCN314 = $(addprefix $(AMDDALPATH)/dc/resource/dcn314/,$(RESOURCE_DCN314))

AMD_DISPLAY_FILES += $(AMD_DAL_RESOURCE_DCN314)

###############################################################################

RESOURCE_DCN315 = dcn315_resource.o

AMD_DAL_RESOURCE_DCN315 = $(addprefix $(AMDDALPATH)/dc/resource/dcn315/,$(RESOURCE_DCN315))

AMD_DISPLAY_FILES += $(AMD_DAL_RESOURCE_DCN315)

###############################################################################

RESOURCE_DCN316 = dcn316_resource.o

AMD_DAL_RESOURCE_DCN316 = $(addprefix $(AMDDALPATH)/dc/resource/dcn316/,$(RESOURCE_DCN316))

AMD_DISPLAY_FILES += $(AMD_DAL_RESOURCE_DCN316)

###############################################################################

RESOURCE_DCN32 = dcn32_resource.o dcn32_resource_helpers.o

AMD_DAL_RESOURCE_DCN32 = $(addprefix $(AMDDALPATH)/dc/resource/dcn32/,$(RESOURCE_DCN32))

AMD_DISPLAY_FILES += $(AMD_DAL_RESOURCE_DCN32)

###############################################################################

RESOURCE_DCN321 = dcn321_resource.o

AMD_DAL_RESOURCE_DCN321 = $(addprefix $(AMDDALPATH)/dc/resource/dcn321/,$(RESOURCE_DCN321))

AMD_DISPLAY_FILES += $(AMD_DAL_RESOURCE_DCN321)

###############################################################################

RESOURCE_DCN35 = dcn35_resource.o

AMD_DAL_RESOURCE_DCN35 = $(addprefix $(AMDDALPATH)/dc/resource/dcn35/,$(RESOURCE_DCN35))

AMD_DISPLAY_FILES += $(AMD_DAL_RESOURCE_DCN35)

###############################################################################

RESOURCE_DCN351 = dcn351_resource.o

AMD_DAL_RESOURCE_DCN351 = $(addprefix $(AMDDALPATH)/dc/resource/dcn351/,$(RESOURCE_DCN351))

AMD_DISPLAY_FILES += $(AMD_DAL_RESOURCE_DCN351)

###############################################################################

RESOURCE_DCN401 = dcn401_resource.o

AMD_DAL_RESOURCE_DCN401 = $(addprefix $(AMDDALPATH)/dc/resource/dcn401/,$(RESOURCE_DCN401))

AMD_DISPLAY_FILES += $(AMD_DAL_RESOURCE_DCN401)

endif
