/* SPDX-License-Identifier: MIT */
/*
 * Copyright 2023 Advanced Micro Devices, Inc.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a
 * copy of this software and associated documentation files (the "Software"),
 * to deal in the Software without restriction, including without limitation
 * the rights to use, copy, modify, merge, publish, distribute, sublicense,
 * and/or sell copies of the Software, and to permit persons to whom the
 * Software is furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
 * THE COPYRIGHT HOLDER(S) OR AUTHOR(S) BE LIABLE FOR ANY CLAIM, DAMAGES OR
 * OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
 * ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 * OTHER DEALINGS IN THE SOFTWARE.
 *
 * Authors: <AUTHORS>
 *
 */

#ifndef _DCN35_RESOURCE_H_
#define _DCN35_RESOURCE_H_

#include "core_types.h"

#define DCN3_5_VMIN_DISPCLK_HZ 717000000
#define TO_DCN35_RES_POOL(pool)\
	container_of(pool, struct dcn35_resource_pool, base)

extern struct _vcs_dpi_ip_params_st dcn3_5_ip;
extern struct _vcs_dpi_soc_bounding_box_st dcn3_5_soc;

struct dcn35_resource_pool {
	struct resource_pool base;
};

struct resource_pool *dcn35_create_resource_pool(
		const struct dc_init_data *init_data,
		struct dc *dc);

/* Defs for runtime init of registers */

#define OPP_REG_LIST_DCN20_RI(id) \
	OPP_REG_LIST_DCN10_RI(id), \
	OPP_DPG_REG_LIST_RI(id), \
	SRI_ARR(FMT_422_CONTROL, FMT, id), \
	SRI_ARR(OPPBUF_CONTROL1, OPPBUF, id)

#define OPP_REG_LIST_DCN35_RI(id) \
	OPP_REG_LIST_DCN20_RI(id), \
	SRI2_ARR(OPP_TOP_CLK_CONTROL, OPP, id)

#define VPG_DCN31_REG_LIST_RI(id) \
	SRI_ARR(VPG_GENERIC_STATUS, VPG, id), \
	SRI_ARR(VPG_GENERIC_PACKET_ACCESS_CTRL, VPG, id), \
	SRI_ARR(VPG_GENERIC_PACKET_DATA, VPG, id), \
	SRI_ARR(VPG_GSP_FRAME_UPDATE_CTRL, VPG, id), \
	SRI_ARR(VPG_GSP_IMMEDIATE_UPDATE_CTRL, VPG, id), \
	SRI_ARR(VPG_MEM_PWR, VPG, id)

#define AFMT_DCN31_REG_LIST_RI(id) \
	SRI_ARR(AFMT_INFOFRAME_CONTROL0, AFMT, id), \
	SRI_ARR(AFMT_VBI_PACKET_CONTROL, AFMT, id), \
	SRI_ARR(AFMT_AUDIO_PACKET_CONTROL, AFMT, id), \
	SRI_ARR(AFMT_AUDIO_PACKET_CONTROL2, AFMT, id), \
	SRI_ARR(AFMT_AUDIO_SRC_CONTROL, AFMT, id), \
	SRI_ARR(AFMT_60958_0, AFMT, id), \
	SRI_ARR(AFMT_60958_1, AFMT, id), \
	SRI_ARR(AFMT_60958_2, AFMT, id), \
	SRI_ARR(AFMT_MEM_PWR, AFMT, id)

/* Stream encoder */
#define SE_DCN35_REG_LIST_RI(id) \
	SRI_ARR(AFMT_CNTL, DIG, id), \
	SRI_ARR(DIG_FE_CNTL, DIG, id), \
	SRI_ARR(HDMI_CONTROL, DIG, id), \
	SRI_ARR(HDMI_DB_CONTROL, DIG, id), \
	SRI_ARR(HDMI_GC, DIG, id), \
	SRI_ARR(HDMI_GENERIC_PACKET_CONTROL0, DIG, id), \
	SRI_ARR(HDMI_GENERIC_PACKET_CONTROL1, DIG, id), \
	SRI_ARR(HDMI_GENERIC_PACKET_CONTROL2, DIG, id), \
	SRI_ARR(HDMI_GENERIC_PACKET_CONTROL3, DIG, id), \
	SRI_ARR(HDMI_GENERIC_PACKET_CONTROL4, DIG, id), \
	SRI_ARR(HDMI_GENERIC_PACKET_CONTROL5, DIG, id), \
	SRI_ARR(HDMI_GENERIC_PACKET_CONTROL6, DIG, id), \
	SRI_ARR(HDMI_GENERIC_PACKET_CONTROL7, DIG, id), \
	SRI_ARR(HDMI_GENERIC_PACKET_CONTROL8, DIG, id), \
	SRI_ARR(HDMI_GENERIC_PACKET_CONTROL9, DIG, id), \
	SRI_ARR(HDMI_GENERIC_PACKET_CONTROL10, DIG, id), \
	SRI_ARR(HDMI_INFOFRAME_CONTROL0, DIG, id), \
	SRI_ARR(HDMI_INFOFRAME_CONTROL1, DIG, id), \
	SRI_ARR(HDMI_VBI_PACKET_CONTROL, DIG, id), \
	SRI_ARR(HDMI_AUDIO_PACKET_CONTROL, DIG, id),\
	SRI_ARR(HDMI_ACR_PACKET_CONTROL, DIG, id),\
	SRI_ARR(HDMI_ACR_32_0, DIG, id),\
	SRI_ARR(HDMI_ACR_32_1, DIG, id),\
	SRI_ARR(HDMI_ACR_44_0, DIG, id),\
	SRI_ARR(HDMI_ACR_44_1, DIG, id),\
	SRI_ARR(HDMI_ACR_48_0, DIG, id),\
	SRI_ARR(HDMI_ACR_48_1, DIG, id),\
	SRI_ARR(DP_DB_CNTL, DP, id), \
	SRI_ARR(DP_MSA_MISC, DP, id), \
	SRI_ARR(DP_MSA_VBID_MISC, DP, id), \
	SRI_ARR(DP_MSA_COLORIMETRY, DP, id), \
	SRI_ARR(DP_MSA_TIMING_PARAM1, DP, id), \
	SRI_ARR(DP_MSA_TIMING_PARAM2, DP, id), \
	SRI_ARR(DP_MSA_TIMING_PARAM3, DP, id), \
	SRI_ARR(DP_MSA_TIMING_PARAM4, DP, id), \
	SRI_ARR(DP_MSE_RATE_CNTL, DP, id), \
	SRI_ARR(DP_MSE_RATE_UPDATE, DP, id), \
	SRI_ARR(DP_PIXEL_FORMAT, DP, id), \
	SRI_ARR(DP_SEC_CNTL, DP, id), \
	SRI_ARR(DP_SEC_CNTL1, DP, id), \
	SRI_ARR(DP_SEC_CNTL2, DP, id), \
	SRI_ARR(DP_SEC_CNTL5, DP, id), \
	SRI_ARR(DP_SEC_CNTL6, DP, id), \
	SRI_ARR(DP_STEER_FIFO, DP, id), \
	SRI_ARR(DP_VID_M, DP, id), \
	SRI_ARR(DP_VID_N, DP, id), \
	SRI_ARR(DP_VID_STREAM_CNTL, DP, id), \
	SRI_ARR(DP_VID_TIMING, DP, id), \
	SRI_ARR(DP_SEC_AUD_N, DP, id), \
	SRI_ARR(DP_SEC_TIMESTAMP, DP, id), \
	SRI_ARR(DP_DSC_CNTL, DP, id), \
	SRI_ARR(DP_SEC_METADATA_TRANSMISSION, DP, id), \
	SRI_ARR(HDMI_METADATA_PACKET_CONTROL, DIG, id), \
	SRI_ARR(DP_SEC_FRAMING4, DP, id), \
	SRI_ARR(DP_GSP11_CNTL, DP, id), \
	SRI_ARR(DME_CONTROL, DME, id),\
	SRI_ARR(DP_SEC_METADATA_TRANSMISSION, DP, id), \
	SRI_ARR(HDMI_METADATA_PACKET_CONTROL, DIG, id), \
	SRI_ARR(DIG_FE_CNTL, DIG, id), \
	SRI_ARR(DIG_FE_EN_CNTL, DIG, id), \
	SRI_ARR(DIG_FE_CLK_CNTL, DIG, id), \
	SRI_ARR(DIG_CLOCK_PATTERN, DIG, id), \
	SRI_ARR(DIG_FIFO_CTRL0, DIG, id), \
	SRI_ARR(STREAM_MAPPER_CONTROL, DIG, id)

#define LE_DCN35_REG_LIST_RI(id)\
	LE_DCN3_REG_LIST_RI(id),\
	SRI_ARR(DP_DPHY_INTERNAL_CTRL, DP, id), \
	SR_ARR(DIO_LINKA_CNTL, id), \
	SR_ARR(DIO_LINKB_CNTL, id), \
	SR_ARR(DIO_LINKC_CNTL, id), \
	SR_ARR(DIO_LINKD_CNTL, id), \
	SR_ARR(DIO_LINKE_CNTL, id), \
	SR_ARR(DIO_LINKF_CNTL, id),\
	SRI_ARR(DIG_BE_CLK_CNTL, DIG, id),\
	SR_ARR(DIO_CLK_CNTL, id)

#define MCIF_WB_COMMON_REG_LIST_DCN3_5_RI(inst)  \
	MCIF_WB_COMMON_REG_LIST_DCN32_RI(inst), \
		SRI2_ARR(MMHUBBUB_CLOCK_CNTL, MMHUBBUB, inst)

#define HWSEQ_DCN35_REG_LIST()\
	SR(DCHUBBUB_GLOBAL_TIMER_CNTL), \
	SR(DCHUBBUB_ARB_HOSTVM_CNTL), \
	SR(DIO_MEM_PWR_CTRL), \
	SR(ODM_MEM_PWR_CTRL3), \
	SR(MMHUBBUB_MEM_PWR_CNTL), \
	SR(DCCG_GATE_DISABLE_CNTL), \
	SR(DCCG_GATE_DISABLE_CNTL2), \
	SR(DCCG_GATE_DISABLE_CNTL4), \
	SR(DCCG_GATE_DISABLE_CNTL5), \
	SR(DCFCLK_CNTL),\
	SR(DC_MEM_GLOBAL_PWR_REQ_CNTL), \
	SRII(PIXEL_RATE_CNTL, OTG, 0), \
	SRII(PIXEL_RATE_CNTL, OTG, 1),\
	SRII(PIXEL_RATE_CNTL, OTG, 2),\
	SRII(PIXEL_RATE_CNTL, OTG, 3),\
	SRII(PHYPLL_PIXEL_RATE_CNTL, OTG, 0),\
	SRII(PHYPLL_PIXEL_RATE_CNTL, OTG, 1),\
	SRII(PHYPLL_PIXEL_RATE_CNTL, OTG, 2),\
	SRII(PHYPLL_PIXEL_RATE_CNTL, OTG, 3),\
	SR(MICROSECOND_TIME_BASE_DIV), \
	SR(MILLISECOND_TIME_BASE_DIV), \
	SR(DISPCLK_FREQ_CHANGE_CNTL), \
	SR(RBBMIF_TIMEOUT_DIS), \
	SR(RBBMIF_TIMEOUT_DIS_2), \
	SR(DCHUBBUB_CRC_CTRL), \
	SR(DPP_TOP0_DPP_CRC_CTRL), \
	SR(DPP_TOP0_DPP_CRC_VAL_B_A), \
	SR(DPP_TOP0_DPP_CRC_VAL_R_G), \
	SR(MPC_CRC_CTRL), \
	SR(MPC_CRC_RESULT_GB), \
	SR(MPC_CRC_RESULT_C), \
	SR(MPC_CRC_RESULT_AR), \
	SR(DOMAIN0_PG_CONFIG), \
	SR(DOMAIN1_PG_CONFIG), \
	SR(DOMAIN2_PG_CONFIG), \
	SR(DOMAIN3_PG_CONFIG), \
	SR(DOMAIN16_PG_CONFIG), \
	SR(DOMAIN17_PG_CONFIG), \
	SR(DOMAIN18_PG_CONFIG), \
	SR(DOMAIN19_PG_CONFIG), \
	SR(DOMAIN0_PG_STATUS), \
	SR(DOMAIN1_PG_STATUS), \
	SR(DOMAIN2_PG_STATUS), \
	SR(DOMAIN3_PG_STATUS), \
	SR(DOMAIN16_PG_STATUS), \
	SR(DOMAIN17_PG_STATUS), \
	SR(DOMAIN18_PG_STATUS), \
	SR(DOMAIN19_PG_STATUS), \
	SR(DC_IP_REQUEST_CNTL), \
	SR(AZALIA_AUDIO_DTO), \
	SR(AZALIA_CONTROLLER_CLOCK_GATING), \
	SR(HPO_TOP_HW_CONTROL),\
	SR(DMU_CLK_CNTL)

/* OPTC */
#define OPTC_COMMON_REG_LIST_DCN3_5_RI(inst)                                   \
	SRI_ARR(OTG_VSTARTUP_PARAM, OTG, inst),\
	SRI_ARR(OTG_VUPDATE_PARAM, OTG, inst),\
	SRI_ARR(OTG_VREADY_PARAM, OTG, inst),\
	SRI_ARR(OTG_MASTER_UPDATE_LOCK, OTG, inst),\
	SRI_ARR(OTG_GLOBAL_CONTROL0, OTG, inst),\
	SRI_ARR(OTG_GLOBAL_CONTROL1, OTG, inst),\
	SRI_ARR(OTG_GLOBAL_CONTROL2, OTG, inst),\
	SRI_ARR(OTG_GLOBAL_CONTROL4, OTG, inst),\
	SRI_ARR(OTG_DOUBLE_BUFFER_CONTROL, OTG, inst),\
	SRI_ARR(OTG_H_TOTAL, OTG, inst),\
	SRI_ARR(OTG_H_BLANK_START_END, OTG, inst),\
	SRI_ARR(OTG_H_SYNC_A, OTG, inst),\
	SRI_ARR(OTG_H_SYNC_A_CNTL, OTG, inst),\
	SRI_ARR(OTG_H_TIMING_CNTL, OTG, inst),\
	SRI_ARR(OTG_V_TOTAL, OTG, inst),\
	SRI_ARR(OTG_V_BLANK_START_END, OTG, inst),\
	SRI_ARR(OTG_V_SYNC_A, OTG, inst),\
	SRI_ARR(OTG_V_SYNC_A_CNTL, OTG, inst),\
	SRI_ARR(OTG_CONTROL, OTG, inst),\
	SRI_ARR(OTG_STEREO_CONTROL, OTG, inst),\
	SRI_ARR(OTG_3D_STRUCTURE_CONTROL, OTG, inst),\
	SRI_ARR(OTG_STEREO_STATUS, OTG, inst),\
	SRI_ARR(OTG_V_TOTAL_MAX, OTG, inst),\
	SRI_ARR(OTG_V_TOTAL_MIN, OTG, inst),\
	SRI_ARR(OTG_V_TOTAL_CONTROL, OTG, inst),\
	SRI_ARR(OTG_V_COUNT_STOP_CONTROL, OTG, inst),\
	SRI_ARR(OTG_V_COUNT_STOP_CONTROL2, OTG, inst),\
	SRI_ARR(OTG_TRIGA_CNTL, OTG, inst),\
	SRI_ARR(OTG_FORCE_COUNT_NOW_CNTL, OTG, inst),\
	SRI_ARR(OTG_STATIC_SCREEN_CONTROL, OTG, inst),\
	SRI_ARR(OTG_STATUS_FRAME_COUNT, OTG, inst),\
	SRI_ARR(OTG_STATUS, OTG, inst),\
	SRI_ARR(OTG_STATUS_POSITION, OTG, inst),\
	SRI_ARR(OTG_NOM_VERT_POSITION, OTG, inst),\
	SRI_ARR(OTG_M_CONST_DTO0, OTG, inst),\
	SRI_ARR(OTG_M_CONST_DTO1, OTG, inst),\
	SRI_ARR(OTG_CLOCK_CONTROL, OTG, inst),\
	SRI_ARR(OTG_VERTICAL_INTERRUPT0_CONTROL, OTG, inst),\
	SRI_ARR(OTG_VERTICAL_INTERRUPT0_POSITION, OTG, inst),\
	SRI_ARR(OTG_VERTICAL_INTERRUPT1_CONTROL, OTG, inst),\
	SRI_ARR(OTG_VERTICAL_INTERRUPT1_POSITION, OTG, inst),\
	SRI_ARR(OTG_VERTICAL_INTERRUPT2_CONTROL, OTG, inst),\
	SRI_ARR(OTG_VERTICAL_INTERRUPT2_POSITION, OTG, inst),\
	SRI_ARR(OPTC_INPUT_CLOCK_CONTROL, ODM, inst),\
	SRI_ARR(OPTC_DATA_SOURCE_SELECT, ODM, inst),\
	SRI_ARR(OPTC_INPUT_GLOBAL_CONTROL, ODM, inst),\
	SRI_ARR(CONTROL, VTG, inst),\
	SRI_ARR(OTG_VERT_SYNC_CONTROL, OTG, inst),\
	SRI_ARR(OTG_GSL_CONTROL, OTG, inst),\
	SRI_ARR(OTG_CRC_CNTL, OTG, inst),\
	SRI_ARR(OTG_CRC0_DATA_RG, OTG, inst),\
	SRI_ARR(OTG_CRC0_DATA_B, OTG, inst),\
	SRI_ARR(OTG_CRC1_DATA_RG, OTG, inst),\
	SRI_ARR(OTG_CRC1_DATA_B, OTG, inst),\
	SRI_ARR(OTG_CRC2_DATA_RG, OTG, inst),\
	SRI_ARR(OTG_CRC2_DATA_B, OTG, inst),\
	SRI_ARR(OTG_CRC3_DATA_RG, OTG, inst),\
	SRI_ARR(OTG_CRC3_DATA_B, OTG, inst),\
	SRI_ARR(OTG_CRC0_WINDOWA_X_CONTROL, OTG, inst),\
	SRI_ARR(OTG_CRC0_WINDOWA_Y_CONTROL, OTG, inst),\
	SRI_ARR(OTG_CRC0_WINDOWB_X_CONTROL, OTG, inst),\
	SRI_ARR(OTG_CRC0_WINDOWB_Y_CONTROL, OTG, inst),\
	SRI_ARR(OTG_CRC1_WINDOWA_X_CONTROL, OTG, inst),\
	SRI_ARR(OTG_CRC1_WINDOWA_Y_CONTROL, OTG, inst),\
	SRI_ARR(OTG_CRC1_WINDOWB_X_CONTROL, OTG, inst),\
	SRI_ARR(OTG_CRC1_WINDOWB_Y_CONTROL, OTG, inst),\
	SRI_ARR(OTG_CRC0_WINDOWA_X_CONTROL_READBACK, OTG, inst),\
	SRI_ARR(OTG_CRC0_WINDOWA_Y_CONTROL_READBACK, OTG, inst),\
	SRI_ARR(OTG_CRC0_WINDOWB_X_CONTROL_READBACK, OTG, inst),\
	SRI_ARR(OTG_CRC0_WINDOWB_Y_CONTROL_READBACK, OTG, inst),\
	SRI_ARR(OTG_CRC1_WINDOWA_X_CONTROL_READBACK, OTG, inst),\
	SRI_ARR(OTG_CRC1_WINDOWA_Y_CONTROL_READBACK, OTG, inst),\
	SRI_ARR(OTG_CRC1_WINDOWB_X_CONTROL_READBACK, OTG, inst),\
	SRI_ARR(OTG_CRC1_WINDOWB_Y_CONTROL_READBACK, OTG, inst),\
	SR_ARR(GSL_SOURCE_SELECT, inst),\
	SRI_ARR(OTG_TRIGA_MANUAL_TRIG, OTG, inst),\
	SRI_ARR(OTG_GLOBAL_CONTROL1, OTG, inst),\
	SRI_ARR(OTG_GLOBAL_CONTROL2, OTG, inst),\
	SRI_ARR(OTG_GSL_WINDOW_X, OTG, inst),\
	SRI_ARR(OTG_GSL_WINDOW_Y, OTG, inst),\
	SRI_ARR(OTG_VUPDATE_KEEPOUT, OTG, inst),\
	SRI_ARR(OTG_DSC_START_POSITION, OTG, inst),\
	SRI_ARR(OTG_DRR_TRIGGER_WINDOW, OTG, inst),\
	SRI_ARR(OTG_DRR_V_TOTAL_CHANGE, OTG, inst),\
	SRI_ARR(OPTC_DATA_FORMAT_CONTROL, ODM, inst),\
	SRI_ARR(OPTC_BYTES_PER_PIXEL, ODM, inst),\
	SRI_ARR(OPTC_WIDTH_CONTROL, ODM, inst),\
	SRI_ARR(OPTC_MEMORY_CONFIG, ODM, inst),\
	SRI_ARR(OTG_DRR_CONTROL, OTG, inst),\
	SRI2_ARR(OPTC_CLOCK_CONTROL, OPTC, inst)

/* DPP */
#define DPP_REG_LIST_DCN35_RI(id)\
	DPP_REG_LIST_DCN30_COMMON_RI(id)

#endif /* _DCN35_RESOURCE_H_ */
