
# Copyright 2022 Advanced Micro Devices, Inc.
#
# Permission is hereby granted, free of charge, to any person obtaining a
# copy of this software and associated documentation files (the "Software"),
# to deal in the Software without restriction, including without limitation
# the rights to use, copy, modify, merge, publish, distribute, sublicense,
# and/or sell copies of the Software, and to permit persons to whom the
# Software is furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in
# all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
# THE COPYRIGHT HOLDER(S) OR AUTHOR(S) BE LIABLE FOR ANY CLAIM, DAMAGES OR
# OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
# ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
# OTHER DEALINGS IN THE SOFTWARE.
#
# Makefile for the 'hubbub' sub-component of DAL.
#
ifdef CONFIG_DRM_AMD_DC_FP
###############################################################################
# DCN
###############################################################################

HUBBUB_DCN10 = dcn10_hubbub.o

AMD_DAL_HUBBUB_DCN10 = $(addprefix $(AMDDALPATH)/dc/hubbub/dcn10/,$(HUBBUB_DCN10))

AMD_DISPLAY_FILES += $(AMD_DAL_HUBBUB_DCN10)

###############################################################################

HUBBUB_DCN20 = dcn20_hubbub.o

AMD_DAL_HUBBUB_DCN20 = $(addprefix $(AMDDALPATH)/dc/hubbub/dcn20/,$(HUBBUB_DCN20))

AMD_DISPLAY_FILES += $(AMD_DAL_HUBBUB_DCN20)

###############################################################################

HUBBUB_DCN201 = dcn201_hubbub.o

AMD_DAL_HUBBUB_DCN201 = $(addprefix $(AMDDALPATH)/dc/hubbub/dcn201/,$(HUBBUB_DCN201))

AMD_DISPLAY_FILES += $(AMD_DAL_HUBBUB_DCN201)

###############################################################################

HUBBUB_DCN21 = dcn21_hubbub.o

AMD_DAL_HUBBUB_DCN21 = $(addprefix $(AMDDALPATH)/dc/hubbub/dcn21/,$(HUBBUB_DCN21))

AMD_DISPLAY_FILES += $(AMD_DAL_HUBBUB_DCN21)

###############################################################################
HUBBUB_DCN30 = dcn30_hubbub.o

AMD_DAL_HUBBUB_DCN30 = $(addprefix $(AMDDALPATH)/dc/hubbub/dcn30/,$(HUBBUB_DCN30))

AMD_DISPLAY_FILES += $(AMD_DAL_HUBBUB_DCN30)

###############################################################################
HUBBUB_DCN301 = dcn301_hubbub.o

AMD_DAL_HUBBUB_DCN301 = $(addprefix $(AMDDALPATH)/dc/hubbub/dcn301/,$(HUBBUB_DCN301))

AMD_DISPLAY_FILES += $(AMD_DAL_HUBBUB_DCN301)

###############################################################################

HUBBUB_DCN31 = dcn31_hubbub.o

AMD_DAL_HUBBUB_DCN31 = $(addprefix $(AMDDALPATH)/dc/hubbub/dcn31/,$(HUBBUB_DCN31))

AMD_DISPLAY_FILES += $(AMD_DAL_HUBBUB_DCN31)

###############################################################################
HUBBUB_DCN32 = dcn32_hubbub.o

AMD_DAL_HUBBUB_DCN32 = $(addprefix $(AMDDALPATH)/dc/hubbub/dcn32/,$(HUBBUB_DCN32))

AMD_DISPLAY_FILES += $(AMD_DAL_HUBBUB_DCN32)

###############################################################################

HUBBUB_DCN35 = dcn35_hubbub.o

AMD_DAL_HUBBUB_DCN35 = $(addprefix $(AMDDALPATH)/dc/hubbub/dcn35/,$(HUBBUB_DCN35))

AMD_DISPLAY_FILES += $(AMD_DAL_HUBBUB_DCN35)

###############################################################################
HUBBUB_DCN401 = dcn401_hubbub.o

AMD_DAL_HUBBUB_DCN401 = $(addprefix $(AMDDALPATH)/dc/hubbub/dcn401/,$(HUBBUB_DCN401))

AMD_DISPLAY_FILES += $(AMD_DAL_HUBBUB_DCN401)

###############################################################################
endif
