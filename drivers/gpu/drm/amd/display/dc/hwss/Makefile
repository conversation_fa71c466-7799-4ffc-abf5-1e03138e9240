
# Copyright 2022 Advanced Micro Devices, Inc.
#
# Permission is hereby granted, free of charge, to any person obtaining a
# copy of this software and associated documentation files (the "Software"),
# to deal in the Software without restriction, including without limitation
# the rights to use, copy, modify, merge, publish, distribute, sublicense,
# and/or sell copies of the Software, and to permit persons to whom the
# Software is furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in
# all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
# THE COPYRIGHT HOLDER(S) OR AUTHOR(S) BE LIABLE FOR ANY CLAIM, DAMAGES OR
# OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE,
# ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
# OTHER DEALINGS IN THE SOFTWARE.
#
# Makefile for the 'hwss' sub-component of DAL.
#


###############################################################################
#  DCE
###############################################################################

HWSS_DCE = dce_hwseq.o

AMD_DAL_HWSS_DCE = $(addprefix $(AMDDALPATH)/dc/hwss/dce/,$(HWSS_DCE))

AMD_DISPLAY_FILES += $(AMD_DAL_HWSS_DCE)

###############################################################################

HWSS_DCE100 = dce100_hwseq.o

AMD_DAL_HWSS_DCE100 = $(addprefix $(AMDDALPATH)/dc/hwss/dce100/,$(HWSS_DCE100))

AMD_DISPLAY_FILES += $(AMD_DAL_HWSS_DCE100)

###############################################################################

HWSS_DCE110 = dce110_hwseq.o

AMD_DAL_HWSS_DCE110 = $(addprefix $(AMDDALPATH)/dc/hwss/dce110/,$(HWSS_DCE110))

AMD_DISPLAY_FILES += $(AMD_DAL_HWSS_DCE110)

###############################################################################

HWSS_DCE112 = dce112_hwseq.o

AMD_DAL_HWSS_DCE112 = $(addprefix $(AMDDALPATH)/dc/hwss/dce112/,$(HWSS_DCE112))

AMD_DISPLAY_FILES += $(AMD_DAL_HWSS_DCE112)

###############################################################################

HWSS_DCE120 = dce120_hwseq.o

AMD_DAL_HWSS_DCE120 = $(addprefix $(AMDDALPATH)/dc/hwss/dce120/,$(HWSS_DCE120))

AMD_DISPLAY_FILES += $(AMD_DAL_HWSS_DCE120)

###############################################################################

HWSS_DCE80 = dce80_hwseq.o

AMD_DAL_HWSS_DCE80 = $(addprefix $(AMDDALPATH)/dc/hwss/dce80/,$(HWSS_DCE80))

AMD_DISPLAY_FILES += $(AMD_DAL_HWSS_DCE80)

ifdef CONFIG_DRM_AMD_DC_FP
###############################################################################
# DCN
###############################################################################

HWSS_DCN10 = dcn10_hwseq.o dcn10_init.o

AMD_DAL_HWSS_DCN10 = $(addprefix $(AMDDALPATH)/dc/hwss/dcn10/,$(HWSS_DCN10))

AMD_DISPLAY_FILES += $(AMD_DAL_HWSS_DCN10)

###############################################################################

HWSS_DCN20 = dcn20_hwseq.o dcn20_init.o

AMD_DAL_HWSS_DCN20 = $(addprefix $(AMDDALPATH)/dc/hwss/dcn20/,$(HWSS_DCN20))

AMD_DISPLAY_FILES += $(AMD_DAL_HWSS_DCN20)

###############################################################################

HWSS_DCN201 = dcn201_hwseq.o dcn201_init.o

AMD_DAL_HWSS_DCN201 = $(addprefix $(AMDDALPATH)/dc/hwss/dcn201/,$(HWSS_DCN201))

AMD_DISPLAY_FILES += $(AMD_DAL_HWSS_DCN201)

###############################################################################

HWSS_DCN21 = dcn21_hwseq.o dcn21_init.o

AMD_DAL_HWSS_DCN21 = $(addprefix $(AMDDALPATH)/dc/hwss/dcn21/,$(HWSS_DCN21))

AMD_DISPLAY_FILES += $(AMD_DAL_HWSS_DCN21)

###############################################################################

HWSS_DCN30 = dcn30_hwseq.o dcn30_init.o

AMD_DAL_HWSS_DCN30 = $(addprefix $(AMDDALPATH)/dc/hwss/dcn30/,$(HWSS_DCN30))

AMD_DISPLAY_FILES += $(AMD_DAL_HWSS_DCN30)

###############################################################################

HWSS_DCN301 = dcn301_hwseq.o dcn301_init.o

AMD_DAL_HWSS_DCN301 = $(addprefix $(AMDDALPATH)/dc/hwss/dcn301/,$(HWSS_DCN301))

AMD_DISPLAY_FILES += $(AMD_DAL_HWSS_DCN301)

###############################################################################

HWSS_DCN302 = dcn302_hwseq.o dcn302_init.o

AMD_DAL_HWSS_DCN302 = $(addprefix $(AMDDALPATH)/dc/hwss/dcn302/,$(HWSS_DCN302))

AMD_DISPLAY_FILES += $(AMD_DAL_HWSS_DCN302)



###############################################################################

HWSS_DCN303 = dcn303_hwseq.o dcn303_init.o

AMD_DAL_HWSS_DCN303 = $(addprefix $(AMDDALPATH)/dc/hwss/dcn303/,$(HWSS_DCN303))

AMD_DISPLAY_FILES += $(AMD_DAL_HWSS_DCN303)

###############################################################################

HWSS_DCN31 = dcn31_hwseq.o dcn31_init.o

AMD_DAL_HWSS_DCN31 = $(addprefix $(AMDDALPATH)/dc/hwss/dcn31/,$(HWSS_DCN31))

AMD_DISPLAY_FILES += $(AMD_DAL_HWSS_DCN31)

###############################################################################

HWSS_DCN314 = dcn314_hwseq.o dcn314_init.o

AMD_DAL_HWSS_DCN314 = $(addprefix $(AMDDALPATH)/dc/hwss/dcn314/,$(HWSS_DCN314))

AMD_DISPLAY_FILES += $(AMD_DAL_HWSS_DCN314)

###############################################################################

HWSS_DCN32 = dcn32_hwseq.o dcn32_init.o

AMD_DAL_HWSS_DCN32 = $(addprefix $(AMDDALPATH)/dc/hwss/dcn32/,$(HWSS_DCN32))

AMD_DISPLAY_FILES += $(AMD_DAL_HWSS_DCN32)

###############################################################################

HWSS_DCN35 = dcn35_hwseq.o dcn35_init.o

AMD_DAL_HWSS_DCN35 = $(addprefix $(AMDDALPATH)/dc/hwss/dcn35/,$(HWSS_DCN35))

AMD_DISPLAY_FILES += $(AMD_DAL_HWSS_DCN35)

###############################################################################

HWSS_DCN351 = dcn351_hwseq.o dcn351_init.o

AMD_DAL_HWSS_DCN351 = $(addprefix $(AMDDALPATH)/dc/hwss/dcn351/,$(HWSS_DCN351))

AMD_DISPLAY_FILES += $(AMD_DAL_HWSS_DCN351)

###############################################################################

HWSS_DCN401 = dcn401_hwseq.o dcn401_init.o

AMD_DAL_HWSS_DCN401 = $(addprefix $(AMDDALPATH)/dc/hwss/dcn401/,$(HWSS_DCN401))

AMD_DISPLAY_FILES += $(AMD_DAL_HWSS_DCN401)
endif
