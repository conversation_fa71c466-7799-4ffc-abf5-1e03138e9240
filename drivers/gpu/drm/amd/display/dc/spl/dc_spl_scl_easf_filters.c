// SPDX-License-Identifier: MIT
//
// Copyright 2024 Advanced Micro Devices, Inc.

#include "spl_debug.h"
#include "dc_spl_filters.h"
#include "dc_spl_scl_filters.h"
#include "dc_spl_scl_easf_filters.h"

//========================================================
// <using>			gen_scaler_coeffs_cnf_file.m
// <using>			make_test_script.m
// <date>			03-Apr-2024
// <coeffDescrip>	3t_64p_LanczosEd_p_0.3_p_10qb_
// <num_taps>		3
// <num_phases>		64
// <scale_ratio>	 input/output = 0.300000000000
// <CoefType>		LanczosEd
// <CoefQuant>		S1.10
//========================================================
static const uint16_t easf_filter_3tap_64p_ratio_0_30[99] = {
	0x0200, 0x0200, 0x0000,
	0x01F6, 0x0206, 0x0004,
	0x01EC, 0x020B, 0x0009,
	0x01E2, 0x0211, 0x000D,
	0x01D8, 0x0216, 0x0012,
	0x01CE, 0x021C, 0x0016,
	0x01C4, 0x0221, 0x001B,
	0x01BA, 0x0226, 0x0020,
	0x01B0, 0x022A, 0x0026,
	0x01A6, 0x022F, 0x002B,
	0x019C, 0x0233, 0x0031,
	0x0192, 0x0238, 0x0036,
	0x0188, 0x023C, 0x003C,
	0x017E, 0x0240, 0x0042,
	0x0174, 0x0244, 0x0048,
	0x016A, 0x0248, 0x004E,
	0x0161, 0x024A, 0x0055,
	0x0157, 0x024E, 0x005B,
	0x014D, 0x0251, 0x0062,
	0x0144, 0x0253, 0x0069,
	0x013A, 0x0256, 0x0070,
	0x0131, 0x0258, 0x0077,
	0x0127, 0x025B, 0x007E,
	0x011E, 0x025C, 0x0086,
	0x0115, 0x025E, 0x008D,
	0x010B, 0x0260, 0x0095,
	0x0102, 0x0262, 0x009C,
	0x00F9, 0x0263, 0x00A4,
	0x00F0, 0x0264, 0x00AC,
	0x00E7, 0x0265, 0x00B4,
	0x00DF, 0x0264, 0x00BD,
	0x00D6, 0x0265, 0x00C5,
	0x00CD, 0x0266, 0x00CD,
};

//========================================================
// <using>			gen_scaler_coeffs_cnf_file.m
// <using>			make_test_script.m
// <date>			03-Apr-2024
// <coeffDescrip>	3t_64p_LanczosEd_p_0.4_p_10qb_
// <num_taps>		3
// <num_phases>		64
// <scale_ratio>	 input/output = 0.400000000000
// <CoefType>		LanczosEd
// <CoefQuant>		S1.10
//========================================================
static const uint16_t easf_filter_3tap_64p_ratio_0_40[99] = {
	0x0200, 0x0200, 0x0000,
	0x01F6, 0x0206, 0x0004,
	0x01EB, 0x020E, 0x0007,
	0x01E1, 0x0214, 0x000B,
	0x01D7, 0x021A, 0x000F,
	0x01CD, 0x0220, 0x0013,
	0x01C2, 0x0226, 0x0018,
	0x01B8, 0x022C, 0x001C,
	0x01AE, 0x0231, 0x0021,
	0x01A3, 0x0237, 0x0026,
	0x0199, 0x023C, 0x002B,
	0x018F, 0x0240, 0x0031,
	0x0185, 0x0245, 0x0036,
	0x017A, 0x024A, 0x003C,
	0x0170, 0x024F, 0x0041,
	0x0166, 0x0253, 0x0047,
	0x015C, 0x0257, 0x004D,
	0x0152, 0x025A, 0x0054,
	0x0148, 0x025E, 0x005A,
	0x013E, 0x0261, 0x0061,
	0x0134, 0x0264, 0x0068,
	0x012B, 0x0266, 0x006F,
	0x0121, 0x0269, 0x0076,
	0x0117, 0x026C, 0x007D,
	0x010E, 0x026E, 0x0084,
	0x0104, 0x0270, 0x008C,
	0x00FB, 0x0271, 0x0094,
	0x00F2, 0x0272, 0x009C,
	0x00E9, 0x0273, 0x00A4,
	0x00E0, 0x0274, 0x00AC,
	0x00D7, 0x0275, 0x00B4,
	0x00CE, 0x0275, 0x00BD,
	0x00C5, 0x0276, 0x00C5,
};

//========================================================
// <using>			gen_scaler_coeffs_cnf_file.m
// <using>			make_test_script.m
// <date>			03-Apr-2024
// <coeffDescrip>	3t_64p_LanczosEd_p_0.5_p_10qb_
// <num_taps>		3
// <num_phases>		64
// <scale_ratio>	 input/output = 0.500000000000
// <CoefType>		LanczosEd
// <CoefQuant>		S1.10
//========================================================
static const uint16_t easf_filter_3tap_64p_ratio_0_50[99] = {
	0x0200, 0x0200, 0x0000,
	0x01F5, 0x0209, 0x0002,
	0x01EA, 0x0211, 0x0005,
	0x01DF, 0x021A, 0x0007,
	0x01D4, 0x0222, 0x000A,
	0x01C9, 0x022A, 0x000D,
	0x01BE, 0x0232, 0x0010,
	0x01B3, 0x0239, 0x0014,
	0x01A8, 0x0241, 0x0017,
	0x019D, 0x0248, 0x001B,
	0x0192, 0x024F, 0x001F,
	0x0187, 0x0255, 0x0024,
	0x017C, 0x025C, 0x0028,
	0x0171, 0x0262, 0x002D,
	0x0166, 0x0268, 0x0032,
	0x015B, 0x026E, 0x0037,
	0x0150, 0x0273, 0x003D,
	0x0146, 0x0278, 0x0042,
	0x013B, 0x027D, 0x0048,
	0x0130, 0x0282, 0x004E,
	0x0126, 0x0286, 0x0054,
	0x011B, 0x028A, 0x005B,
	0x0111, 0x028D, 0x0062,
	0x0107, 0x0290, 0x0069,
	0x00FD, 0x0293, 0x0070,
	0x00F3, 0x0296, 0x0077,
	0x00E9, 0x0298, 0x007F,
	0x00DF, 0x029A, 0x0087,
	0x00D5, 0x029C, 0x008F,
	0x00CC, 0x029D, 0x0097,
	0x00C3, 0x029E, 0x009F,
	0x00BA, 0x029E, 0x00A8,
	0x00B1, 0x029E, 0x00B1,
};

//========================================================
// <using>			gen_scaler_coeffs_cnf_file.m
// <using>			make_test_script.m
// <date>			03-Apr-2024
// <coeffDescrip>	3t_64p_LanczosEd_p_0.6_p_10qb_
// <num_taps>		3
// <num_phases>		64
// <scale_ratio>	 input/output = 0.600000000000
// <CoefType>		LanczosEd
// <CoefQuant>		S1.10
//========================================================
static const uint16_t easf_filter_3tap_64p_ratio_0_60[99] = {
	0x0200, 0x0200, 0x0000,
	0x01F4, 0x020B, 0x0001,
	0x01E8, 0x0216, 0x0002,
	0x01DC, 0x0221, 0x0003,
	0x01D0, 0x022B, 0x0005,
	0x01C4, 0x0235, 0x0007,
	0x01B8, 0x0240, 0x0008,
	0x01AC, 0x0249, 0x000B,
	0x01A0, 0x0253, 0x000D,
	0x0194, 0x025C, 0x0010,
	0x0188, 0x0265, 0x0013,
	0x017C, 0x026E, 0x0016,
	0x0170, 0x0277, 0x0019,
	0x0164, 0x027F, 0x001D,
	0x0158, 0x0287, 0x0021,
	0x014C, 0x028F, 0x0025,
	0x0140, 0x0297, 0x0029,
	0x0135, 0x029D, 0x002E,
	0x0129, 0x02A4, 0x0033,
	0x011D, 0x02AB, 0x0038,
	0x0112, 0x02B0, 0x003E,
	0x0107, 0x02B5, 0x0044,
	0x00FC, 0x02BA, 0x004A,
	0x00F1, 0x02BF, 0x0050,
	0x00E6, 0x02C3, 0x0057,
	0x00DB, 0x02C7, 0x005E,
	0x00D1, 0x02CA, 0x0065,
	0x00C7, 0x02CC, 0x006D,
	0x00BD, 0x02CE, 0x0075,
	0x00B3, 0x02D0, 0x007D,
	0x00A9, 0x02D2, 0x0085,
	0x00A0, 0x02D2, 0x008E,
	0x0097, 0x02D2, 0x0097,
};

//========================================================
// <using>			gen_scaler_coeffs_cnf_file.m
// <using>			make_test_script.m
// <date>			03-Apr-2024
// <coeffDescrip>	3t_64p_LanczosEd_p_0.7_p_10qb_
// <num_taps>		3
// <num_phases>		64
// <scale_ratio>	 input/output = 0.700000000000
// <CoefType>		LanczosEd
// <CoefQuant>		S1.10
//========================================================
static const uint16_t easf_filter_3tap_64p_ratio_0_70[99] = {
	0x0200, 0x0200, 0x0000,
	0x01F3, 0x020D, 0x0000,
	0x01E5, 0x021B, 0x0000,
	0x01D8, 0x0228, 0x0000,
	0x01CB, 0x0235, 0x0000,
	0x01BD, 0x0243, 0x0000,
	0x01B0, 0x024F, 0x0001,
	0x01A2, 0x025C, 0x0002,
	0x0195, 0x0268, 0x0003,
	0x0187, 0x0275, 0x0004,
	0x017A, 0x0280, 0x0006,
	0x016D, 0x028C, 0x0007,
	0x015F, 0x0298, 0x0009,
	0x0152, 0x02A2, 0x000C,
	0x0145, 0x02AD, 0x000E,
	0x0138, 0x02B7, 0x0011,
	0x012B, 0x02C0, 0x0015,
	0x011E, 0x02CA, 0x0018,
	0x0111, 0x02D3, 0x001C,
	0x0105, 0x02DB, 0x0020,
	0x00F8, 0x02E3, 0x0025,
	0x00EC, 0x02EA, 0x002A,
	0x00E0, 0x02F1, 0x002F,
	0x00D5, 0x02F6, 0x0035,
	0x00C9, 0x02FC, 0x003B,
	0x00BE, 0x0301, 0x0041,
	0x00B3, 0x0305, 0x0048,
	0x00A8, 0x0309, 0x004F,
	0x009E, 0x030C, 0x0056,
	0x0094, 0x030E, 0x005E,
	0x008A, 0x0310, 0x0066,
	0x0081, 0x0310, 0x006F,
	0x0077, 0x0312, 0x0077,
};

//========================================================
// <using>			gen_scaler_coeffs_cnf_file.m
// <using>			make_test_script.m
// <date>			03-Apr-2024
// <coeffDescrip>	3t_64p_LanczosEd_p_0.8_p_10qb_
// <num_taps>		3
// <num_phases>		64
// <scale_ratio>	 input/output = 0.800000000000
// <CoefType>		LanczosEd
// <CoefQuant>		S1.10
//========================================================
static const uint16_t easf_filter_3tap_64p_ratio_0_80[99] = {
	0x0200, 0x0200, 0x0000,
	0x01F1, 0x0210, 0x0FFF,
	0x01E2, 0x0220, 0x0FFE,
	0x01D2, 0x0232, 0x0FFC,
	0x01C3, 0x0241, 0x0FFC,
	0x01B4, 0x0251, 0x0FFB,
	0x01A4, 0x0262, 0x0FFA,
	0x0195, 0x0271, 0x0FFA,
	0x0186, 0x0281, 0x0FF9,
	0x0176, 0x0291, 0x0FF9,
	0x0167, 0x02A0, 0x0FF9,
	0x0158, 0x02AE, 0x0FFA,
	0x0149, 0x02BD, 0x0FFA,
	0x013A, 0x02CB, 0x0FFB,
	0x012C, 0x02D7, 0x0FFD,
	0x011D, 0x02E5, 0x0FFE,
	0x010F, 0x02F1, 0x0000,
	0x0101, 0x02FD, 0x0002,
	0x00F3, 0x0308, 0x0005,
	0x00E5, 0x0313, 0x0008,
	0x00D8, 0x031D, 0x000B,
	0x00CB, 0x0326, 0x000F,
	0x00BE, 0x032F, 0x0013,
	0x00B2, 0x0337, 0x0017,
	0x00A6, 0x033E, 0x001C,
	0x009A, 0x0345, 0x0021,
	0x008F, 0x034A, 0x0027,
	0x0084, 0x034F, 0x002D,
	0x0079, 0x0353, 0x0034,
	0x006F, 0x0356, 0x003B,
	0x0065, 0x0358, 0x0043,
	0x005C, 0x0359, 0x004B,
	0x0053, 0x035A, 0x0053,
};

//========================================================
// <using>			gen_scaler_coeffs_cnf_file.m
// <using>			make_test_script.m
// <date>			03-Apr-2024
// <coeffDescrip>	3t_64p_LanczosEd_p_0.9_p_10qb_
// <num_taps>		3
// <num_phases>		64
// <scale_ratio>	 input/output = 0.900000000000
// <CoefType>		LanczosEd
// <CoefQuant>		S1.10
//========================================================
static const uint16_t easf_filter_3tap_64p_ratio_0_90[99] = {
	0x0200, 0x0200, 0x0000,
	0x01EE, 0x0214, 0x0FFE,
	0x01DC, 0x0228, 0x0FFC,
	0x01CA, 0x023C, 0x0FFA,
	0x01B9, 0x024F, 0x0FF8,
	0x01A7, 0x0262, 0x0FF7,
	0x0195, 0x0276, 0x0FF5,
	0x0183, 0x028A, 0x0FF3,
	0x0172, 0x029C, 0x0FF2,
	0x0160, 0x02AF, 0x0FF1,
	0x014F, 0x02C2, 0x0FEF,
	0x013E, 0x02D4, 0x0FEE,
	0x012D, 0x02E5, 0x0FEE,
	0x011C, 0x02F7, 0x0FED,
	0x010C, 0x0307, 0x0FED,
	0x00FB, 0x0318, 0x0FED,
	0x00EC, 0x0327, 0x0FED,
	0x00DC, 0x0336, 0x0FEE,
	0x00CD, 0x0344, 0x0FEF,
	0x00BE, 0x0352, 0x0FF0,
	0x00B0, 0x035E, 0x0FF2,
	0x00A2, 0x036A, 0x0FF4,
	0x0095, 0x0375, 0x0FF6,
	0x0088, 0x037F, 0x0FF9,
	0x007B, 0x0388, 0x0FFD,
	0x006F, 0x0391, 0x0000,
	0x0064, 0x0397, 0x0005,
	0x0059, 0x039D, 0x000A,
	0x004E, 0x03A3, 0x000F,
	0x0045, 0x03A6, 0x0015,
	0x003B, 0x03A9, 0x001C,
	0x0033, 0x03AA, 0x0023,
	0x002A, 0x03AC, 0x002A,
};

//========================================================
// <using>			gen_scaler_coeffs_cnf_file.m
// <using>			make_test_script.m
// <date>			03-Apr-2024
// <coeffDescrip>	3t_64p_LanczosEd_p_1_p_10qb_
// <num_taps>		3
// <num_phases>		64
// <scale_ratio>	 input/output = 1.000000000000
// <CoefType>		LanczosEd
// <CoefQuant>		S1.10
//========================================================
static const uint16_t easf_filter_3tap_64p_ratio_1_00[99] = {
	0x0200, 0x0200, 0x0000,
	0x01EB, 0x0217, 0x0FFE,
	0x01D5, 0x022F, 0x0FFC,
	0x01C0, 0x0247, 0x0FF9,
	0x01AB, 0x025E, 0x0FF7,
	0x0196, 0x0276, 0x0FF4,
	0x0181, 0x028D, 0x0FF2,
	0x016C, 0x02A5, 0x0FEF,
	0x0158, 0x02BB, 0x0FED,
	0x0144, 0x02D1, 0x0FEB,
	0x0130, 0x02E8, 0x0FE8,
	0x011C, 0x02FE, 0x0FE6,
	0x0109, 0x0313, 0x0FE4,
	0x00F6, 0x0328, 0x0FE2,
	0x00E4, 0x033C, 0x0FE0,
	0x00D2, 0x034F, 0x0FDF,
	0x00C0, 0x0363, 0x0FDD,
	0x00B0, 0x0374, 0x0FDC,
	0x009F, 0x0385, 0x0FDC,
	0x0090, 0x0395, 0x0FDB,
	0x0081, 0x03A4, 0x0FDB,
	0x0072, 0x03B3, 0x0FDB,
	0x0064, 0x03C0, 0x0FDC,
	0x0057, 0x03CC, 0x0FDD,
	0x004B, 0x03D6, 0x0FDF,
	0x003F, 0x03E0, 0x0FE1,
	0x0034, 0x03E8, 0x0FE4,
	0x002A, 0x03EF, 0x0FE7,
	0x0020, 0x03F5, 0x0FEB,
	0x0017, 0x03FA, 0x0FEF,
	0x000F, 0x03FD, 0x0FF4,
	0x0007, 0x03FF, 0x0FFA,
	0x0000, 0x0400, 0x0000,
};

//========================================================
// <using>			gen_scaler_coeffs_cnf_file.m
// <using>			make_test_script.m
// <date>			03-Apr-2024
// <coeffDescrip>	4t_64p_LanczosEd_p_0.3_p_10qb_
// <num_taps>		4
// <num_phases>		64
// <scale_ratio>	 input/output = 0.300000000000
// <CoefType>		LanczosEd
// <CoefQuant>		S1.10
//========================================================
static const uint16_t easf_filter_4tap_64p_ratio_0_30[132] = {
	0x0104, 0x01F8, 0x0104, 0x0000,
	0x00FE, 0x01F7, 0x010A, 0x0001,
	0x00F8, 0x01F6, 0x010F, 0x0003,
	0x00F2, 0x01F5, 0x0114, 0x0005,
	0x00EB, 0x01F4, 0x011B, 0x0006,
	0x00E5, 0x01F3, 0x0120, 0x0008,
	0x00DF, 0x01F2, 0x0125, 0x000A,
	0x00DA, 0x01F0, 0x012A, 0x000C,
	0x00D4, 0x01EE, 0x0130, 0x000E,
	0x00CE, 0x01ED, 0x0135, 0x0010,
	0x00C8, 0x01EB, 0x013A, 0x0013,
	0x00C2, 0x01E9, 0x0140, 0x0015,
	0x00BD, 0x01E7, 0x0145, 0x0017,
	0x00B7, 0x01E5, 0x014A, 0x001A,
	0x00B1, 0x01E2, 0x0151, 0x001C,
	0x00AC, 0x01E0, 0x0155, 0x001F,
	0x00A7, 0x01DD, 0x015A, 0x0022,
	0x00A1, 0x01DB, 0x015F, 0x0025,
	0x009C, 0x01D8, 0x0165, 0x0027,
	0x0097, 0x01D5, 0x016A, 0x002A,
	0x0092, 0x01D2, 0x016E, 0x002E,
	0x008C, 0x01CF, 0x0174, 0x0031,
	0x0087, 0x01CC, 0x0179, 0x0034,
	0x0083, 0x01C9, 0x017D, 0x0037,
	0x007E, 0x01C5, 0x0182, 0x003B,
	0x0079, 0x01C2, 0x0187, 0x003E,
	0x0074, 0x01BE, 0x018C, 0x0042,
	0x0070, 0x01BA, 0x0190, 0x0046,
	0x006B, 0x01B7, 0x0195, 0x0049,
	0x0066, 0x01B3, 0x019A, 0x004D,
	0x0062, 0x01AF, 0x019E, 0x0051,
	0x005E, 0x01AB, 0x01A2, 0x0055,
	0x005A, 0x01A6, 0x01A6, 0x005A,
};

//========================================================
// <using>			gen_scaler_coeffs_cnf_file.m
// <using>			make_test_script.m
// <date>			03-Apr-2024
// <coeffDescrip>	4t_64p_LanczosEd_p_0.4_p_10qb_
// <num_taps>		4
// <num_phases>		64
// <scale_ratio>	 input/output = 0.400000000000
// <CoefType>		LanczosEd
// <CoefQuant>		S1.10
//========================================================
static const uint16_t easf_filter_4tap_64p_ratio_0_40[132] = {
	0x00FB, 0x0209, 0x00FC, 0x0000,
	0x00F5, 0x0209, 0x0101, 0x0001,
	0x00EE, 0x0208, 0x0108, 0x0002,
	0x00E8, 0x0207, 0x010E, 0x0003,
	0x00E2, 0x0206, 0x0114, 0x0004,
	0x00DB, 0x0205, 0x011A, 0x0006,
	0x00D5, 0x0204, 0x0120, 0x0007,
	0x00CF, 0x0203, 0x0125, 0x0009,
	0x00C9, 0x0201, 0x012C, 0x000A,
	0x00C3, 0x01FF, 0x0132, 0x000C,
	0x00BD, 0x01FD, 0x0138, 0x000E,
	0x00B7, 0x01FB, 0x013E, 0x0010,
	0x00B1, 0x01F9, 0x0144, 0x0012,
	0x00AC, 0x01F7, 0x0149, 0x0014,
	0x00A6, 0x01F4, 0x0150, 0x0016,
	0x00A0, 0x01F2, 0x0156, 0x0018,
	0x009B, 0x01EF, 0x015C, 0x001A,
	0x0095, 0x01EC, 0x0162, 0x001D,
	0x0090, 0x01E9, 0x0168, 0x001F,
	0x008B, 0x01E6, 0x016D, 0x0022,
	0x0085, 0x01E3, 0x0173, 0x0025,
	0x0080, 0x01DF, 0x0179, 0x0028,
	0x007B, 0x01DC, 0x017E, 0x002B,
	0x0076, 0x01D8, 0x0184, 0x002E,
	0x0071, 0x01D4, 0x018A, 0x0031,
	0x006D, 0x01D1, 0x018E, 0x0034,
	0x0068, 0x01CD, 0x0193, 0x0038,
	0x0063, 0x01C8, 0x019A, 0x003B,
	0x005F, 0x01C4, 0x019E, 0x003F,
	0x005B, 0x01C0, 0x01A3, 0x0042,
	0x0056, 0x01BB, 0x01A9, 0x0046,
	0x0052, 0x01B7, 0x01AD, 0x004A,
	0x004E, 0x01B2, 0x01B2, 0x004E,
};

//========================================================
// <using>			gen_scaler_coeffs_cnf_file.m
// <using>			make_test_script.m
// <date>			03-Apr-2024
// <coeffDescrip>	4t_64p_LanczosEd_p_0.5_p_10qb_
// <num_taps>		4
// <num_phases>		64
// <scale_ratio>	 input/output = 0.500000000000
// <CoefType>		LanczosEd
// <CoefQuant>		S1.10
//========================================================
static const uint16_t easf_filter_4tap_64p_ratio_0_50[132] = {
	0x00E5, 0x0236, 0x00E5, 0x0000,
	0x00DE, 0x0235, 0x00ED, 0x0000,
	0x00D7, 0x0235, 0x00F4, 0x0000,
	0x00D0, 0x0235, 0x00FB, 0x0000,
	0x00C9, 0x0234, 0x0102, 0x0001,
	0x00C2, 0x0233, 0x010A, 0x0001,
	0x00BC, 0x0232, 0x0111, 0x0001,
	0x00B5, 0x0230, 0x0119, 0x0002,
	0x00AE, 0x022F, 0x0121, 0x0002,
	0x00A8, 0x022D, 0x0128, 0x0003,
	0x00A2, 0x022B, 0x012F, 0x0004,
	0x009B, 0x0229, 0x0137, 0x0005,
	0x0095, 0x0226, 0x013F, 0x0006,
	0x008F, 0x0224, 0x0146, 0x0007,
	0x0089, 0x0221, 0x014E, 0x0008,
	0x0083, 0x021E, 0x0155, 0x000A,
	0x007E, 0x021B, 0x015C, 0x000B,
	0x0078, 0x0217, 0x0164, 0x000D,
	0x0072, 0x0213, 0x016D, 0x000E,
	0x006D, 0x0210, 0x0173, 0x0010,
	0x0068, 0x020C, 0x017A, 0x0012,
	0x0063, 0x0207, 0x0182, 0x0014,
	0x005E, 0x0203, 0x0189, 0x0016,
	0x0059, 0x01FE, 0x0191, 0x0018,
	0x0054, 0x01F9, 0x0198, 0x001B,
	0x0050, 0x01F4, 0x019F, 0x001D,
	0x004B, 0x01EF, 0x01A6, 0x0020,
	0x0047, 0x01EA, 0x01AC, 0x0023,
	0x0043, 0x01E4, 0x01B3, 0x0026,
	0x003F, 0x01DF, 0x01B9, 0x0029,
	0x003B, 0x01D9, 0x01C0, 0x002C,
	0x0037, 0x01D3, 0x01C6, 0x0030,
	0x0033, 0x01CD, 0x01CD, 0x0033,
};

//========================================================
// <using>			gen_scaler_coeffs_cnf_file.m
// <using>			make_test_script.m
// <date>			03-Apr-2024
// <coeffDescrip>	4t_64p_LanczosEd_p_0.6_p_10qb_
// <num_taps>		4
// <num_phases>		64
// <scale_ratio>	 input/output = 0.600000000000
// <CoefType>		LanczosEd
// <CoefQuant>		S1.10
//========================================================
static const uint16_t easf_filter_4tap_64p_ratio_0_60[132] = {
	0x00C8, 0x026F, 0x00C9, 0x0000,
	0x00C0, 0x0270, 0x00D1, 0x0FFF,
	0x00B8, 0x0270, 0x00D9, 0x0FFF,
	0x00B1, 0x0270, 0x00E1, 0x0FFE,
	0x00A9, 0x026F, 0x00EB, 0x0FFD,
	0x00A2, 0x026E, 0x00F3, 0x0FFD,
	0x009A, 0x026D, 0x00FD, 0x0FFC,
	0x0093, 0x026C, 0x0105, 0x0FFC,
	0x008C, 0x026A, 0x010F, 0x0FFB,
	0x0085, 0x0268, 0x0118, 0x0FFB,
	0x007E, 0x0265, 0x0122, 0x0FFB,
	0x0078, 0x0263, 0x012A, 0x0FFB,
	0x0071, 0x0260, 0x0134, 0x0FFB,
	0x006B, 0x025C, 0x013E, 0x0FFB,
	0x0065, 0x0259, 0x0147, 0x0FFB,
	0x005F, 0x0255, 0x0151, 0x0FFB,
	0x0059, 0x0251, 0x015A, 0x0FFC,
	0x0054, 0x024D, 0x0163, 0x0FFC,
	0x004E, 0x0248, 0x016D, 0x0FFD,
	0x0049, 0x0243, 0x0176, 0x0FFE,
	0x0044, 0x023E, 0x017F, 0x0FFF,
	0x003F, 0x0238, 0x0189, 0x0000,
	0x003A, 0x0232, 0x0193, 0x0001,
	0x0036, 0x022C, 0x019C, 0x0002,
	0x0031, 0x0226, 0x01A5, 0x0004,
	0x002D, 0x021F, 0x01AF, 0x0005,
	0x0029, 0x0218, 0x01B8, 0x0007,
	0x0025, 0x0211, 0x01C1, 0x0009,
	0x0022, 0x020A, 0x01C9, 0x000B,
	0x001E, 0x0203, 0x01D2, 0x000D,
	0x001B, 0x01FB, 0x01DA, 0x0010,
	0x0018, 0x01F3, 0x01E3, 0x0012,
	0x0015, 0x01EB, 0x01EB, 0x0015,
};

//========================================================
// <using>			gen_scaler_coeffs_cnf_file.m
// <using>			make_test_script.m
// <date>			03-Apr-2024
// <coeffDescrip>	4t_64p_LanczosEd_p_0.7_p_10qb_
// <num_taps>		4
// <num_phases>		64
// <scale_ratio>	 input/output = 0.700000000000
// <CoefType>		LanczosEd
// <CoefQuant>		S1.10
//========================================================
static const uint16_t easf_filter_4tap_64p_ratio_0_70[132] = {
	0x00A3, 0x02B9, 0x00A4, 0x0000,
	0x009A, 0x02BA, 0x00AD, 0x0FFF,
	0x0092, 0x02BA, 0x00B6, 0x0FFE,
	0x0089, 0x02BA, 0x00C1, 0x0FFC,
	0x0081, 0x02B9, 0x00CB, 0x0FFB,
	0x0079, 0x02B8, 0x00D5, 0x0FFA,
	0x0071, 0x02B7, 0x00DF, 0x0FF9,
	0x0069, 0x02B5, 0x00EA, 0x0FF8,
	0x0062, 0x02B3, 0x00F4, 0x0FF7,
	0x005B, 0x02B0, 0x00FF, 0x0FF6,
	0x0054, 0x02AD, 0x010B, 0x0FF4,
	0x004D, 0x02A9, 0x0117, 0x0FF3,
	0x0046, 0x02A5, 0x0123, 0x0FF2,
	0x0040, 0x02A1, 0x012D, 0x0FF2,
	0x003A, 0x029C, 0x0139, 0x0FF1,
	0x0034, 0x0297, 0x0145, 0x0FF0,
	0x002F, 0x0292, 0x0150, 0x0FEF,
	0x0029, 0x028C, 0x015C, 0x0FEF,
	0x0024, 0x0285, 0x0169, 0x0FEE,
	0x001F, 0x027F, 0x0174, 0x0FEE,
	0x001B, 0x0278, 0x017F, 0x0FEE,
	0x0016, 0x0270, 0x018D, 0x0FED,
	0x0012, 0x0268, 0x0199, 0x0FED,
	0x000E, 0x0260, 0x01A4, 0x0FEE,
	0x000B, 0x0258, 0x01AF, 0x0FEE,
	0x0007, 0x024F, 0x01BC, 0x0FEE,
	0x0004, 0x0246, 0x01C7, 0x0FEF,
	0x0001, 0x023D, 0x01D3, 0x0FEF,
	0x0FFE, 0x0233, 0x01DF, 0x0FF0,
	0x0FFC, 0x0229, 0x01EA, 0x0FF1,
	0x0FFA, 0x021F, 0x01F4, 0x0FF3,
	0x0FF8, 0x0215, 0x01FF, 0x0FF4,
	0x0FF6, 0x020A, 0x020A, 0x0FF6,
};

//========================================================
// <using>			gen_scaler_coeffs_cnf_file.m
// <using>			make_test_script.m
// <date>			03-Apr-2024
// <coeffDescrip>	4t_64p_LanczosEd_p_0.8_p_10qb_
// <num_taps>		4
// <num_phases>		64
// <scale_ratio>	 input/output = 0.800000000000
// <CoefType>		LanczosEd
// <CoefQuant>		S1.10
//========================================================
static const uint16_t easf_filter_4tap_64p_ratio_0_80[132] = {
	0x0075, 0x0315, 0x0076, 0x0000,
	0x006C, 0x0316, 0x007F, 0x0FFF,
	0x0062, 0x0316, 0x008A, 0x0FFE,
	0x0059, 0x0315, 0x0096, 0x0FFC,
	0x0050, 0x0314, 0x00A1, 0x0FFB,
	0x0048, 0x0312, 0x00AD, 0x0FF9,
	0x0040, 0x0310, 0x00B8, 0x0FF8,
	0x0038, 0x030D, 0x00C5, 0x0FF6,
	0x0030, 0x030A, 0x00D1, 0x0FF5,
	0x0029, 0x0306, 0x00DE, 0x0FF3,
	0x0022, 0x0301, 0x00EB, 0x0FF2,
	0x001C, 0x02FC, 0x00F8, 0x0FF0,
	0x0015, 0x02F7, 0x0106, 0x0FEE,
	0x0010, 0x02F1, 0x0112, 0x0FED,
	0x000A, 0x02EA, 0x0121, 0x0FEB,
	0x0005, 0x02E3, 0x012F, 0x0FE9,
	0x0000, 0x02DB, 0x013D, 0x0FE8,
	0x0FFB, 0x02D3, 0x014C, 0x0FE6,
	0x0FF7, 0x02CA, 0x015A, 0x0FE5,
	0x0FF3, 0x02C1, 0x0169, 0x0FE3,
	0x0FF0, 0x02B7, 0x0177, 0x0FE2,
	0x0FEC, 0x02AD, 0x0186, 0x0FE1,
	0x0FE9, 0x02A2, 0x0196, 0x0FDF,
	0x0FE7, 0x0297, 0x01A4, 0x0FDE,
	0x0FE4, 0x028C, 0x01B3, 0x0FDD,
	0x0FE2, 0x0280, 0x01C2, 0x0FDC,
	0x0FE0, 0x0274, 0x01D0, 0x0FDC,
	0x0FDF, 0x0268, 0x01DE, 0x0FDB,
	0x0FDD, 0x025B, 0x01EE, 0x0FDA,
	0x0FDC, 0x024E, 0x01FC, 0x0FDA,
	0x0FDB, 0x0241, 0x020A, 0x0FDA,
	0x0FDB, 0x0233, 0x0218, 0x0FDA,
	0x0FDA, 0x0226, 0x0226, 0x0FDA,
};

//========================================================
// <using>			gen_scaler_coeffs_cnf_file.m
// <using>			make_test_script.m
// <date>			03-Apr-2024
// <coeffDescrip>	4t_64p_LanczosEd_p_0.9_p_10qb_
// <num_taps>		4
// <num_phases>		64
// <scale_ratio>	 input/output = 0.900000000000
// <CoefType>		LanczosEd
// <CoefQuant>		S1.10
//========================================================
static const uint16_t easf_filter_4tap_64p_ratio_0_90[132] = {
	0x003F, 0x0383, 0x003E, 0x0000,
	0x0034, 0x0383, 0x004A, 0x0FFF,
	0x002B, 0x0383, 0x0054, 0x0FFE,
	0x0021, 0x0381, 0x0061, 0x0FFD,
	0x0019, 0x037F, 0x006C, 0x0FFC,
	0x0010, 0x037C, 0x0079, 0x0FFB,
	0x0008, 0x0378, 0x0086, 0x0FFA,
	0x0001, 0x0374, 0x0093, 0x0FF8,
	0x0FFA, 0x036E, 0x00A1, 0x0FF7,
	0x0FF3, 0x0368, 0x00B0, 0x0FF5,
	0x0FED, 0x0361, 0x00BF, 0x0FF3,
	0x0FE8, 0x035A, 0x00CD, 0x0FF1,
	0x0FE2, 0x0352, 0x00DC, 0x0FF0,
	0x0FDE, 0x0349, 0x00EB, 0x0FEE,
	0x0FD9, 0x033F, 0x00FC, 0x0FEC,
	0x0FD5, 0x0335, 0x010D, 0x0FE9,
	0x0FD2, 0x032A, 0x011D, 0x0FE7,
	0x0FCF, 0x031E, 0x012E, 0x0FE5,
	0x0FCC, 0x0312, 0x013F, 0x0FE3,
	0x0FCA, 0x0305, 0x0150, 0x0FE1,
	0x0FC8, 0x02F8, 0x0162, 0x0FDE,
	0x0FC6, 0x02EA, 0x0174, 0x0FDC,
	0x0FC5, 0x02DC, 0x0185, 0x0FDA,
	0x0FC4, 0x02CD, 0x0197, 0x0FD8,
	0x0FC3, 0x02BE, 0x01AA, 0x0FD5,
	0x0FC3, 0x02AF, 0x01BB, 0x0FD3,
	0x0FC3, 0x029F, 0x01CD, 0x0FD1,
	0x0FC3, 0x028E, 0x01E0, 0x0FCF,
	0x0FC3, 0x027E, 0x01F2, 0x0FCD,
	0x0FC4, 0x026D, 0x0203, 0x0FCC,
	0x0FC5, 0x025C, 0x0215, 0x0FCA,
	0x0FC6, 0x024B, 0x0227, 0x0FC8,
	0x0FC7, 0x0239, 0x0239, 0x0FC7,
};

//========================================================
// <using>			gen_scaler_coeffs_cnf_file.m
// <using>			make_test_script.m
// <date>			03-Apr-2024
// <coeffDescrip>	4t_64p_LanczosEd_p_1_p_10qb_
// <num_taps>		4
// <num_phases>		64
// <scale_ratio>	 input/output = 1.000000000000
// <CoefType>		LanczosEd
// <CoefQuant>		S1.10
//========================================================
static const uint16_t easf_filter_4tap_64p_ratio_1_00[132] = {
	0x0000, 0x0400, 0x0000, 0x0000,
	0x0FF6, 0x03FF, 0x000B, 0x0000,
	0x0FED, 0x03FE, 0x0015, 0x0000,
	0x0FE4, 0x03FB, 0x0022, 0x0FFF,
	0x0FDC, 0x03F7, 0x002E, 0x0FFF,
	0x0FD5, 0x03F2, 0x003B, 0x0FFE,
	0x0FCE, 0x03EC, 0x0048, 0x0FFE,
	0x0FC8, 0x03E5, 0x0056, 0x0FFD,
	0x0FC3, 0x03DC, 0x0065, 0x0FFC,
	0x0FBE, 0x03D3, 0x0075, 0x0FFA,
	0x0FB9, 0x03C9, 0x0085, 0x0FF9,
	0x0FB6, 0x03BE, 0x0094, 0x0FF8,
	0x0FB2, 0x03B2, 0x00A6, 0x0FF6,
	0x0FB0, 0x03A5, 0x00B7, 0x0FF4,
	0x0FAD, 0x0397, 0x00CA, 0x0FF2,
	0x0FAB, 0x0389, 0x00DC, 0x0FF0,
	0x0FAA, 0x0379, 0x00EF, 0x0FEE,
	0x0FA9, 0x0369, 0x0102, 0x0FEC,
	0x0FA9, 0x0359, 0x0115, 0x0FE9,
	0x0FA9, 0x0348, 0x0129, 0x0FE6,
	0x0FA9, 0x0336, 0x013D, 0x0FE4,
	0x0FA9, 0x0323, 0x0153, 0x0FE1,
	0x0FAA, 0x0310, 0x0168, 0x0FDE,
	0x0FAC, 0x02FD, 0x017C, 0x0FDB,
	0x0FAD, 0x02E9, 0x0192, 0x0FD8,
	0x0FAF, 0x02D5, 0x01A7, 0x0FD5,
	0x0FB1, 0x02C0, 0x01BD, 0x0FD2,
	0x0FB3, 0x02AC, 0x01D2, 0x0FCF,
	0x0FB5, 0x0296, 0x01E9, 0x0FCC,
	0x0FB8, 0x0281, 0x01FE, 0x0FC9,
	0x0FBA, 0x026C, 0x0214, 0x0FC6,
	0x0FBD, 0x0256, 0x022A, 0x0FC3,
	0x0FC0, 0x0240, 0x0240, 0x0FC0,
};

//========================================================
// <using>			gen_scaler_coeffs_cnf_file.m
// <using>			make_test_script.m
// <date>			02-Apr-2024
// <coeffDescrip>	6t_64p_LanczosEd_p_0.3_p_10qb_
// <num_taps>		6
// <num_phases>		64
// <scale_ratio>	 input/output = 0.300000000000
// <CoefType>		LanczosEd
// <CoefQuant>		S1.10
//========================================================
static const uint16_t easf_filter_6tap_64p_ratio_0_30[198] = {
	0x004B, 0x0100, 0x0169, 0x0101, 0x004B, 0x0000,
	0x0049, 0x00FD, 0x0169, 0x0103, 0x004E, 0x0000,
	0x0047, 0x00FA, 0x0169, 0x0106, 0x0050, 0x0000,
	0x0045, 0x00F7, 0x0168, 0x0109, 0x0052, 0x0001,
	0x0043, 0x00F5, 0x0168, 0x010B, 0x0054, 0x0001,
	0x0040, 0x00F2, 0x0168, 0x010E, 0x0057, 0x0001,
	0x003E, 0x00EF, 0x0168, 0x0110, 0x0059, 0x0002,
	0x003C, 0x00EC, 0x0167, 0x0113, 0x005C, 0x0002,
	0x003A, 0x00E9, 0x0167, 0x0116, 0x005E, 0x0002,
	0x0038, 0x00E6, 0x0166, 0x0118, 0x0061, 0x0003,
	0x0036, 0x00E3, 0x0165, 0x011C, 0x0063, 0x0003,
	0x0034, 0x00E0, 0x0165, 0x011D, 0x0066, 0x0004,
	0x0033, 0x00DD, 0x0164, 0x0120, 0x0068, 0x0004,
	0x0031, 0x00DA, 0x0163, 0x0122, 0x006B, 0x0005,
	0x002F, 0x00D7, 0x0163, 0x0125, 0x006D, 0x0005,
	0x002D, 0x00D3, 0x0162, 0x0128, 0x0070, 0x0006,
	0x002B, 0x00D0, 0x0161, 0x012A, 0x0073, 0x0007,
	0x002A, 0x00CD, 0x0160, 0x012D, 0x0075, 0x0007,
	0x0028, 0x00CA, 0x015F, 0x012F, 0x0078, 0x0008,
	0x0026, 0x00C7, 0x015E, 0x0131, 0x007B, 0x0009,
	0x0025, 0x00C4, 0x015D, 0x0133, 0x007E, 0x0009,
	0x0023, 0x00C1, 0x015C, 0x0136, 0x0080, 0x000A,
	0x0022, 0x00BE, 0x015A, 0x0138, 0x0083, 0x000B,
	0x0020, 0x00BB, 0x0159, 0x013A, 0x0086, 0x000C,
	0x001F, 0x00B8, 0x0158, 0x013B, 0x0089, 0x000D,
	0x001E, 0x00B5, 0x0156, 0x013E, 0x008C, 0x000D,
	0x001C, 0x00B2, 0x0155, 0x0140, 0x008F, 0x000E,
	0x001B, 0x00AF, 0x0153, 0x0143, 0x0091, 0x000F,
	0x0019, 0x00AC, 0x0152, 0x0145, 0x0094, 0x0010,
	0x0018, 0x00A9, 0x0150, 0x0147, 0x0097, 0x0011,
	0x0017, 0x00A6, 0x014F, 0x0148, 0x009A, 0x0012,
	0x0016, 0x00A3, 0x014D, 0x0149, 0x009D, 0x0014,
	0x0015, 0x00A0, 0x014B, 0x014B, 0x00A0, 0x0015,
};

//========================================================
// <using>			gen_scaler_coeffs_cnf_file.m
// <using>			make_test_script.m
// <date>			02-Apr-2024
// <coeffDescrip>	6t_64p_LanczosEd_p_0.4_p_10qb_
// <num_taps>		6
// <num_phases>		64
// <scale_ratio>	 input/output = 0.400000000000
// <CoefType>		LanczosEd
// <CoefQuant>		S1.10
//========================================================
static const uint16_t easf_filter_6tap_64p_ratio_0_40[198] = {
	0x0028, 0x0106, 0x01A3, 0x0107, 0x0028, 0x0000,
	0x0026, 0x0102, 0x01A3, 0x010A, 0x002B, 0x0000,
	0x0024, 0x00FE, 0x01A3, 0x010F, 0x002D, 0x0FFF,
	0x0022, 0x00FA, 0x01A3, 0x0113, 0x002F, 0x0FFF,
	0x0021, 0x00F6, 0x01A3, 0x0116, 0x0031, 0x0FFF,
	0x001F, 0x00F2, 0x01A2, 0x011B, 0x0034, 0x0FFE,
	0x001D, 0x00EE, 0x01A2, 0x011F, 0x0036, 0x0FFE,
	0x001B, 0x00EA, 0x01A1, 0x0123, 0x0039, 0x0FFE,
	0x0019, 0x00E6, 0x01A1, 0x0127, 0x003B, 0x0FFE,
	0x0018, 0x00E2, 0x01A0, 0x012A, 0x003E, 0x0FFE,
	0x0016, 0x00DE, 0x01A0, 0x012E, 0x0041, 0x0FFD,
	0x0015, 0x00DA, 0x019F, 0x0132, 0x0043, 0x0FFD,
	0x0013, 0x00D6, 0x019E, 0x0136, 0x0046, 0x0FFD,
	0x0012, 0x00D2, 0x019D, 0x0139, 0x0049, 0x0FFD,
	0x0010, 0x00CE, 0x019C, 0x013D, 0x004C, 0x0FFD,
	0x000F, 0x00CA, 0x019A, 0x0141, 0x004F, 0x0FFD,
	0x000E, 0x00C6, 0x0199, 0x0144, 0x0052, 0x0FFD,
	0x000D, 0x00C2, 0x0197, 0x0148, 0x0055, 0x0FFD,
	0x000B, 0x00BE, 0x0196, 0x014C, 0x0058, 0x0FFD,
	0x000A, 0x00BA, 0x0195, 0x014F, 0x005B, 0x0FFD,
	0x0009, 0x00B6, 0x0193, 0x0153, 0x005E, 0x0FFD,
	0x0008, 0x00B2, 0x0191, 0x0157, 0x0061, 0x0FFD,
	0x0007, 0x00AE, 0x0190, 0x015A, 0x0064, 0x0FFD,
	0x0006, 0x00AA, 0x018E, 0x015D, 0x0068, 0x0FFD,
	0x0005, 0x00A6, 0x018C, 0x0161, 0x006B, 0x0FFD,
	0x0005, 0x00A2, 0x0189, 0x0164, 0x006F, 0x0FFD,
	0x0004, 0x009E, 0x0187, 0x0167, 0x0072, 0x0FFE,
	0x0003, 0x009A, 0x0185, 0x016B, 0x0075, 0x0FFE,
	0x0002, 0x0096, 0x0183, 0x016E, 0x0079, 0x0FFE,
	0x0002, 0x0093, 0x0180, 0x016F, 0x007D, 0x0FFF,
	0x0001, 0x008F, 0x017E, 0x0173, 0x0080, 0x0FFF,
	0x0001, 0x008B, 0x017B, 0x0175, 0x0084, 0x0000,
	0x0000, 0x0087, 0x0179, 0x0179, 0x0087, 0x0000,
};

//========================================================
// <using>			gen_scaler_coeffs_cnf_file.m
// <using>			make_test_script.m
// <date>			02-Apr-2024
// <coeffDescrip>	6t_64p_LanczosEd_p_0.5_p_10qb_
// <num_taps>		6
// <num_phases>		64
// <scale_ratio>	 input/output = 0.500000000000
// <CoefType>		LanczosEd
// <CoefQuant>		S1.10
//========================================================
static const uint16_t easf_filter_6tap_64p_ratio_0_50[198] = {
	0x0000, 0x0107, 0x01F3, 0x0106, 0x0000, 0x0000,
	0x0FFE, 0x0101, 0x01F3, 0x010D, 0x0002, 0x0FFF,
	0x0FFD, 0x00FB, 0x01F3, 0x0113, 0x0003, 0x0FFF,
	0x0FFC, 0x00F6, 0x01F3, 0x0118, 0x0005, 0x0FFE,
	0x0FFA, 0x00F0, 0x01F3, 0x011E, 0x0007, 0x0FFE,
	0x0FF9, 0x00EB, 0x01F2, 0x0124, 0x0009, 0x0FFD,
	0x0FF8, 0x00E5, 0x01F2, 0x0129, 0x000B, 0x0FFD,
	0x0FF7, 0x00E0, 0x01F1, 0x012F, 0x000D, 0x0FFC,
	0x0FF6, 0x00DA, 0x01F0, 0x0135, 0x0010, 0x0FFB,
	0x0FF5, 0x00D4, 0x01EF, 0x013B, 0x0012, 0x0FFB,
	0x0FF4, 0x00CF, 0x01EE, 0x0141, 0x0014, 0x0FFA,
	0x0FF3, 0x00C9, 0x01ED, 0x0147, 0x0017, 0x0FF9,
	0x0FF2, 0x00C4, 0x01EB, 0x014C, 0x001A, 0x0FF9,
	0x0FF1, 0x00BF, 0x01EA, 0x0152, 0x001C, 0x0FF8,
	0x0FF1, 0x00B9, 0x01E8, 0x0157, 0x001F, 0x0FF8,
	0x0FF0, 0x00B4, 0x01E6, 0x015D, 0x0022, 0x0FF7,
	0x0FF0, 0x00AE, 0x01E4, 0x0163, 0x0025, 0x0FF6,
	0x0FEF, 0x00A9, 0x01E2, 0x0168, 0x0028, 0x0FF6,
	0x0FEF, 0x00A4, 0x01DF, 0x016E, 0x002B, 0x0FF5,
	0x0FEF, 0x009F, 0x01DD, 0x0172, 0x002E, 0x0FF5,
	0x0FEE, 0x009A, 0x01DA, 0x0178, 0x0032, 0x0FF4,
	0x0FEE, 0x0094, 0x01D8, 0x017E, 0x0035, 0x0FF3,
	0x0FEE, 0x008F, 0x01D5, 0x0182, 0x0039, 0x0FF3,
	0x0FEE, 0x008A, 0x01D2, 0x0188, 0x003C, 0x0FF2,
	0x0FEE, 0x0085, 0x01CF, 0x018C, 0x0040, 0x0FF2,
	0x0FEE, 0x0081, 0x01CB, 0x0191, 0x0044, 0x0FF1,
	0x0FEE, 0x007C, 0x01C8, 0x0196, 0x0047, 0x0FF1,
	0x0FEE, 0x0077, 0x01C4, 0x019C, 0x004B, 0x0FF0,
	0x0FEE, 0x0072, 0x01C1, 0x01A0, 0x004F, 0x0FF0,
	0x0FEE, 0x006E, 0x01BD, 0x01A4, 0x0053, 0x0FF0,
	0x0FEE, 0x0069, 0x01B9, 0x01A9, 0x0058, 0x0FEF,
	0x0FEE, 0x0065, 0x01B5, 0x01AD, 0x005C, 0x0FEF,
	0x0FEF, 0x0060, 0x01B1, 0x01B1, 0x0060, 0x0FEF,
};

//========================================================
// <using>			gen_scaler_coeffs_cnf_file.m
// <using>			make_test_script.m
// <date>			02-Apr-2024
// <coeffDescrip>	6t_64p_LanczosEd_p_0.6_p_10qb_
// <num_taps>		6
// <num_phases>		64
// <scale_ratio>	 input/output = 0.600000000000
// <CoefType>		LanczosEd
// <CoefQuant>		S1.10
//========================================================
static const uint16_t easf_filter_6tap_64p_ratio_0_60[198] = {
	0x0FD9, 0x00FB, 0x0258, 0x00FB, 0x0FD9, 0x0000,
	0x0FD9, 0x00F3, 0x0258, 0x0102, 0x0FDA, 0x0000,
	0x0FD8, 0x00EB, 0x0258, 0x010B, 0x0FDB, 0x0FFF,
	0x0FD8, 0x00E3, 0x0258, 0x0112, 0x0FDC, 0x0FFF,
	0x0FD8, 0x00DC, 0x0257, 0x011B, 0x0FDC, 0x0FFE,
	0x0FD7, 0x00D4, 0x0256, 0x0123, 0x0FDE, 0x0FFE,
	0x0FD7, 0x00CD, 0x0255, 0x012B, 0x0FDF, 0x0FFD,
	0x0FD7, 0x00C5, 0x0254, 0x0133, 0x0FE0, 0x0FFD,
	0x0FD7, 0x00BE, 0x0252, 0x013C, 0x0FE1, 0x0FFC,
	0x0FD7, 0x00B6, 0x0251, 0x0143, 0x0FE3, 0x0FFC,
	0x0FD8, 0x00AF, 0x024F, 0x014B, 0x0FE4, 0x0FFB,
	0x0FD8, 0x00A8, 0x024C, 0x0154, 0x0FE6, 0x0FFA,
	0x0FD8, 0x00A1, 0x024A, 0x015B, 0x0FE8, 0x0FFA,
	0x0FD9, 0x009A, 0x0247, 0x0163, 0x0FEA, 0x0FF9,
	0x0FD9, 0x0093, 0x0244, 0x016C, 0x0FEC, 0x0FF8,
	0x0FD9, 0x008C, 0x0241, 0x0174, 0x0FEF, 0x0FF7,
	0x0FDA, 0x0085, 0x023E, 0x017B, 0x0FF1, 0x0FF7,
	0x0FDB, 0x007F, 0x023A, 0x0183, 0x0FF3, 0x0FF6,
	0x0FDB, 0x0078, 0x0237, 0x018B, 0x0FF6, 0x0FF5,
	0x0FDC, 0x0072, 0x0233, 0x0192, 0x0FF9, 0x0FF4,
	0x0FDD, 0x006C, 0x022F, 0x0199, 0x0FFC, 0x0FF3,
	0x0FDD, 0x0065, 0x022A, 0x01A3, 0x0FFF, 0x0FF2,
	0x0FDE, 0x005F, 0x0226, 0x01AA, 0x0002, 0x0FF1,
	0x0FDF, 0x005A, 0x0221, 0x01B0, 0x0006, 0x0FF0,
	0x0FE0, 0x0054, 0x021C, 0x01B7, 0x0009, 0x0FF0,
	0x0FE1, 0x004E, 0x0217, 0x01BE, 0x000D, 0x0FEF,
	0x0FE2, 0x0048, 0x0212, 0x01C6, 0x0010, 0x0FEE,
	0x0FE3, 0x0043, 0x020C, 0x01CD, 0x0014, 0x0FED,
	0x0FE4, 0x003E, 0x0207, 0x01D3, 0x0018, 0x0FEC,
	0x0FE5, 0x0039, 0x0200, 0x01DA, 0x001D, 0x0FEB,
	0x0FE6, 0x0034, 0x01FA, 0x01E1, 0x0021, 0x0FEA,
	0x0FE7, 0x002F, 0x01F5, 0x01E7, 0x0025, 0x0FE9,
	0x0FE8, 0x002A, 0x01EE, 0x01EE, 0x002A, 0x0FE8,
};

//========================================================
// <using>			gen_scaler_coeffs_cnf_file.m
// <using>			make_test_script.m
// <date>			02-Apr-2024
// <coeffDescrip>	6t_64p_LanczosEd_p_0.7_p_10qb_
// <num_taps>		6
// <num_phases>		64
// <scale_ratio>	 input/output = 0.700000000000
// <CoefType>		LanczosEd
// <CoefQuant>		S1.10
//========================================================
static const uint16_t easf_filter_6tap_64p_ratio_0_70[198] = {
	0x0FC0, 0x00DA, 0x02CC, 0x00DA, 0x0FC0, 0x0000,
	0x0FC1, 0x00D0, 0x02CC, 0x00E4, 0x0FBF, 0x0000,
	0x0FC2, 0x00C6, 0x02CB, 0x00EF, 0x0FBE, 0x0000,
	0x0FC3, 0x00BC, 0x02CA, 0x00F9, 0x0FBE, 0x0000,
	0x0FC4, 0x00B2, 0x02C9, 0x0104, 0x0FBD, 0x0000,
	0x0FC5, 0x00A8, 0x02C7, 0x010F, 0x0FBD, 0x0000,
	0x0FC7, 0x009F, 0x02C5, 0x0119, 0x0FBC, 0x0000,
	0x0FC8, 0x0095, 0x02C3, 0x0124, 0x0FBC, 0x0000,
	0x0FC9, 0x008C, 0x02C0, 0x012F, 0x0FBC, 0x0000,
	0x0FCB, 0x0083, 0x02BD, 0x0139, 0x0FBC, 0x0000,
	0x0FCC, 0x007A, 0x02BA, 0x0144, 0x0FBC, 0x0000,
	0x0FCE, 0x0072, 0x02B6, 0x014D, 0x0FBD, 0x0000,
	0x0FD0, 0x0069, 0x02B2, 0x0159, 0x0FBD, 0x0FFF,
	0x0FD1, 0x0061, 0x02AD, 0x0164, 0x0FBE, 0x0FFF,
	0x0FD3, 0x0059, 0x02A9, 0x016E, 0x0FBF, 0x0FFE,
	0x0FD4, 0x0051, 0x02A4, 0x017A, 0x0FBF, 0x0FFE,
	0x0FD6, 0x0049, 0x029E, 0x0184, 0x0FC1, 0x0FFE,
	0x0FD8, 0x0042, 0x0299, 0x018E, 0x0FC2, 0x0FFD,
	0x0FD9, 0x003A, 0x0293, 0x019B, 0x0FC3, 0x0FFC,
	0x0FDB, 0x0033, 0x028D, 0x01A4, 0x0FC5, 0x0FFC,
	0x0FDC, 0x002D, 0x0286, 0x01AF, 0x0FC7, 0x0FFB,
	0x0FDE, 0x0026, 0x0280, 0x01BA, 0x0FC8, 0x0FFA,
	0x0FE0, 0x001F, 0x0279, 0x01C4, 0x0FCB, 0x0FF9,
	0x0FE1, 0x0019, 0x0272, 0x01CE, 0x0FCD, 0x0FF9,
	0x0FE3, 0x0013, 0x026A, 0x01D9, 0x0FCF, 0x0FF8,
	0x0FE4, 0x000D, 0x0263, 0x01E3, 0x0FD2, 0x0FF7,
	0x0FE6, 0x0008, 0x025B, 0x01EC, 0x0FD5, 0x0FF6,
	0x0FE7, 0x0002, 0x0253, 0x01F7, 0x0FD8, 0x0FF5,
	0x0FE9, 0x0FFD, 0x024A, 0x0202, 0x0FDB, 0x0FF3,
	0x0FEA, 0x0FF8, 0x0242, 0x020B, 0x0FDF, 0x0FF2,
	0x0FEC, 0x0FF3, 0x0239, 0x0215, 0x0FE2, 0x0FF1,
	0x0FED, 0x0FEF, 0x0230, 0x021E, 0x0FE6, 0x0FF0,
	0x0FEF, 0x0FEB, 0x0226, 0x0226, 0x0FEB, 0x0FEF,
};

//========================================================
// <using>			gen_scaler_coeffs_cnf_file.m
// <using>			make_test_script.m
// <date>			02-Apr-2024
// <coeffDescrip>	6t_64p_LanczosEd_p_0.8_p_10qb_
// <num_taps>		6
// <num_phases>		64
// <scale_ratio>	 input/output = 0.800000000000
// <CoefType>		LanczosEd
// <CoefQuant>		S1.10
//========================================================
static const uint16_t easf_filter_6tap_64p_ratio_0_80[198] = {
	0x0FBF, 0x00A1, 0x0340, 0x00A1, 0x0FBF, 0x0000,
	0x0FC1, 0x0095, 0x0340, 0x00AD, 0x0FBC, 0x0001,
	0x0FC4, 0x0089, 0x033E, 0x00BA, 0x0FBA, 0x0001,
	0x0FC6, 0x007D, 0x033D, 0x00C6, 0x0FB8, 0x0002,
	0x0FC9, 0x0072, 0x033A, 0x00D3, 0x0FB6, 0x0002,
	0x0FCC, 0x0067, 0x0338, 0x00DF, 0x0FB3, 0x0003,
	0x0FCE, 0x005C, 0x0334, 0x00EE, 0x0FB1, 0x0003,
	0x0FD1, 0x0051, 0x0331, 0x00FA, 0x0FAF, 0x0004,
	0x0FD3, 0x0047, 0x032D, 0x0108, 0x0FAD, 0x0004,
	0x0FD6, 0x003D, 0x0328, 0x0116, 0x0FAB, 0x0004,
	0x0FD8, 0x0033, 0x0323, 0x0123, 0x0FAA, 0x0005,
	0x0FDB, 0x002A, 0x031D, 0x0131, 0x0FA8, 0x0005,
	0x0FDD, 0x0021, 0x0317, 0x013F, 0x0FA7, 0x0005,
	0x0FDF, 0x0018, 0x0311, 0x014D, 0x0FA5, 0x0006,
	0x0FE2, 0x0010, 0x030A, 0x015A, 0x0FA4, 0x0006,
	0x0FE4, 0x0008, 0x0302, 0x0169, 0x0FA3, 0x0006,
	0x0FE6, 0x0000, 0x02FB, 0x0177, 0x0FA2, 0x0006,
	0x0FE8, 0x0FF9, 0x02F3, 0x0185, 0x0FA1, 0x0006,
	0x0FEB, 0x0FF1, 0x02EA, 0x0193, 0x0FA1, 0x0006,
	0x0FED, 0x0FEB, 0x02E1, 0x01A1, 0x0FA0, 0x0006,
	0x0FEE, 0x0FE4, 0x02D8, 0x01B0, 0x0FA0, 0x0006,
	0x0FF0, 0x0FDE, 0x02CE, 0x01BE, 0x0FA0, 0x0006,
	0x0FF2, 0x0FD8, 0x02C5, 0x01CB, 0x0FA0, 0x0006,
	0x0FF4, 0x0FD3, 0x02BA, 0x01D8, 0x0FA1, 0x0006,
	0x0FF6, 0x0FCD, 0x02B0, 0x01E7, 0x0FA1, 0x0005,
	0x0FF7, 0x0FC8, 0x02A5, 0x01F5, 0x0FA2, 0x0005,
	0x0FF9, 0x0FC4, 0x029A, 0x0202, 0x0FA3, 0x0004,
	0x0FFA, 0x0FC0, 0x028E, 0x0210, 0x0FA4, 0x0004,
	0x0FFB, 0x0FBC, 0x0283, 0x021D, 0x0FA6, 0x0003,
	0x0FFD, 0x0FB8, 0x0276, 0x022A, 0x0FA8, 0x0003,
	0x0FFE, 0x0FB4, 0x026B, 0x0237, 0x0FAA, 0x0002,
	0x0FFF, 0x0FB1, 0x025E, 0x0245, 0x0FAC, 0x0001,
	0x0000, 0x0FAE, 0x0252, 0x0252, 0x0FAE, 0x0000,
};

//========================================================
// <using>			gen_scaler_coeffs_cnf_file.m
// <using>			make_test_script.m
// <date>			02-Apr-2024
// <coeffDescrip>	6t_64p_LanczosEd_p_0.9_p_10qb_
// <num_taps>		6
// <num_phases>		64
// <scale_ratio>	 input/output = 0.900000000000
// <CoefType>		LanczosEd
// <CoefQuant>		S1.10
//========================================================
static const uint16_t easf_filter_6tap_64p_ratio_0_90[198] = {
	0x0FD8, 0x0055, 0x03A7, 0x0054, 0x0FD8, 0x0000,
	0x0FDB, 0x0047, 0x03A7, 0x0063, 0x0FD4, 0x0000,
	0x0FDF, 0x003B, 0x03A5, 0x006F, 0x0FD1, 0x0001,
	0x0FE2, 0x002E, 0x03A3, 0x007E, 0x0FCD, 0x0002,
	0x0FE5, 0x0022, 0x03A0, 0x008D, 0x0FCA, 0x0002,
	0x0FE8, 0x0017, 0x039D, 0x009B, 0x0FC6, 0x0003,
	0x0FEB, 0x000C, 0x0398, 0x00AC, 0x0FC2, 0x0003,
	0x0FEE, 0x0001, 0x0394, 0x00BA, 0x0FBF, 0x0004,
	0x0FF1, 0x0FF7, 0x038E, 0x00CA, 0x0FBB, 0x0005,
	0x0FF4, 0x0FED, 0x0388, 0x00DA, 0x0FB8, 0x0005,
	0x0FF6, 0x0FE4, 0x0381, 0x00EB, 0x0FB4, 0x0006,
	0x0FF9, 0x0FDB, 0x037A, 0x00FA, 0x0FB1, 0x0007,
	0x0FFB, 0x0FD3, 0x0372, 0x010B, 0x0FAD, 0x0008,
	0x0FFD, 0x0FCB, 0x0369, 0x011D, 0x0FAA, 0x0008,
	0x0000, 0x0FC3, 0x0360, 0x012E, 0x0FA6, 0x0009,
	0x0002, 0x0FBC, 0x0356, 0x013F, 0x0FA3, 0x000A,
	0x0003, 0x0FB6, 0x034C, 0x0150, 0x0FA0, 0x000B,
	0x0005, 0x0FB0, 0x0341, 0x0162, 0x0F9D, 0x000B,
	0x0007, 0x0FAA, 0x0336, 0x0173, 0x0F9A, 0x000C,
	0x0008, 0x0FA5, 0x032A, 0x0185, 0x0F97, 0x000D,
	0x000A, 0x0FA0, 0x031E, 0x0197, 0x0F94, 0x000D,
	0x000B, 0x0F9B, 0x0311, 0x01A9, 0x0F92, 0x000E,
	0x000C, 0x0F97, 0x0303, 0x01BC, 0x0F8F, 0x000F,
	0x000D, 0x0F94, 0x02F6, 0x01CD, 0x0F8D, 0x000F,
	0x000E, 0x0F91, 0x02E8, 0x01DE, 0x0F8B, 0x0010,
	0x000F, 0x0F8E, 0x02D9, 0x01F1, 0x0F89, 0x0010,
	0x0010, 0x0F8B, 0x02CA, 0x0202, 0x0F88, 0x0011,
	0x0010, 0x0F89, 0x02BB, 0x0214, 0x0F87, 0x0011,
	0x0011, 0x0F87, 0x02AB, 0x0226, 0x0F86, 0x0011,
	0x0011, 0x0F86, 0x029C, 0x0236, 0x0F85, 0x0012,
	0x0011, 0x0F85, 0x028B, 0x0249, 0x0F84, 0x0012,
	0x0012, 0x0F84, 0x027B, 0x0259, 0x0F84, 0x0012,
	0x0012, 0x0F84, 0x026A, 0x026A, 0x0F84, 0x0012,
};

//========================================================
// <using>			gen_scaler_coeffs_cnf_file.m
// <using>			make_test_script.m
// <date>			02-Apr-2024
// <coeffDescrip>	6t_64p_LanczosEd_p_1_p_10qb_
// <num_taps>		6
// <num_phases>		64
// <scale_ratio>	 input/output = 1.000000000000
// <CoefType>		LanczosEd
// <CoefQuant>		S1.10
//========================================================
static const uint16_t easf_filter_6tap_64p_ratio_1_00[198] = {
	0x0000, 0x0000, 0x0400, 0x0000, 0x0000, 0x0000,
	0x0003, 0x0FF3, 0x0400, 0x000D, 0x0FFD, 0x0000,
	0x0006, 0x0FE7, 0x03FE, 0x001C, 0x0FF9, 0x0000,
	0x0009, 0x0FDB, 0x03FC, 0x002B, 0x0FF5, 0x0000,
	0x000C, 0x0FD0, 0x03F9, 0x003A, 0x0FF1, 0x0000,
	0x000E, 0x0FC5, 0x03F5, 0x004A, 0x0FED, 0x0001,
	0x0011, 0x0FBB, 0x03F0, 0x005A, 0x0FE9, 0x0001,
	0x0013, 0x0FB2, 0x03EB, 0x006A, 0x0FE5, 0x0001,
	0x0015, 0x0FA9, 0x03E4, 0x007B, 0x0FE1, 0x0002,
	0x0017, 0x0FA1, 0x03DD, 0x008D, 0x0FDC, 0x0002,
	0x0018, 0x0F99, 0x03D4, 0x00A0, 0x0FD8, 0x0003,
	0x001A, 0x0F92, 0x03CB, 0x00B2, 0x0FD3, 0x0004,
	0x001B, 0x0F8C, 0x03C1, 0x00C6, 0x0FCE, 0x0004,
	0x001C, 0x0F86, 0x03B7, 0x00D9, 0x0FC9, 0x0005,
	0x001D, 0x0F80, 0x03AB, 0x00EE, 0x0FC4, 0x0006,
	0x001E, 0x0F7C, 0x039F, 0x0101, 0x0FBF, 0x0007,
	0x001F, 0x0F78, 0x0392, 0x0115, 0x0FBA, 0x0008,
	0x001F, 0x0F74, 0x0385, 0x012B, 0x0FB5, 0x0008,
	0x0020, 0x0F71, 0x0376, 0x0140, 0x0FB0, 0x0009,
	0x0020, 0x0F6E, 0x0367, 0x0155, 0x0FAB, 0x000B,
	0x0020, 0x0F6C, 0x0357, 0x016B, 0x0FA6, 0x000C,
	0x0020, 0x0F6A, 0x0347, 0x0180, 0x0FA2, 0x000D,
	0x0020, 0x0F69, 0x0336, 0x0196, 0x0F9D, 0x000E,
	0x0020, 0x0F69, 0x0325, 0x01AB, 0x0F98, 0x000F,
	0x001F, 0x0F68, 0x0313, 0x01C3, 0x0F93, 0x0010,
	0x001F, 0x0F69, 0x0300, 0x01D8, 0x0F8F, 0x0011,
	0x001E, 0x0F69, 0x02ED, 0x01EF, 0x0F8B, 0x0012,
	0x001D, 0x0F6A, 0x02D9, 0x0205, 0x0F87, 0x0014,
	0x001D, 0x0F6C, 0x02C5, 0x021A, 0x0F83, 0x0015,
	0x001C, 0x0F6E, 0x02B1, 0x0230, 0x0F7F, 0x0016,
	0x001B, 0x0F70, 0x029C, 0x0247, 0x0F7B, 0x0017,
	0x001A, 0x0F72, 0x0287, 0x025D, 0x0F78, 0x0018,
	0x0019, 0x0F75, 0x0272, 0x0272, 0x0F75, 0x0019,
};

/* Converted scaler coeff tables from S1.10 to S1.12 */
static uint16_t easf_filter_3tap_64p_ratio_0_30_s1_12[99];
static uint16_t easf_filter_3tap_64p_ratio_0_40_s1_12[99];
static uint16_t easf_filter_3tap_64p_ratio_0_50_s1_12[99];
static uint16_t easf_filter_3tap_64p_ratio_0_60_s1_12[99];
static uint16_t easf_filter_3tap_64p_ratio_0_70_s1_12[99];
static uint16_t easf_filter_3tap_64p_ratio_0_80_s1_12[99];
static uint16_t easf_filter_3tap_64p_ratio_0_90_s1_12[99];
static uint16_t easf_filter_3tap_64p_ratio_1_00_s1_12[99];
static uint16_t easf_filter_4tap_64p_ratio_0_30_s1_12[132];
static uint16_t easf_filter_4tap_64p_ratio_0_40_s1_12[132];
static uint16_t easf_filter_4tap_64p_ratio_0_50_s1_12[132];
static uint16_t easf_filter_4tap_64p_ratio_0_60_s1_12[132];
static uint16_t easf_filter_4tap_64p_ratio_0_70_s1_12[132];
static uint16_t easf_filter_4tap_64p_ratio_0_80_s1_12[132];
static uint16_t easf_filter_4tap_64p_ratio_0_90_s1_12[132];
static uint16_t easf_filter_4tap_64p_ratio_1_00_s1_12[132];
static uint16_t easf_filter_6tap_64p_ratio_0_30_s1_12[198];
static uint16_t easf_filter_6tap_64p_ratio_0_40_s1_12[198];
static uint16_t easf_filter_6tap_64p_ratio_0_50_s1_12[198];
static uint16_t easf_filter_6tap_64p_ratio_0_60_s1_12[198];
static uint16_t easf_filter_6tap_64p_ratio_0_70_s1_12[198];
static uint16_t easf_filter_6tap_64p_ratio_0_80_s1_12[198];
static uint16_t easf_filter_6tap_64p_ratio_0_90_s1_12[198];
static uint16_t easf_filter_6tap_64p_ratio_1_00_s1_12[198];

struct scale_ratio_to_reg_value_lookup easf_v_bf3_mode_lookup[] = {
	{3, 10, 0x0000},
	{4, 10, 0x0000},
	{5, 10, 0x0000},
	{6, 10, 0x0000},
	{7, 10, 0x0000},
	{8, 10, 0x0000},
	{9, 10, 0x0000},
	{1, 1, 0x0000},
	{-1, -1, 0x0002},
};

struct scale_ratio_to_reg_value_lookup easf_h_bf3_mode_lookup[] = {
	{3, 10, 0x0000},
	{4, 10, 0x0000},
	{5, 10, 0x0000},
	{6, 10, 0x0000},
	{7, 10, 0x0000},
	{8, 10, 0x0000},
	{9, 10, 0x0000},
	{1, 1, 0x0000},
	{-1, -1, 0x0002},
};

struct scale_ratio_to_reg_value_lookup easf_reducer_gain6_6tap_lookup[] = {
	{3, 10, 0x4100},
	{4, 10, 0x4100},
	{5, 10, 0x4100},
	{6, 10, 0x4100},
	{7, 10, 0x4100},
	{8, 10, 0x4100},
	{9, 10, 0x4100},
	{1, 1, 0x4100},
	{-1, -1, 0x4100},
};

struct scale_ratio_to_reg_value_lookup easf_reducer_gain4_6tap_lookup[] = {
	{3, 10, 0x4000},
	{4, 10, 0x4000},
	{5, 10, 0x4000},
	{6, 10, 0x4000},
	{7, 10, 0x4000},
	{8, 10, 0x4000},
	{9, 10, 0x4000},
	{1, 1, 0x4000},
	{-1, -1, 0x4000},
};

struct scale_ratio_to_reg_value_lookup easf_gain_ring6_6tap_lookup[] = {
	{3, 10, 0x0000},
	{4, 10, 0x251F},
	{5, 10, 0x291F},
	{6, 10, 0xA51F},
	{7, 10, 0xA51F},
	{8, 10, 0xAA66},
	{9, 10, 0xA51F},
	{1, 1, 0xA640},
	{-1, -1, 0xA640},
};

struct scale_ratio_to_reg_value_lookup easf_gain_ring4_6tap_lookup[] = {
	{3, 10, 0x0000},
	{4, 10, 0x9600},
	{5, 10, 0xA460},
	{6, 10, 0xA8E0},
	{7, 10, 0xAC00},
	{8, 10, 0xAD20},
	{9, 10, 0xAFC0},
	{1, 1, 0xB058},
	{-1, -1, 0xB058},
};

struct scale_ratio_to_reg_value_lookup easf_reducer_gain6_4tap_lookup[] = {
	{3, 10, 0x4100},
	{4, 10, 0x4100},
	{5, 10, 0x4100},
	{6, 10, 0x4100},
	{7, 10, 0x4100},
	{8, 10, 0x4100},
	{9, 10, 0x4100},
	{1, 1, 0x4100},
	{-1, -1, 0x4100},
};

struct scale_ratio_to_reg_value_lookup easf_reducer_gain4_4tap_lookup[] = {
	{3, 10, 0x4000},
	{4, 10, 0x4000},
	{5, 10, 0x4000},
	{6, 10, 0x4000},
	{7, 10, 0x4000},
	{8, 10, 0x4000},
	{9, 10, 0x4000},
	{1, 1, 0x4000},
	{-1, -1, 0x4000},
};

struct scale_ratio_to_reg_value_lookup easf_gain_ring6_4tap_lookup[] = {
	{3, 10, 0x0000},
	{4, 10, 0x0000},
	{5, 10, 0x0000},
	{6, 10, 0x0000},
	{7, 10, 0x0000},
	{8, 10, 0x0000},
	{9, 10, 0x0000},
	{1, 1, 0x0000},
	{-1, -1, 0x0000},
};

struct scale_ratio_to_reg_value_lookup easf_gain_ring4_4tap_lookup[] = {
	{3, 10, 0x0000},
	{4, 10, 0x0000},
	{5, 10, 0x0000},
	{6, 10, 0x9900},
	{7, 10, 0xA100},
	{8, 10, 0xA8C0},
	{9, 10, 0xAB20},
	{1, 1, 0xAC00},
	{-1, -1, 0xAC00},
};

struct scale_ratio_to_reg_value_lookup easf_3tap_dntilt_uptilt_offset_lookup[] = {
	{3, 10, 0x0000},
	{4, 10, 0x0000},
	{5, 10, 0x0000},
	{6, 10, 0x0000},
	{7, 10, 0x0000},
	{8, 10, 0x4100},
	{9, 10, 0x9F00},
	{1, 1, 0xA4C0},
	{-1, -1, 0xA8D8},
};

struct scale_ratio_to_reg_value_lookup easf_3tap_uptilt_maxval_lookup[] = {
	{3, 10, 0x0000},
	{4, 10, 0x0000},
	{5, 10, 0x0000},
	{6, 10, 0x0000},
	{7, 10, 0x0000},
	{8, 10, 0x4000},
	{9, 10, 0x24FE},
	{1, 1, 0x2D64},
	{-1, -1, 0x3ADB},
};

struct scale_ratio_to_reg_value_lookup easf_3tap_dntilt_slope_lookup[] = {
	{3, 10, 0x3800},
	{4, 10, 0x3800},
	{5, 10, 0x3800},
	{6, 10, 0x3800},
	{7, 10, 0x3800},
	{8, 10, 0x3886},
	{9, 10, 0x3940},
	{1, 1, 0x3A4E},
	{-1, -1, 0x3B66},
};

struct scale_ratio_to_reg_value_lookup easf_3tap_uptilt1_slope_lookup[] = {
	{3, 10, 0x3800},
	{4, 10, 0x3800},
	{5, 10, 0x3800},
	{6, 10, 0x3800},
	{7, 10, 0x3800},
	{8, 10, 0x36F4},
	{9, 10, 0x359C},
	{1, 1, 0x3360},
	{-1, -1, 0x2F20},
};

struct scale_ratio_to_reg_value_lookup easf_3tap_uptilt2_slope_lookup[] = {
	{3, 10, 0x0000},
	{4, 10, 0x0000},
	{5, 10, 0x0000},
	{6, 10, 0x0000},
	{7, 10, 0x0000},
	{8, 10, 0x0000},
	{9, 10, 0x359C},
	{1, 1, 0x31F0},
	{-1, -1, 0x1F00},
};

struct scale_ratio_to_reg_value_lookup easf_3tap_uptilt2_offset_lookup[] = {
	{3, 10, 0x0000},
	{4, 10, 0x0000},
	{5, 10, 0x0000},
	{6, 10, 0x0000},
	{7, 10, 0x0000},
	{8, 10, 0x0000},
	{9, 10, 0x9F00},
	{1, 1, 0xA400},
	{-1, -1, 0x9E00},
};

void spl_init_easf_filter_coeffs(void)
{
	convert_filter_s1_10_to_s1_12(easf_filter_3tap_64p_ratio_0_30,
		easf_filter_3tap_64p_ratio_0_30_s1_12, 3);
	convert_filter_s1_10_to_s1_12(easf_filter_3tap_64p_ratio_0_40,
		easf_filter_3tap_64p_ratio_0_40_s1_12, 3);
	convert_filter_s1_10_to_s1_12(easf_filter_3tap_64p_ratio_0_50,
		easf_filter_3tap_64p_ratio_0_50_s1_12, 3);
	convert_filter_s1_10_to_s1_12(easf_filter_3tap_64p_ratio_0_60,
		easf_filter_3tap_64p_ratio_0_60_s1_12, 3);
	convert_filter_s1_10_to_s1_12(easf_filter_3tap_64p_ratio_0_70,
		easf_filter_3tap_64p_ratio_0_70_s1_12, 3);
	convert_filter_s1_10_to_s1_12(easf_filter_3tap_64p_ratio_0_80,
		easf_filter_3tap_64p_ratio_0_80_s1_12, 3);
	convert_filter_s1_10_to_s1_12(easf_filter_3tap_64p_ratio_0_90,
		easf_filter_3tap_64p_ratio_0_90_s1_12, 3);
	convert_filter_s1_10_to_s1_12(easf_filter_3tap_64p_ratio_1_00,
		easf_filter_3tap_64p_ratio_1_00_s1_12, 3);

	convert_filter_s1_10_to_s1_12(easf_filter_4tap_64p_ratio_0_30,
		easf_filter_4tap_64p_ratio_0_30_s1_12, 4);
	convert_filter_s1_10_to_s1_12(easf_filter_4tap_64p_ratio_0_40,
		easf_filter_4tap_64p_ratio_0_40_s1_12, 4);
	convert_filter_s1_10_to_s1_12(easf_filter_4tap_64p_ratio_0_50,
		easf_filter_4tap_64p_ratio_0_50_s1_12, 4);
	convert_filter_s1_10_to_s1_12(easf_filter_4tap_64p_ratio_0_60,
		easf_filter_4tap_64p_ratio_0_60_s1_12, 4);
	convert_filter_s1_10_to_s1_12(easf_filter_4tap_64p_ratio_0_70,
		easf_filter_4tap_64p_ratio_0_70_s1_12, 4);
	convert_filter_s1_10_to_s1_12(easf_filter_4tap_64p_ratio_0_80,
		easf_filter_4tap_64p_ratio_0_80_s1_12, 4);
	convert_filter_s1_10_to_s1_12(easf_filter_4tap_64p_ratio_0_90,
		easf_filter_4tap_64p_ratio_0_90_s1_12, 4);
	convert_filter_s1_10_to_s1_12(easf_filter_4tap_64p_ratio_1_00,
		easf_filter_4tap_64p_ratio_1_00_s1_12, 4);

	convert_filter_s1_10_to_s1_12(easf_filter_6tap_64p_ratio_0_30,
		easf_filter_6tap_64p_ratio_0_30_s1_12, 6);
	convert_filter_s1_10_to_s1_12(easf_filter_6tap_64p_ratio_0_40,
		easf_filter_6tap_64p_ratio_0_40_s1_12, 6);
	convert_filter_s1_10_to_s1_12(easf_filter_6tap_64p_ratio_0_50,
		easf_filter_6tap_64p_ratio_0_50_s1_12, 6);
	convert_filter_s1_10_to_s1_12(easf_filter_6tap_64p_ratio_0_60,
		easf_filter_6tap_64p_ratio_0_60_s1_12, 6);
	convert_filter_s1_10_to_s1_12(easf_filter_6tap_64p_ratio_0_70,
		easf_filter_6tap_64p_ratio_0_70_s1_12, 6);
	convert_filter_s1_10_to_s1_12(easf_filter_6tap_64p_ratio_0_80,
		easf_filter_6tap_64p_ratio_0_80_s1_12, 6);
	convert_filter_s1_10_to_s1_12(easf_filter_6tap_64p_ratio_0_90,
		easf_filter_6tap_64p_ratio_0_90_s1_12, 6);
	convert_filter_s1_10_to_s1_12(easf_filter_6tap_64p_ratio_1_00,
		easf_filter_6tap_64p_ratio_1_00_s1_12, 6);
}

uint16_t *spl_get_easf_filter_3tap_64p(struct spl_fixed31_32 ratio)
{
	if (ratio.value < spl_fixpt_from_fraction(3, 10).value)
		return easf_filter_3tap_64p_ratio_0_30_s1_12;
	else if (ratio.value < spl_fixpt_from_fraction(4, 10).value)
		return easf_filter_3tap_64p_ratio_0_40_s1_12;
	else if (ratio.value < spl_fixpt_from_fraction(5, 10).value)
		return easf_filter_3tap_64p_ratio_0_50_s1_12;
	else if (ratio.value < spl_fixpt_from_fraction(6, 10).value)
		return easf_filter_3tap_64p_ratio_0_60_s1_12;
	else if (ratio.value < spl_fixpt_from_fraction(7, 10).value)
		return easf_filter_3tap_64p_ratio_0_70_s1_12;
	else if (ratio.value < spl_fixpt_from_fraction(8, 10).value)
		return easf_filter_3tap_64p_ratio_0_80_s1_12;
	else if (ratio.value < spl_fixpt_from_fraction(9, 10).value)
		return easf_filter_3tap_64p_ratio_0_90_s1_12;
	else
		return easf_filter_3tap_64p_ratio_1_00_s1_12;
}

uint16_t *spl_get_easf_filter_4tap_64p(struct spl_fixed31_32 ratio)
{
	if (ratio.value < spl_fixpt_from_fraction(3, 10).value)
		return easf_filter_4tap_64p_ratio_0_30_s1_12;
	else if (ratio.value < spl_fixpt_from_fraction(4, 10).value)
		return easf_filter_4tap_64p_ratio_0_40_s1_12;
	else if (ratio.value < spl_fixpt_from_fraction(5, 10).value)
		return easf_filter_4tap_64p_ratio_0_50_s1_12;
	else if (ratio.value < spl_fixpt_from_fraction(6, 10).value)
		return easf_filter_4tap_64p_ratio_0_60_s1_12;
	else if (ratio.value < spl_fixpt_from_fraction(7, 10).value)
		return easf_filter_4tap_64p_ratio_0_70_s1_12;
	else if (ratio.value < spl_fixpt_from_fraction(8, 10).value)
		return easf_filter_4tap_64p_ratio_0_80_s1_12;
	else if (ratio.value < spl_fixpt_from_fraction(9, 10).value)
		return easf_filter_4tap_64p_ratio_0_90_s1_12;
	else
		return easf_filter_4tap_64p_ratio_1_00_s1_12;
}

uint16_t *spl_get_easf_filter_6tap_64p(struct spl_fixed31_32 ratio)
{
	if (ratio.value < spl_fixpt_from_fraction(3, 10).value)
		return easf_filter_6tap_64p_ratio_0_30_s1_12;
	else if (ratio.value < spl_fixpt_from_fraction(4, 10).value)
		return easf_filter_6tap_64p_ratio_0_40_s1_12;
	else if (ratio.value < spl_fixpt_from_fraction(5, 10).value)
		return easf_filter_6tap_64p_ratio_0_50_s1_12;
	else if (ratio.value < spl_fixpt_from_fraction(6, 10).value)
		return easf_filter_6tap_64p_ratio_0_60_s1_12;
	else if (ratio.value < spl_fixpt_from_fraction(7, 10).value)
		return easf_filter_6tap_64p_ratio_0_70_s1_12;
	else if (ratio.value < spl_fixpt_from_fraction(8, 10).value)
		return easf_filter_6tap_64p_ratio_0_80_s1_12;
	else if (ratio.value < spl_fixpt_from_fraction(9, 10).value)
		return easf_filter_6tap_64p_ratio_0_90_s1_12;
	else
		return easf_filter_6tap_64p_ratio_1_00_s1_12;
}

uint16_t *spl_dscl_get_easf_filter_coeffs_64p(int taps, struct spl_fixed31_32 ratio)
{
	if (taps == 6)
		return spl_get_easf_filter_6tap_64p(ratio);
	else if (taps == 4)
		return spl_get_easf_filter_4tap_64p(ratio);
	else if (taps == 3)
		return spl_get_easf_filter_3tap_64p(ratio);
	else {
		/* should never happen, bug */
		SPL_BREAK_TO_DEBUGGER();
		return NULL;
	}
}

void spl_set_filters_data(struct dscl_prog_data *dscl_prog_data,
		const struct spl_scaler_data *data, bool enable_easf_v,
		bool enable_easf_h)
{
	/*
	 * Old coefficients calculated scaling ratio = input / output
	 * New coefficients are calculated based on = output / input
	 */
	if (enable_easf_h) {
		dscl_prog_data->filter_h = spl_dscl_get_easf_filter_coeffs_64p(
			data->taps.h_taps, data->recip_ratios.horz);

		dscl_prog_data->filter_h_c = spl_dscl_get_easf_filter_coeffs_64p(
			data->taps.h_taps_c, data->recip_ratios.horz_c);
	} else {
		dscl_prog_data->filter_h = spl_dscl_get_filter_coeffs_64p(
			data->taps.h_taps, data->ratios.horz);

		dscl_prog_data->filter_h_c = spl_dscl_get_filter_coeffs_64p(
			data->taps.h_taps_c, data->ratios.horz_c);
	}
	if (enable_easf_v) {
		dscl_prog_data->filter_v = spl_dscl_get_easf_filter_coeffs_64p(
			data->taps.v_taps, data->recip_ratios.vert);

		dscl_prog_data->filter_v_c = spl_dscl_get_easf_filter_coeffs_64p(
			data->taps.v_taps_c, data->recip_ratios.vert_c);
	} else {
		dscl_prog_data->filter_v = spl_dscl_get_filter_coeffs_64p(
			data->taps.v_taps, data->ratios.vert);

		dscl_prog_data->filter_v_c = spl_dscl_get_filter_coeffs_64p(
			data->taps.v_taps_c, data->ratios.vert_c);
	}
}

static uint32_t spl_easf_get_scale_ratio_to_reg_value(struct spl_fixed31_32 ratio,
	struct scale_ratio_to_reg_value_lookup *lookup_table_base_ptr,
	unsigned int num_entries)
{
	unsigned int count = 0;
	uint32_t value = 0;
	struct scale_ratio_to_reg_value_lookup *lookup_table_index_ptr;

	lookup_table_index_ptr = (lookup_table_base_ptr + num_entries - 1);
	value = lookup_table_index_ptr->reg_value;

	while (count < num_entries) {

		lookup_table_index_ptr = (lookup_table_base_ptr + count);
		if (lookup_table_index_ptr->numer < 0)
			break;

		if (ratio.value < spl_fixpt_from_fraction(
			lookup_table_index_ptr->numer,
			lookup_table_index_ptr->denom).value) {
			value = lookup_table_index_ptr->reg_value;
			break;
		}

		count++;
	}
	return value;
}
uint32_t spl_get_v_bf3_mode(struct spl_fixed31_32 ratio)
{
	uint32_t value;
	unsigned int num_entries = sizeof(easf_v_bf3_mode_lookup) /
		sizeof(struct scale_ratio_to_reg_value_lookup);
	value = spl_easf_get_scale_ratio_to_reg_value(ratio,
		easf_v_bf3_mode_lookup, num_entries);
	return value;
}
uint32_t spl_get_h_bf3_mode(struct spl_fixed31_32 ratio)
{
	uint32_t value;
	unsigned int num_entries = sizeof(easf_h_bf3_mode_lookup) /
		sizeof(struct scale_ratio_to_reg_value_lookup);
	value = spl_easf_get_scale_ratio_to_reg_value(ratio,
		easf_h_bf3_mode_lookup, num_entries);
	return value;
}
uint32_t spl_get_reducer_gain6(int taps, struct spl_fixed31_32 ratio)
{
	uint32_t value;
	unsigned int num_entries;

	if (taps == 4) {
		num_entries = sizeof(easf_reducer_gain6_4tap_lookup) /
			sizeof(struct scale_ratio_to_reg_value_lookup);
		value = spl_easf_get_scale_ratio_to_reg_value(ratio,
			easf_reducer_gain6_4tap_lookup, num_entries);
	} else if (taps == 6) {
		num_entries = sizeof(easf_reducer_gain6_6tap_lookup) /
			sizeof(struct scale_ratio_to_reg_value_lookup);
		value = spl_easf_get_scale_ratio_to_reg_value(ratio,
			easf_reducer_gain6_6tap_lookup, num_entries);
	} else
		value = 0;
	return value;
}
uint32_t spl_get_reducer_gain4(int taps, struct spl_fixed31_32 ratio)
{
	uint32_t value;
	unsigned int num_entries;

	if (taps == 4) {
		num_entries = sizeof(easf_reducer_gain4_4tap_lookup) /
			sizeof(struct scale_ratio_to_reg_value_lookup);
		value = spl_easf_get_scale_ratio_to_reg_value(ratio,
			easf_reducer_gain4_4tap_lookup, num_entries);
	} else if (taps == 6) {
		num_entries = sizeof(easf_reducer_gain4_6tap_lookup) /
			sizeof(struct scale_ratio_to_reg_value_lookup);
		value = spl_easf_get_scale_ratio_to_reg_value(ratio,
			easf_reducer_gain4_6tap_lookup, num_entries);
	} else
		value = 0;
	return value;
}
uint32_t spl_get_gainRing6(int taps, struct spl_fixed31_32 ratio)
{
	uint32_t value;
	unsigned int num_entries;

	if (taps == 4) {
		num_entries = sizeof(easf_gain_ring6_4tap_lookup) /
			sizeof(struct scale_ratio_to_reg_value_lookup);
		value = spl_easf_get_scale_ratio_to_reg_value(ratio,
			easf_gain_ring6_4tap_lookup, num_entries);
	} else if (taps == 6) {
		num_entries = sizeof(easf_gain_ring6_6tap_lookup) /
			sizeof(struct scale_ratio_to_reg_value_lookup);
		value = spl_easf_get_scale_ratio_to_reg_value(ratio,
			easf_gain_ring6_6tap_lookup, num_entries);
	} else
		value = 0;
	return value;
}
uint32_t spl_get_gainRing4(int taps, struct spl_fixed31_32 ratio)
{
	uint32_t value;
	unsigned int num_entries;

	if (taps == 4) {
		num_entries = sizeof(easf_gain_ring4_4tap_lookup) /
			sizeof(struct scale_ratio_to_reg_value_lookup);
		value = spl_easf_get_scale_ratio_to_reg_value(ratio,
			easf_gain_ring4_4tap_lookup, num_entries);
	} else if (taps == 6) {
		num_entries = sizeof(easf_gain_ring4_6tap_lookup) /
			sizeof(struct scale_ratio_to_reg_value_lookup);
		value = spl_easf_get_scale_ratio_to_reg_value(ratio,
			easf_gain_ring4_6tap_lookup, num_entries);
	} else
		value = 0;
	return value;
}
uint32_t spl_get_3tap_dntilt_uptilt_offset(int taps, struct spl_fixed31_32 ratio)
{
	uint32_t value;
	unsigned int num_entries;

	if (taps == 3) {
		num_entries = sizeof(easf_3tap_dntilt_uptilt_offset_lookup) /
			sizeof(struct scale_ratio_to_reg_value_lookup);
		value = spl_easf_get_scale_ratio_to_reg_value(ratio,
			easf_3tap_dntilt_uptilt_offset_lookup, num_entries);
	} else
		value = 0;
	return value;
}
uint32_t spl_get_3tap_uptilt_maxval(int taps, struct spl_fixed31_32 ratio)
{
	uint32_t value;
	unsigned int num_entries;

	if (taps == 3) {
		num_entries = sizeof(easf_3tap_uptilt_maxval_lookup) /
			sizeof(struct scale_ratio_to_reg_value_lookup);
		value = spl_easf_get_scale_ratio_to_reg_value(ratio,
			easf_3tap_uptilt_maxval_lookup, num_entries);
	} else
		value = 0;
	return value;
}
uint32_t spl_get_3tap_dntilt_slope(int taps, struct spl_fixed31_32 ratio)
{
	uint32_t value;
	unsigned int num_entries;

	if (taps == 3) {
		num_entries = sizeof(easf_3tap_dntilt_slope_lookup) /
			sizeof(struct scale_ratio_to_reg_value_lookup);
		value = spl_easf_get_scale_ratio_to_reg_value(ratio,
			easf_3tap_dntilt_slope_lookup, num_entries);
	} else
		value = 0;
	return value;
}
uint32_t spl_get_3tap_uptilt1_slope(int taps, struct spl_fixed31_32 ratio)
{
	uint32_t value;
	unsigned int num_entries;

	if (taps == 3) {
		num_entries = sizeof(easf_3tap_uptilt1_slope_lookup) /
			sizeof(struct scale_ratio_to_reg_value_lookup);
		value = spl_easf_get_scale_ratio_to_reg_value(ratio,
			easf_3tap_uptilt1_slope_lookup, num_entries);
	} else
		value = 0;
	return value;
}
uint32_t spl_get_3tap_uptilt2_slope(int taps, struct spl_fixed31_32 ratio)
{
	uint32_t value;
	unsigned int num_entries;

	if (taps == 3) {
		num_entries = sizeof(easf_3tap_uptilt2_slope_lookup) /
			sizeof(struct scale_ratio_to_reg_value_lookup);
		value = spl_easf_get_scale_ratio_to_reg_value(ratio,
			easf_3tap_uptilt2_slope_lookup, num_entries);
	} else
		value = 0;
	return value;
}
uint32_t spl_get_3tap_uptilt2_offset(int taps, struct spl_fixed31_32 ratio)
{
	uint32_t value;
	unsigned int num_entries;

	if (taps == 3) {
		num_entries = sizeof(easf_3tap_uptilt2_offset_lookup) /
			sizeof(struct scale_ratio_to_reg_value_lookup);
		value = spl_easf_get_scale_ratio_to_reg_value(ratio,
			easf_3tap_uptilt2_offset_lookup, num_entries);
	} else
		value = 0;
	return value;
}
