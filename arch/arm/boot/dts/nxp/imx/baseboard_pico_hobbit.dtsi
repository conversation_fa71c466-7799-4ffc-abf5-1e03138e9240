/*
 * Copyright 2018 Technexion Ltd.
 *
 * Author: <PERSON><PERSON><PERSON> <<EMAIL>>
 *         <PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 *
 */

#include "tnmacro.h"

/*
 * Difference between hobbit baseboard FL and GL version
 *
 *			  FL                GL
 * ethernet PHY       10_100m LAN        Giga LAN
 * ethernet Reset     PICO_PIN_P42       PICO_PIN_P37
 */

/ {
	sound {
		compatible = "fsl,imx-sgtl5000",
			     "fsl,imx-audio-sgtl5000";
		model = "audio-sgtl5000";
		ssi-controller = <&PICO_AUDA_I2S_CHANNEL>;
		audio-codec = <&codec>;
		audio-routing =
			"MIC_IN", "Mic Jack",
			"Mic Jack", "Mic Bias",
			"Headphone Jack", "HP_OUT";
#if defined(PICO_AUDA_MUX_INT) && defined(PICO_AUDA_MUX_EXT)
		mux-int-port = <PICO_AUDA_MUX_INT>;
		mux-ext-port = <PICO_AUDA_MUX_EXT>;
#else
		fsl,no-audmux = "true";
#endif


	};

	gpio-leds {
		compatible = "gpio-leds";

		led {
			label = "gpio-led";
			gpios = <&PICO_EXT_GPIO_P44 GPIO_ACTIVE_HIGH>; /* PICO_P44 */
		};
	};

	clocks {
		codec_osc: aud_mclk {
			compatible = "fixed-clock";
			#clock-cells = <0>;
			clock-frequency = <********>;
		};
	};
};

PICO_HOBBIT_GPIO_DEFS();

&PICO_I2CA {
	status = "okay";

	codec: sgtl5000@a {
		compatible = "fsl,sgtl5000";
		reg = <0x0a>;
		clocks = <&codec_osc>;
		VDDA-supply = <&reg_2p5v>;
		VDDIO-supply = <&reg_3p3v>;
	};
};

&PICO_I2CB {
	status = "okay";

	adc: adc081c@50 {
		compatible = "ti,adc081c";
		reg = <0x50>;
		vref-supply = <&reg_3p3v>;
	};

	polytouch: edt-ft5x06@38 {
		compatible = "edt,edt-ft5x06";
		reg = <0x38>;
		interrupt-parent = <&GPIO_BANK(PICO_EXT_GPIO_P25)>; /* PICO_P25 */
		interrupts = <GPIO_NO(PICO_EXT_GPIO_P25) 0>;
		reset-gpios = <&PICO_EXT_GPIO_P32 GPIO_ACTIVE_LOW>; /* PICO_P32 */
		touchscreen-size-x = <800>;
		touchscreen-size-y = <480>;
	};
};

&PICO_I2CC {
	status = "okay";
};

&PICO_SPIA {
	status = "okay";

	ads7846@0 {
		reg = <0>;  /* CS0 */
		compatible = "ti,ads7846";
		interrupt-parent = <&GPIO_BANK(PICO_EXT_GPIO_P48)>; /* PICO_P48 */
		interrupts = <GPIO_NO(PICO_EXT_GPIO_P48) IRQ_TYPE_EDGE_FALLING>;
		spi-max-frequency = <1000000>;
		pendown-gpio = <&PICO_EXT_GPIO_P48 GPIO_ACTIVE_LOW>; /* PICO_P48 */
		vcc-supply = <&reg_3p3v>;

		ti,x-min = /bits/ 16 <0>;
		ti,x-max = /bits/ 16 <4095>;
		ti,y-min = /bits/ 16 <0>;
		ti,y-max = /bits/ 16 <4095>;
		ti,pressure-max = /bits/ 16 <1024>;
		ti,x-plate-ohms = /bits/ 16 <90>;
		ti,y-plate-ohms = /bits/ 16 <90>;
		ti,debounce-max = /bits/ 16 <70>;
		ti,debounce-tol = /bits/ 16 <3>;
		ti,debounce-rep = /bits/ 16 <2>;
		ti,settle-delay-usec = /bits/ 16 <150>;

		linux,wakeup;
	};
};

