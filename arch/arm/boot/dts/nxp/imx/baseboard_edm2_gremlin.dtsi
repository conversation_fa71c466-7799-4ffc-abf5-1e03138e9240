/*
 * Copyright 2017 Technexion Ltd.
 *
 * Author: <PERSON><PERSON><PERSON> <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License version 2 as
 * published by the Free Software Foundation.
 *
 */

#include "tnmacro.h"

/ {
	gpio-edm {
		compatible = "gpio-edm";

		gpio_p255 {
			label = "GPIO_P255";
			gpios = <&EDM_EXT_GPIO_P255 GPIO_ACTIVE_HIGH>;
			dir   = "out";
		};
		gpio_p256 {
			label = "GPIO_P256";
			gpios = <&EDM_EXT_GPIO_P256 GPIO_ACTIVE_HIGH>;
			dir   = "out";
		};
		gpio_p257 {
			label = "GPIO_P257";
			gpios = <&EDM_EXT_GPIO_P257 GPIO_ACTIVE_HIGH>;
			dir   = "out";
		};
		gpio_p258 {
			label = "GPIO_P258";
			gpios = <&EDM_EXT_GPIO_P258 GPIO_ACTIVE_HIGH>;
			dir   = "out";
		};
		gpio_p259 {
			label = "GPIO_P259";
			gpios = <&EDM_EXT_GPIO_P259 GPIO_ACTIVE_HIGH>;
			dir   = "out";
		};
		gpio_p260 {
			label = "GPIO_P260";
			gpios = <&EDM_EXT_GPIO_P260 GPIO_ACTIVE_HIGH>;
			dir   = "out";
		};
		gpio_p261 {
			label = "GPIO_P261";
			gpios = <&EDM_EXT_GPIO_P261 GPIO_ACTIVE_HIGH>;
			dir   = "out";
		};
		gpio_p262 {
			label = "GPIO_P262";
			gpios = <&EDM_EXT_GPIO_P262 GPIO_ACTIVE_HIGH>;
			dir   = "out";
		};
		gpio_p263 {
			label = "GPIO_P263";
			gpios = <&EDM_EXT_GPIO_P263 GPIO_ACTIVE_HIGH>;
			dir   = "out";
		};
		gpio_p264 {
			label = "GPIO_P264";
			gpios = <&EDM_EXT_GPIO_P264 GPIO_ACTIVE_HIGH>;
			dir   = "out";
		};
	};

	sound {
		compatible = "fsl,imx-sgtl5000",
			     "fsl,imx-audio-sgtl5000";
		model = "audio-sgtl5000";
		ssi-controller = <&EDM_AUDA_I2S_CHANNEL>;
		audio-codec = <&codec>;
		audio-routing =
			"MIC_IN", "Mic Jack",
			"Mic Jack", "Mic Bias",
			"Headphone Jack", "HP_OUT";
#if defined(EDM_AUDA_MUX_INT) && defined(EDM_AUDA_MUX_EXT)
		mux-int-port = <EDM_AUDA_MUX_INT>;
		mux-ext-port = <EDM_AUDA_MUX_EXT>;
#else
		fsl,no-audmux = "true";
#endif
	};
};

&EDM_I2CA {
	status = "okay";
};

&EDM_I2CB {
	status = "okay";

	codec: sgtl5000@0a {
		compatible = "fsl,sgtl5000";
		reg = <0x0a>;
		clocks = <&EDM_AUDA_CLK_SRC>;
		VDDA-supply = <&reg_2p5v>;
		VDDIO-supply = <&reg_3p3v>;
	};
};

&EDM_I2CC {
	status = "okay";

	ds1337: rtc@68 {
		compatible = "mxim,ds1337";
		reg = <0x68>;
	};
};

&can1 {
	status = "okay";
};

&can2 {
	status = "okay";
};

&EDM_SPIB {
	status = "okay";

	ads7846@0 {
		reg = <0>;  /* CS0 */
		compatible = "ti,ads7846";
		interrupt-parent = <&GPIO_BANK(EDM_EXT_GPIO_P263)>;
		interrupts = <GPIO_NO(EDM_EXT_GPIO_P263) IRQ_TYPE_EDGE_FALLING>;
		spi-max-frequency = <1000000>;
		pendown-gpio = <&EDM_EXT_GPIO_P263 GPIO_ACTIVE_LOW>;
		vcc-supply = <&reg_3p3v>;

		ti,x-min = /bits/ 16 <0>;
		ti,x-max = /bits/ 16 <4095>;
		ti,y-min = /bits/ 16 <0>;
		ti,y-max = /bits/ 16 <4095>;
		ti,pressure-max = /bits/ 16 <1024>;
		ti,x-plate-ohms = /bits/ 16 <90>;
		ti,y-plate-ohms = /bits/ 16 <90>;
		ti,debounce-max = /bits/ 16 <70>;
		ti,debounce-tol = /bits/ 16 <3>;
		ti,debounce-rep = /bits/ 16 <2>;
		ti,settle-delay-usec = /bits/ 16 <150>;

		linux,wakeup;
	};
};
