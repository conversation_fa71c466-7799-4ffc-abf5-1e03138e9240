// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright 2024 NXP
 */

/dts-v1/;

#include <dt-bindings/usb/pd.h>
#include <dt-bindings/pwm/pwm.h>
#include "imx91-edm.dtsi"

&ele_fw2 {
	memory-region = <&ele_reserved>;
};

/ {
	model = "TechNexion i.MX91 EDM board";
	compatible = "fsl,imx91-edm", "fsl,imx91";

	chosen {
		stdout-path = &lpuart1;
	};

	reg_usb_otg_vbus: usb_otg_vbus {
		compatible = "regulator-fixed";
		regulator-name = "usb_otg_vbus";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&tca9554pwr 3 GPIO_ACTIVE_LOW>;
		enable-active-low;
	};

	usb_hub_rst: usb_hub_rst {
		compatible = "gpio-reset";
		reset-gpios = <&tca9554pwr 5 GPIO_ACTIVE_LOW>;
		reset-delay-us = <10>;
		#reset-cells = <0>;
	};

	disp_backlight: disp_backlight {
		compatible = "pwm-backlight";
		pwms = <&tpm2 0 40000 PWM_POLARITY_INVERTED>;	/* 25KHz PWM */
		/* TPM driver seems revert the value */
		brightness-levels = <0 36 72 108 144 180 216 255>;
		default-brightness-level = <1>;
		power-supply = <&reg_5v>;
		status = "disabled";
	};

	tlv320_mclk: clk-0 {
		#clock-cells = <0>;
		compatible = "fixed-clock";
		clock-frequency = <********>;
	};

	sound {
		compatible = "simple-audio-card";
		simple-audio-card,name = "tlv320aic3x-audio";
		simple-audio-card,widgets =
			"Headphone", "Headphone Jack",
			"Microphone", "Mic Jack";
		simple-audio-card,routing =
			"Headphone Jack", "HPLOUT",
			"Headphone Jack", "HPROUT",
			"MIC2R", "Mic Jack",
			"Mic Jack", "Mic Bias";
		simple-audio-card,format = "dsp_b";
		simple-audio-card,bitclock-master = <&sound_master>;
		simple-audio-card,frame-master = <&sound_master>;
		simple-audio-card,bitclock-inversion;
		status = "okay";

		simple-audio-card,cpu {
			sound-dai = <&sai1>;
		};

		sound_master: simple-audio-card,codec {
			sound-dai = <&tlv320aic3104>;
			clocks = <&tlv320_mclk>;
		};
	};

	leds {
		compatible = "gpio-leds";

		led_1 {
			label = "WB-led";
			gpios = <&pca9555_bs23 1 GPIO_ACTIVE_HIGH>;
			default-state = "on";
		};
	};
};

&fec {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&pinctrl_fec>;
	pinctrl-1 = <&pinctrl_fec_sleep>;
	phy-mode = "rgmii-id";
	phy-handle = <&ethphy2>;
	fsl,magic-packet;
	status = "okay";

	mdio {
		#address-cells = <1>;
		#size-cells = <0>;
		clock-frequency = <5000000>;

		ethphy2: ethernet-phy@2 {
			/* Atheros AR8035 PHY */
			compatible = "ethernet-phy-ieee802.3-c22";
			reg = <2>;
			pinctrl-names = "default";
			pinctrl-0 = <&pinctrl_fec_rst>;
			reset-gpios = <&gpio1 10 GPIO_ACTIVE_LOW>;
			reset-assert-us = <35000>;
			reset-deassert-us = <75000>;
			eee-broken-1000t;
			at803x,eee-disabled;
			at803x,vddio-1p8v;
			realtek,aldps-disable;
			realtek,clkout-disable;
		};
	};
};

&sai1 {
	status = "okay";
};

&sai3 {
	status = "okay";
};

&flexcan1 {
	status = "okay";
};

&lpi2c2 {
	status = "okay";

	pca9555_bs23: pca9555@23 {
		compatible = "nxp,pca9555";
		reg = <0x23>;
		pinctrl-names = "default";
		pinctrl-0 = <&pinctrl_pca9555_bs23>;
		vcc-supply = <&reg_3p3v>;
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-controller;
		#interrupt-cells = <2>;
		interrupt-parent = <&gpio1>;
		interrupts = <1 IRQ_TYPE_EDGE_FALLING>;
		gpio-line-names = "M2_DISABLE_N", "LED_EN","USB_OTG_OC", "M2_WAN_WAKE",
			"M2_RST_N", "CSI1_PDB","", "PD_FAULT",
			"AUD_A_RST", "CAM_EXT_INT","VH_15_EXT_IO1_2", "VH_P22_EXT_IO1_3","VH_P32_EXT_IO1_4", "VH_P36_EXT_IO1_5","", "";
	};

	typec_hd3ss3220: hd3ss3220@67 {
		compatible = "ti,hd3ss3220";
		interrupts-extended = <&tca9554pwr 4 IRQ_TYPE_EDGE_FALLING>;
		vbus-supply = <&reg_usb_otg_vbus>;
		reg = <0x67>;

		ports {
			#address-cells = <1>;
			#size-cells = <0>;

			port@0 {
				reg = <0>;
				hd3ss3220_in_ep: endpoint {
					remote-endpoint = <&otg1_out_ep>;
				};
			};

			port@1 {
				reg = <1>;
				hd3ss3220_out_ep: endpoint {
					remote-endpoint = <&otg1_in_ep>;
				};
			};
		};
	};

	tlv320aic3104: audio-codec@18 {
		#sound-dai-cells = <0>;
		compatible = "ti,tlv320aic3104";
		reg = <0x18>;
		status = "okay";
		reset-gpios = <&pca9555_bs23 8 GPIO_ACTIVE_LOW>;
		ai3x-ocmv = <0>;
		ai3x-micbias-vg = <1>;		/* 2.0V */

		/* Regulators */
		AVDD-supply = <&reg_3p3v>;
		IOVDD-supply = <&reg_3p3v>;
		DRVDD-supply = <&reg_3p3v>;
		DVDD-supply = <&reg_1p8v>;
	};
};

&lpi2c3 {
	status = "okay";
};

&media_blk_ctrl {
	status = "okay";
};

&usbotg1 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			otg1_out_ep: endpoint {
				remote-endpoint = <&hd3ss3220_in_ep>;
			};
		};

		port@1 {
			reg = <1>;
			otg1_in_ep: endpoint {
				remote-endpoint = <&hd3ss3220_out_ep>;
			};
		};
	};
};

&usbotg2 {
	status = "okay";
};

&gpio1 {
	status = "okay";

	gpio-line-names = "", "PCA9554BS_irq", "", "",
			"", "", "", "",

			"", "", "FEC_PHY_RST", "",
			"", "", "", "",

			"", "", "", "",
			"", "", "", "";
};

&gpio2 {
	status = "okay";

	gpio-line-names = "", "", "", "",
			"", "", "", "",

			"", "", "", "",
			"", "", "", "",

			"", "", "", "",
			"", "", "", "";
};

&gpio3 {
	status = "okay";

	gpio-line-names = "SD_CD", "", "", "",
			"", "", "", "SD_VMMC",

			"", "", "", "",
			"", "", "", "",

			"", "", "", "",
			"", "", "", "",

			"", "", "TOUCH_INTn", "tca9554pwr_IRQn",
			"", "", "", "";
};

&gpio4 {
	status = "okay";

	gpio-line-names = "", "", "", "",
			"", "", "", "",

			"", "", "", "",
			"", "", "", "",

			"", "", "", "",
			"", "", "", "",

			"", "", "", "",
			"EQOS_PHY_RST", "", "", "";
};

&iomuxc {
	pinctrl_pca9555_bs23: pca9555_bs23grp {
		fsl,pins = <
			MX91_PAD_I2C1_SDA__GPIO1_IO1		0x31e
		>;
	};

	pinctrl_fec: fecgrp {
		fsl,pins = <
			MX91_PAD_ENET2_MDC__ENET2_MDC			0x57e
			MX91_PAD_ENET2_MDIO__ENET2_MDIO			0x57e
			MX91_PAD_ENET2_RD0__ENET2_RGMII_RD0		0x57e
			MX91_PAD_ENET2_RD1__ENET2_RGMII_RD1		0x57e
			MX91_PAD_ENET2_RD2__ENET2_RGMII_RD2		0x57e
			MX91_PAD_ENET2_RD3__ENET2_RGMII_RD3		0x57e
			MX91_PAD_ENET2_RXC__ENET2_RGMII_RXC		0x5fe
			MX91_PAD_ENET2_RX_CTL__ENET2_RGMII_RX_CTL	0x57e
			MX91_PAD_ENET2_TD0__ENET2_RGMII_TD0		0x57e
			MX91_PAD_ENET2_TD1__ENET2_RGMII_TD1		0x57e
			MX91_PAD_ENET2_TD2__ENET2_RGMII_TD2		0x57e
			MX91_PAD_ENET2_TD3__ENET2_RGMII_TD3		0x57e
			MX91_PAD_ENET2_TXC__ENET2_RGMII_TXC		0x5fe
			MX91_PAD_ENET2_TX_CTL__ENET2_RGMII_TX_CTL	0x57e
		>;
	};

	pinctrl_fec_sleep: fecsleepgrp {
		fsl,pins = <
			MX91_PAD_ENET2_MDC__GPIO4_IO14			0x51e
			MX91_PAD_ENET2_MDIO__GPIO4_IO15			0x51e
			MX91_PAD_ENET2_RD0__GPIO4_IO24			0x51e
			MX91_PAD_ENET2_RD1__GPIO4_IO25			0x51e
			MX91_PAD_ENET2_RD2__GPIO4_IO26			0x51e
			MX91_PAD_ENET2_RD3__GPIO4_IO27			0x51e
			MX91_PAD_ENET2_RXC__GPIO4_IO23                  0x51e
			MX91_PAD_ENET2_RX_CTL__GPIO4_IO22		0x51e
			MX91_PAD_ENET2_TD0__GPIO4_IO19			0x51e
			MX91_PAD_ENET2_TD1__GPIO4_IO18			0x51e
			MX91_PAD_ENET2_TD2__GPIO4_IO17			0x51e
			MX91_PAD_ENET2_TD3__GPIO4_IO16			0x51e
			MX91_PAD_ENET2_TXC__GPIO4_IO21                  0x51e
			MX91_PAD_ENET2_TX_CTL__GPIO4_IO20               0x51e
		>;
	};

	pinctrl_fec_rst: fecrstgrp {
		fsl,pins = <
			MX91_PAD_PDM_BIT_STREAM1__GPIO1_IO10		0x31e
		>;
	};
};
