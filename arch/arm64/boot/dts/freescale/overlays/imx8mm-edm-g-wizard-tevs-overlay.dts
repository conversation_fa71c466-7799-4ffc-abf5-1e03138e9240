// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright 2024 TechNexion Ltd.
 *
 * Author: <PERSON><PERSON> <<EMAIL>>
 *
 */

/dts-v1/;
/plugin/;

#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/clock/imx8mm-clock.h>
#include <dt-bindings/gpio/gpio.h>

/ {
	fragment@0 {
		target = <&mipi_csi>;
		__overlay__ {
			status = "okay";

			ports {
				port@0 {
					imx8mm_mipi_csi_in: endpoint {
						remote-endpoint = <&tevs_1_ep>;
						data-lanes = <1 2>;
					};
				};
				port@1 {
					imx8mm_mipi_csi_out: endpoint {
						remote-endpoint = <&csi_in>;
					};
				};
			};
		};
	};

	fragment@1 {
		target = <&csi>;
		__overlay__ {
			status = "okay";
		};
	};

	fragment@2 {
		target = <&i2c2>;
		__overlay__ {
			#address-cells = <1>;
			#size-cells = <0>;
			status = "okay";

			tevs_mipi: tevs_mipi@48 {
				compatible = "tn,tevs";
				reg = <0x48>;
				status = "okay";

				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_csi_rst>;
				host-power-gpios = <&pca9555_a21 10 GPIO_ACTIVE_LOW>;
				reset-gpios = <&gpio1 5 GPIO_ACTIVE_HIGH>;
				standby-gpios = <&pca9555_a21 2 GPIO_ACTIVE_HIGH>;

				supports-over-4k-res;

				port {
					tevs_1_ep: endpoint {
						remote-endpoint = <&imx8mm_mipi_csi_in>;
						data-lanes = <1 2>;
						clock-lanes = <0>;
						clock-noncontinuous;
						link-frequencies = /bits/ 64 <400000000>;
					};
				};
			};
		};
	};
};
