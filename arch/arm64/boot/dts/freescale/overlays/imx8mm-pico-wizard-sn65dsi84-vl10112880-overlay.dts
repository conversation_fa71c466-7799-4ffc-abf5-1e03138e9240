// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright 2020 TechNexion Ltd.
 *
 * Author: <PERSON> <<EMAIL>>
 *
 */

/dts-v1/;
/plugin/;

#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/gpio/gpio.h>

/ {
	fragment@0 {
		target-path = "/";
		__overlay__ {
			backlight_mipi {
				status = "okay";
			};

			regulators {
				reg_backlight_pwr: regulator_blpwr { /* MIPI_VDDEN */
					regulator-always-on;
					status = "okay";
				};

				reg_mipi_bl_en: regulator_mipiblen {
					regulator-always-on;
					status = "okay";
				};
			};
		};
	};

	fragment@1 {
		target = <&mipi_dsi>;
		__overlay__ {
			status = "okay";
			#address-cells = <1>;
			#size-cells = <0>;

			panel@0 {
				reg = <0>;
				compatible = "tn,dsi2lvds-panel";
				power-supply = <&reg_backlight_pwr>;

				dsi,flags = <0x0003>;
				dsi,format = <0>;
				dsi,lanes = <4>;
				panel-width-mm  = <217>;
				panel-height-mm = <135>;
				bus-format = <0x100a>;	/* MEDIA_BUS_FMT_RGB888_1X24 */
				bus-flags = <1>;
				refresh-rate = <60>;
				rotate = <0>;
				/* horz-flip; */
				/* vert-flip; */

				panel-timing {
					clock-frequency = <68900000>;
					hactive = <1280>;
					vactive = <800>;
					hfront-porch = <40>;
					hsync-len = <80>;
					hback-porch = <40>;
					vfront-porch = <3>;
					vsync-len = <10>;
					vback-porch = <10>;
					/* flags = DISPLAY_FLAGS_HSYNC_HIGH | DISPLAY_FLAGS_VSYNC_HIGH */
					vsync-active = <1>;
					hsync-active = <1>;
				};
			};
		};
	};

	fragment@2 {
		target = <&i2c2>;
		__overlay__ {
			#address-cells = <1>;
			#size-cells = <0>;
			status = "okay";

			dsi_lvds_bridge: sn65dsi84@2d {
				reg = <0x2d>;
				status = "okay";
				compatible = "ti,sn65dsi84";
				#gpio-cells = <2>;
				enable-gpios = <&pca9555_23 2 GPIO_ACTIVE_HIGH>; /* MIPI_RST */
				sn65dsi84,addresses = <0x09 0x0A 0x0B 0x0D
									0x10 0x11 0x12 0x13
									0x18 0x19 0x1A 0x1B
									0x20 0x21 0x22 0x23
									0x24 0x25 0x26 0x27
									0x28 0x29 0x2A 0x2B
									0x2C 0x2D 0x2E 0x2F
									0x30 0x31 0x32 0x33
									0x34 0x35 0x36 0x37
									0x38 0x39 0x3A 0x3B
									0x3C 0x3D 0x3E 0x0D>;

				sn65dsi84,values = </* reset and clock registers */
									0x00 0x05 0x10 0x00
									/* DSI registers */
									0x26 0x00 0x2a 0x00
									/* LVDS registers */
									0x78 0x00 0x03 0x00

									/* video registers */
									/* cha-al-len-l, cha-al-len-h, chb-al-len-l, chb-al-len-h */
									0x00 0x05 0x00 0x00
									/* cha-v-ds-l, cha-v-ds-h, chb-v-ds-l, chb-v-ds-h */
									0x00 0x00 0x00 0x00
									/* cha-sdl, cha-sdh, chb-sdl, chb-sdh */
									0x21 0x00 0x00 0x00
									/* cha-hs-pwl, cha-hs-pwh, chb-hs-pwl, chb-hs-pwh */
									0x50 0x00 0x00 0x00
									/* cha-vs-pwl, cha-vs-pwh, chb-vs-pwl, chb-vs-pwh */
									0x0a 0x00 0x00 0x00
									/*cha-hbp, chb-hbp, cha-vbp, chb-vbp*/
									0x28 0x00 0x00 0x00
									/* cha-hfp, chb-hfp, cha-vfp, chb-vfp*/
									0x00 0x00 0x00 0x00

									0x00 0x00 0x00 0x01>;
			};
		};
	};

	fragment@3 {
		target = <&i2c3>;
		__overlay__ {
			#address-cells = <1>;
			#size-cells = <0>;
			status = "okay";

			exc80w46: touchscreen@2a {
				compatible = "eeti,exc80h60";
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_touch_irq>;
				reset-gpios = <&pca9555_23 3 GPIO_ACTIVE_LOW>;
				reg = <0x2a>;
				interrupt-parent = <&gpio1>;
				interrupts = <7 IRQ_TYPE_LEVEL_LOW>;
				status = "okay";
			};
		};
	};
};
