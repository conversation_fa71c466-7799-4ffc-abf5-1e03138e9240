// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright 2024 TechNexion Ltd.
 *
 * Author: <PERSON><PERSON> <<EMAIL>>
 *
 */

/dts-v1/;
/plugin/;

#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/clock/imx8mm-clock.h>
#include <dt-bindings/gpio/gpio.h>

/ {
	fragment@0 {
		target = <&mipi_csi>;
		__overlay__ {
			status = "okay";

			ports {
				port@0 {
					imx8mm_mipi_csi_in: endpoint {
						remote-endpoint = <&tevs_1_ep>;
						data-lanes = <1 2>;
					};
				};
				port@1 {
					imx8mm_mipi_csi_out: endpoint {
						remote-endpoint = <&csi_in>;
					};
				};
			};
		};
	};

	fragment@1 {
		target = <&csi>;
		__overlay__ {
			status = "okay";
		};
	};

	fragment@2 {
		target = <&i2c2>;
		__overlay__ {
			#address-cells = <1>;
			#size-cells = <0>;

			vizionlink: vizionlink@30 {
				compatible = "tn,vizionlink";
				reg = <0x30>;
				pdb-gpios = <&pca9555_a23 13 GPIO_ACTIVE_HIGH>;
				ser_alias_id = <0x1b>;

				//i2c addr alias map "gpio extender, eeprom , sensor"
				i2c_addr_alias_map_local = <0x25 0x48>;
				i2c_addr_alias_map_remote = <0x25 0x48>;

				//deserializer output csi lanes 1~4. default 4
				des_csi_lanes = <2>;

				//1: Enable 0: Disable continuous clock. default 0
				des_csi_continuous_clock = <0>;

				//serializer input csi lanes 1~4. default 4
				ser_csi_lanes = <2>;

				//1: Enable 0: Disable continuous clock. default 0
				ser_csi_continuous_clock = <0>;

				status = "okay";

				i2c_dev_list {
					#address-cells = <1>;
					#size-cells = <0>;

					vizionlink_pca9554_a25: vizionlink_pca9554@25 {
						compatible = "nxp,pca9554";
						reg = <0x25>;
						gpio-controller;
						#gpio-cells = <2>;
						status = "okay";
					};
				};
			};

			tevs_mipi: tevs_mipi@48 {
				compatible = "tn,tevs";
				reg = <0x48>;
				status = "okay";

				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_csi_rst>;
				host-power-gpios = <&pca9555_a21 10 GPIO_ACTIVE_LOW>;
				reset-gpios = <&vizionlink_pca9554_a25 4 GPIO_ACTIVE_HIGH>;
				standby-gpios = <&vizionlink_pca9554_a25 2 GPIO_ACTIVE_HIGH>;

				supports-over-4k-res;

				port {
					tevs_1_ep: endpoint {
						remote-endpoint = <&imx8mm_mipi_csi_in>;
						data-lanes = <1 2>;
						clock-lanes = <0>;
						clock-noncontinuous;
						link-frequencies = /bits/ 64 <400000000>;
					};
				};
			};
		};
	};
};
