// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright 2023 TechNexion Ltd.
 *
 * Author: <PERSON><PERSON> <<EMAIL>>
 *
 */

/dts-v1/;
/plugin/;

#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/clock/imx8mm-clock.h>
#include <dt-bindings/gpio/gpio.h>

/ {
	fragment@0 {
		target = <&mipi_csi>;
		__overlay__ {
			status = "okay";

			ports {
				port@0 {
					imx8mm_mipi_csi_in: endpoint {
						remote-endpoint = <&tevs_0_ep>;
						data-lanes = <1 2>;
					};
				};
				port@1 {
					imx8mm_mipi_csi_out: endpoint {
						remote-endpoint = <&csi_in>;
					};
				};
			};
		};
	};

	fragment@1 {
		target = <&csi>;
		__overlay__ {
			status = "okay";
		};
	};

	fragment@2 {
		target = <&i2c2>;
		__overlay__ {
			#address-cells = <1>;
			#size-cells = <0>;
			status = "okay";

			pca9555_a21: pca9555@21 {
				compatible = "nxp,pca9555";
				reg = <0x21>;
				status = "okay";
				gpio-controller;
				#gpio-cells = <2>;
				gpio-line-names = "EXPOSURE_TRIG_IN1", "FLASH_OUT1", "INFO_TRIG_IN1", "CAM_SHUTTER1", "XVS1", "PWR1_TIME0", "PWR1_TIME1", "PWR1_TIME2",
									"EXPOSURE_TRIG_IN2", "FLASH_OUT2", "INFO_TRIG_IN2", "CAM_SHUTTER2", "XVS2", "PWR2_TIME0", "PWR2_TIME1", "PWR2_TIME2";
			};

			tevs_0: tevs@48 {
				compatible = "tn,tevs";
				reg = <0x48>;
				status = "okay";

				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_csi_rst>;
				host-power-gpios = <&pca9555_a21 10 GPIO_ACTIVE_LOW>;
				reset-gpios = <&gpio1 5 GPIO_ACTIVE_HIGH>;
				device-power-gpios = <&pca9555_a21 0 GPIO_ACTIVE_HIGH>;
				standby-gpios = <&pca9555_a21 2 GPIO_ACTIVE_HIGH>;

				supports-over-4k-res;

				port {
					tevs_0_ep: endpoint {
						remote-endpoint = <&imx8mm_mipi_csi_in>;
						data-lanes = <1 2>;
						clock-lanes = <0>;
						clock-noncontinuous;
						link-frequencies = /bits/ 64 <400000000>;
					};
				};
			};
		};
	};
};
