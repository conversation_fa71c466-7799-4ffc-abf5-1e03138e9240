// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright 2024 TechNexion Ltd.
 *
 * Author: <PERSON><PERSON> <<EMAIL>>
 *
 */

/dts-v1/;
/plugin/;

#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/gpio/gpio.h>

/ {
	fragment@101 {
		target-path = "/";

		__overlay__ {
			clk_ov5640_fixed: ov5640-xclk {
				compatible = "fixed-clock";
				#clock-cells = <0>;
				clock-frequency = <24000000>;
			};
		};
	};
};

&isi {
	#address-cells = <1>;
	#size-cells = <0>;
	status = "okay";

	port {
		isi_in: endpoint {
			remote-endpoint = <&mipi_csi_out>;
		};
	};
};

&mipi_csi {
	#address-cells = <1>;
	#size-cells = <0>;
	status = "okay";

	port@0 {
		reg = <0>;
		mipi_csi_ep: endpoint {
			remote-endpoint = <&ov5640_out>;
			data-lanes = <1 2>;
			clock-lanes = <0>;
		};
	};

	port@1 {
		reg = <1>;
		mipi_csi_out: endpoint {
			remote-endpoint = <&isi_in>;
		};
	};
};

&dphy_rx {
	#address-cells = <1>;
	#size-cells = <0>;
	status = "okay";
};

&lpi2c2 {
	#address-cells = <1>;
	#size-cells = <0>;
	status = "okay";

	ov5640_mipi: ov5640_mipi@3c {
		compatible = "ovti,ov5640";
		reg = <0x3c>;

		clocks = <&clk_ov5640_fixed>;
		clock-names = "xclk";

		reset-gpios = <&pcal6524 12 GPIO_ACTIVE_LOW>;
		powerdown-gpios = <&tca9555_a21 2 GPIO_ACTIVE_HIGH>;

		status = "okay";

		port {

			ov5640_out: endpoint {
				remote-endpoint = <&mipi_csi_ep>;
				data-lanes = <1 2>;
				clock-lanes = <0>;
			};
		};
	};
};
