// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright 2020 TechNexion Ltd.
 *
 * Author: <PERSON> <<EMAIL>>
 *
 */

/dts-v1/;
/plugin/;

#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/gpio/gpio.h>

/ {
	fragment@0 {
		target-path = "/";
		__overlay__ {
			reg_mipi_pwr: regulator_mipipwr {
				regulator-always-on;
				status = "okay";
			};

			reg_mipi_vdden: regulator_mipivdden {
				regulator-always-on;
				status = "okay";
			};

			mipi_backlight: mipi_backlight {
				status = "okay";
			};

			reg_mipi_bl_en: regulator_mipiblen {
				regulator-always-on;
				status = "okay";
			};
		};
	};

	fragment@1 {
		target = <&mipi_dsi>;
		__overlay__ {
			status = "okay";
			#address-cells = <1>;
			#size-cells = <0>;

			panel@0 {
				compatible = "tn,vizionpanel_15010276";
				backlight = <&mipi_backlight>;
				power-supply = <&reg_mipi_vdden>;
				reg = <0>;
				status = "okay";
			};
		};
	};

	fragment@2 {
		target = <&i2c2>;
		__overlay__ {
			#address-cells = <1>;
			#size-cells = <0>;
			status = "okay";

			fpdlink_dsi2lvds: ds90ub948@c {
				compatible = "ti,ds90ub94x";
				reg = <0x0c>;
				status = "okay";
				reset-gpios = <&pca9554 5 GPIO_ACTIVE_LOW>;
				vizionpanel-remote-gpio;
				dsi-delay = <2500>;
				i2c-alias-pool = <0x48 0x49 0x4a 0x4b 0x4c 0x4d 0x4e 0x4f>;

				i2c {
					#address-cells = <1>;
					#size-cells = <0>;

					exc80w46: touchscreen@2a {
						compatible = "eeti,exc80h60";
						pinctrl-names = "default";
						pinctrl-0 = <&pinctrl_ft5336_touch_irq>;
						reg = <0x2a>;
						interrupt-parent = <&gpio4>;
						interrupts = <15 IRQ_TYPE_LEVEL_LOW>;
						reset-gpios = <&pca9554 1 GPIO_ACTIVE_LOW>;
						status = "okay";
					};

					eeprom_baseboard: eeprom@56 {
						compatible = "atmel,24c02";
						reg = <0x56>;
						pagesize = <16>;
						status = "okay";
					};
				};
			};
		};
	};
};
