// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright 2024 TechNexion Ltd.
 *
 * Author: <PERSON><PERSON> <<EMAIL>>
 *
 */

/dts-v1/;
/plugin/;

#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/clock/imx8mp-clock.h>
#include <dt-bindings/gpio/gpio.h>

/ {
	fragment@0 {
		target = <&mipi_csi_0>;
		__overlay__ {
			#address-cells = <1>;
			#size-cells = <0>;
			clock-frequency = <400000000>;
			status = "okay";

			port@0 {
				reg = <0>;
				mipi_csi0_ep: endpoint {
					remote-endpoint = <&tevs_0_ep>;
					data-lanes = <4>;
					csis-hs-settle = <19>;
					csis-clk-settle = <2>;
					csis-wclk;
				};
			};
		};
	};

	fragment@1 {
		target = <&mipi_csi_1>;
		__overlay__ {
			#address-cells = <1>;
			#size-cells = <0>;
			clock-frequency = <400000000>;
			status = "okay";

			port@1 {
				reg = <1>;
				mipi_csi1_ep: endpoint {
					remote-endpoint = <&tevs_1_ep>;
					data-lanes = <4>;
					csis-hs-settle = <19>;
					csis-clk-settle = <2>;
					csis-wclk;
				};
			};
		};
	};

	fragment@2 {
		target = <&i2c2>;
		__overlay__ {
			#address-cells = <1>;
			#size-cells = <0>;

			vizionlink_1: vizionlink@30 {
				compatible = "tn,vizionlink";
				reg = <0x30>;
				pinctrl-0 = <&pinctrl_csi2_pdb>;
				pdb-gpios = <&gpio4 3 GPIO_ACTIVE_HIGH>;
				ser_alias_id = <0x1b>;

				//i2c addr alias map "gpio extender, eeprom , sensor"
				i2c_addr_alias_map_local = <0x25 0x3e>;
				i2c_addr_alias_map_remote = <0x25 0x48>;

				//deserializer output csi lanes 1~4. default 4
				des_csi_lanes = <4>;

				//1: Enable 0: Disable continuous clock. default 0
				des_csi_continuous_clock = <0>;

				//serializer input csi lanes 1~4. default 4
				ser_csi_lanes = <4>;

				//1: Enable 0: Disable continuous clock. default 0
				ser_csi_continuous_clock = <0>;

				status = "okay";

				i2c_dev_list {
					#address-cells = <1>;
					#size-cells = <0>;

					vizionlink_tca9554_a25_1: vizionlink_tca9554@25 {
						compatible = "nxp,pca9554";
						reg = <0x25>;
						gpio-controller;
						#gpio-cells = <2>;
						status = "okay";
					};
				};
			};

			tevs_1: tevs@3e {
				compatible = "tn,tevs";
				reg = <0x3e>;
				status = "okay";

				reset-gpios = <&vizionlink_tca9554_a25_1 4 GPIO_ACTIVE_HIGH>;
				standby-gpios = <&vizionlink_tca9554_a25_1 2 GPIO_ACTIVE_HIGH>;

				port {
					tevs_1_ep: endpoint {
						remote-endpoint = <&mipi_csi1_ep>;
						data-lanes = <1 2 3 4>;
						clock-lanes = <0>;
						clock-noncontinuous;
						link-frequencies = /bits/ 64 <400000000>;
					};
				};
			};
		};
	};

	fragment@3 {
		target = <&i2c5>;
		__overlay__ {
			#address-cells = <1>;
			#size-cells = <0>;

			vizionlink@30 {
				compatible = "tn,vizionlink";
				reg = <0x30>;
				pinctrl-names = "default";
				pdb-gpios = <&pca9555_a26 6 GPIO_ACTIVE_HIGH>;
				ser_alias_id = <0x1b>;

				//i2c addr alias map "gpio extender, eeprom , sensor"
				i2c_addr_alias_map_local = <0x25 0x3e>;
				i2c_addr_alias_map_remote = <0x25 0x48>;

				//deserializer output csi lanes 1~4. default 4
				des_csi_lanes = <4>;

				//1: Enable 0: Disable continuous clock. default 0
				des_csi_continuous_clock = <0>;

				//serializer input csi lanes 1~4. default 4
				ser_csi_lanes = <4>;

				//1: Enable 0: Disable continuous clock. default 0
				ser_csi_continuous_clock = <0>;

				status = "okay";

				i2c_dev_list {
					#address-cells = <1>;
					#size-cells = <0>;

					vizionlink_tca9554_a25_0: vizionlink_tca9554@25 {
						compatible = "nxp,pca9554";
						reg = <0x25>;
						gpio-controller;
						#gpio-cells = <2>;
						status = "okay";
					};
				};
			};

			tevs_0: tevs@3e {
				compatible = "tn,tevs";
				reg = <0x3e>;
				status = "okay";

				reset-gpios = <&vizionlink_tca9554_a25_0 4 GPIO_ACTIVE_HIGH>;
				standby-gpios = <&vizionlink_tca9554_a25_0 2 GPIO_ACTIVE_HIGH>;

				port {
					tevs_0_ep: endpoint {
						remote-endpoint = <&mipi_csi0_ep>;
						data-lanes = <1 2 3 4>;
						clock-lanes = <0>;
						clock-noncontinuous;
						link-frequencies = /bits/ 64 <400000000>;
					};
				};
			};
		};
	};
};
