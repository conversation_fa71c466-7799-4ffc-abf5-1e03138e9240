// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright 2024 TechNexion Ltd.
 *
 * Author: <PERSON><PERSON> <<EMAIL>>
 *
 */

/dts-v1/;
/plugin/;

#include "../imx8mq-pinfunc.h"
#include <dt-bindings/clock/imx8mq-clock.h>
#include <dt-bindings/interrupt-controller/irq.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/gpio/gpio.h>

/ {

	fragment@0 {
		target = <&csi1_bridge>;
		__overlay__ {
			fsl,two-8bit-sensor-mode;
			status = "okay";
		};
	};

	fragment@1 {
		target = <&csi2_bridge>;
		__overlay__ {
			fsl,two-8bit-sensor-mode;
			status = "okay";
		};
	};

	fragment@2 {
		target = <&mipi_csi1>;
		__overlay__ {
			status = "okay";
			#address-cells = <1>;
			#size-cells = <0>;

			port {
				mipi1_sensor_ep: endpoint@0 {
					remote-endpoint = <&tevs_mipi1_ep>;
					data-lanes = <1 2 3 4>;
					bus-type = <4>;
					csis-wclk;
				};
			};
		};
	};

	fragment@3 {
		target = <&mipi_csi2>;
		__overlay__ {
			status = "okay";
			#address-cells = <1>;
			#size-cells = <0>;

			port {
				mipi2_sensor_ep: endpoint@0 {
					remote-endpoint = <&tevs_mipi2_ep>;
					data-lanes = <1 2 3 4>;
					bus-type = <4>;
					csis-wclk;
				};
			};
		};
	};

	fragment@4 {
		target = <&i2c2>;
		__overlay__ {
			#address-cells = <1>;
			#size-cells = <0>;
			status = "okay";

			pca9555_i2c2_a21: pca9555@21 {
				compatible = "nxp,pca9555";
				reg = <0x21>;
				gpio-controller;
				#gpio-cells = <2>;
				status = "okay";
				gpio-line-names = "EXPOSURE_TRIG_IN1", "FLASH_OUT1", "INFO_TRIG_IN1", "CAM_SHUTTER1", "XVS1", "PWR1_TIME0", "PWR1_TIME1", "PWR1_TIME2",
									"EXPOSURE_TRIG_IN2", "FLASH_OUT2", "INFO_TRIG_IN2", "CAM_SHUTTER2", "XVS2", "PWR2_TIME0", "PWR2_TIME1", "PWR2_TIME2";
			};

			tevs_mipi1: tevs_mipi@48 {
				compatible = "tn,tevs";
				reg = <0x48>;
				status = "okay";

				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_csi1>;
				reset-gpios = <&gpio1 12 GPIO_ACTIVE_HIGH>;
				standby-gpios = <&pca9555_i2c2_a21 2 GPIO_ACTIVE_HIGH>;

				csi_id = <0>;
				supports-over-4k-res;

				port {
					tevs_mipi1_ep: endpoint {
						remote-endpoint = <&mipi1_sensor_ep>;
						data-lanes = <1 2 3 4>;
						clock-lanes = <0>;
						clock-noncontinuous;
						link-frequencies = /bits/ 64 <400000000>;
					};
				};
			};
		};
	};

	fragment@5 {
		target = <&i2c3>;
		__overlay__ {
			#address-cells = <1>;
			#size-cells = <0>;
			status = "okay";

			/* PCA9554 on camera tev_mipi board */
			pca9554_tev_mipi2: pca9554_tev_mipi@24 {
				compatible = "nxp,pca9554";
				reg =<0x24>;
				gpio-controller;
				#gpio-cells = <2>;
			};

			tevs_mipi2: tevs_mipi2@48 {
				compatible = "tn,tevs";
				reg = <0x48>;
				status = "okay";

				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_csi2>;
				reset-gpios = <&gpio3 16 GPIO_ACTIVE_HIGH>;
				standby-gpios = <&pca9554_tev_mipi2 2 GPIO_ACTIVE_HIGH>;

				csi_id = <1>;
				supports-over-4k-res;

				port {
					tevs_mipi2_ep: endpoint {
						remote-endpoint = <&mipi2_sensor_ep>;
						data-lanes = <1 2 3 4>;
						clock-lanes = <0>;
						clock-noncontinuous;
						link-frequencies = /bits/ 64 <400000000>;
					};
				};
			};
		};
	};
};
