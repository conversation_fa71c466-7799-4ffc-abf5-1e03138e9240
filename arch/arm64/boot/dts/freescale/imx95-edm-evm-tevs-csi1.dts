// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright 2024 Technexion Ltd.
 *
 * Author: <PERSON> <<EMAIL>>
 *
 */

/dts-v1/;

#include "imx95-edm-evm.dts"

/ {
	model = "TechNexion EDM-IMX95 and EVM baseboard with TechNexion TEVS camera on CSI1";
	compatible = "fsl,imx95-edm", "fsl,imx95";

	reg_lvds_pwr: regulator_lvdspwr {	/* LVDS0_VDDEN */
		status = "okay";
	};

	lvds0_backlight: lvds0_backlight {
		status = "okay";
	};

	lvds0_panel {
		compatible = "vxt,vl10112880", "panel-lvds";
		backlight = <&lvds0_backlight>;
		power-supply = <&reg_lvds_pwr>;
		data-mapping = "vesa-24";
		height-mm = <161>;
		width-mm = <243>;
		panel-timing {
			clock-frequency = <71100000>;
			hactive = <1280>;
			vactive = <800>;
			hback-porch = <40>;
			hfront-porch = <40>;
			vback-porch = <10>;
			vfront-porch = <3>;
			hsync-len = <80>;
			vsync-len = <10>;
			de-active = <1>;
		};
		port {
			panel_in: endpoint {
				remote-endpoint = <&lvds0_out>;
			};
		};
	};
};

&display_pixel_link {
	status = "okay";
};

&ldb {
	#address-cells = <1>;
	#size-cells = <0>;
	assigned-clocks = <&scmi_clk IMX95_CLK_LDBPLL_VCO>,
			  <&scmi_clk IMX95_CLK_LDBPLL>;
	assigned-clock-rates = <2986200000>, <497700000>;
	status = "okay";

	channel@0 {
		#address-cells = <1>;
		#size-cells = <0>;
		reg = <0>;
		status = "okay";

		port@1 {
			reg = <1>;

			lvds0_out: endpoint {
				remote-endpoint = <&panel_in>;
			};
		};
	};
};

&ldb0_phy {
	status = "okay";
};

&pixel_interleaver {
	#address-cells = <1>;
	#size-cells = <0>;
	status = "okay";

	channel@0 {
		reg = <0>;
		status = "okay";
	};
};

/* TEVS on MIPI CSI1 */
&lpi2c4 {
	#address-cells = <1>;
	#size-cells = <0>;
	status = "okay";

	tevs_1: tevs@48 {
		compatible = "tn,tevs";
		reg = <0x48>;
		status = "okay";

		host-pwdn-gpios = <&tca9555_a21 7 GPIO_ACTIVE_HIGH>;
		reset-gpios = <&tca9555_a21 6 GPIO_ACTIVE_HIGH>;
		standby-gpios = <&tca9554_d25 2 GPIO_ACTIVE_HIGH>;

		port {
			tevs_1_ep: endpoint {
				remote-endpoint = <&mipi_csi1_ep>;
				data-lanes = <1 2 3 4>;
				clock-lanes = <0>;
				clock-noncontinuous;
				link-frequencies = /bits/ 64 <400000000>;
			};
		};
	};
};

&display_stream_csr {
	status = "disabled";
};

&display_master_csr {
	status = "disabled";
};

&mipi_tx_phy_csr {
	status = "disabled";
};

&mipi_dsi_intf {
	status = "okay";
};

&combo_rx {
	status = "okay";
};

&mipi_csi1 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;
			mipi_csi1_ep: endpoint {
				remote-endpoint = <&tevs_1_ep>;
				data-lanes = <1 2 3 4>;
				clock-lanes = <0>;
			};
		};

		port@1 {
			reg = <1>;
			mipi_csi1_out: endpoint {
				remote-endpoint = <&formatter_1_in>;
			};
		};
	};
};

&csi_pixel_formatter_1 {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@0 {
			reg = <0>;

			formatter_1_in: endpoint {
				remote-endpoint = <&mipi_csi1_out>;
			};
		};

		port@1 {
			reg = <1>;

			formatter_1_out: endpoint {
				remote-endpoint = <&isi_in_3>;
			};
		};
	};
};

&isi {
	status = "okay";

	ports {
		#address-cells = <1>;
		#size-cells = <0>;

		port@3 {
			reg = <3>;

			isi_in_3: endpoint {
				remote-endpoint = <&formatter_1_out>;
			};
		};
	};
};
