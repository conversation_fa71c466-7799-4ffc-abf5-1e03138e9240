// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright 2024 NXP
 */

/dts-v1/;

#include <dt-bindings/usb/pd.h>
#include "imx91.dtsi"

&ele_fw2 {
	memory-region = <&ele_reserved>;
};

/ {
	reserved-memory {
		#address-cells = <2>;
		#size-cells = <2>;
		ranges;

		linux,cma {
			compatible = "shared-dma-pool";
			reusable;
			alloc-ranges = <0 0x80000000 0 0x40000000>;
			size = <0 0x10000000>;
			linux,cma-default;
		};

		ele_reserved: ele-reserved@a4120000 {
			compatible = "shared-dma-pool";
			reg = <0 0xa4120000 0 0x100000>;
			no-map;
		};
	};

	reg_vref_1v8: regulator-adc-vref {
		compatible = "regulator-fixed";
		regulator-name = "vref_1v8";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
	};

	reg_1p8v: regulator-1p8v {
		compatible = "regulator-fixed";
		regulator-name = "1P8V";
		regulator-min-microvolt = <1800000>;
		regulator-max-microvolt = <1800000>;
		regulator-always-on;
	};

	reg_3p3v: regulator-3p3v {
		compatible = "regulator-fixed";
		regulator-name = "3P3V";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		regulator-always-on;
	};

	reg_5v: regulator-5v {
		compatible = "regulator-fixed";
		regulator-name = "5V";
		regulator-min-microvolt = <5000000>;
		regulator-max-microvolt = <5000000>;
		regulator-always-on;
	};

	reg_usdhc2_vmmc: regulator-usdhc2 {
		compatible = "regulator-fixed";
		pinctrl-names = "default";
		pinctrl-0 = <&pinctrl_reg_usdhc2_vmmc>;
		regulator-name = "VSD_3V3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio3 7 GPIO_ACTIVE_HIGH>;
		off-on-delay-us = <12000>;
		enable-active-high;
	};

	wl_reg_on: wlreg_on {
		compatible = "regulator-fixed";
		regulator-name = "WL_REG_ON";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&pcal6524 1 GPIO_ACTIVE_HIGH>;
		off-on-delay = <20000>;
		startup-delay-us = <100>;
		enable-active-high;
	};

	bt_reg_on: btreg_on {
		compatible = "gpio-reset";
		reset-gpios = <&pcal6524 2 GPIO_ACTIVE_LOW>;
		reset-delay-us = <2000>;
		reset-post-delay-ms = <200>;
		#reset-cells = <0>;
	};
};

&sai1 {
	#sound-dai-cells = <0>;
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&pinctrl_sai1>;
	pinctrl-1 = <&pinctrl_sai1_sleep>;
	assigned-clocks = <&clk IMX91_CLK_SAI1>;
	assigned-clock-parents = <&clk IMX91_CLK_AUDIO_PLL>;
	assigned-clock-rates = <12288000>;
//	fsl,sai-mclk-direction-output;
	status = "disabled";
};

&adc1 {
	vref-supply = <&reg_vref_1v8>;
	status = "okay";
};

&eqos {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&pinctrl_eqos>;
	pinctrl-1 = <&pinctrl_eqos_sleep>;
	phy-mode = "rgmii-id";
	phy-handle = <&ethphy1>;
	status = "okay";

	mdio {
		compatible = "snps,dwmac-mdio";
		#address-cells = <1>;
		#size-cells = <0>;
		clock-frequency = <5000000>;

		ethphy1: ethernet-phy@1 {
			/* Atheros AR8035 PHY */
			compatible = "ethernet-phy-ieee802.3-c22";
			reg = <1>;
			reset-gpios = <&pcal6524 15 GPIO_ACTIVE_LOW>;
			reset-assert-us = <35000>;
			reset-deassert-us = <75000>;
			eee-broken-1000t;
			at803x,eee-disabled;
			at803x,vddio-1p8v;
			realtek,aldps-disable;
			realtek,clkout-disable;
		};
	};
};

&fec {
	pinctrl-names = "default", "sleep";
	pinctrl-0 = <&pinctrl_fec>;
	pinctrl-1 = <&pinctrl_fec_sleep>;
	phy-mode = "rgmii-id";
	phy-handle = <&ethphy2>;
	fsl,magic-packet;
	status = "okay";

	mdio {
		#address-cells = <1>;
		#size-cells = <0>;
		clock-frequency = <5000000>;

		ethphy2: ethernet-phy@2 {
			/* Atheros AR8035 PHY */
			compatible = "ethernet-phy-ieee802.3-c22";
			reg = <2>;
			reset-gpios = <&pcal6524 14 GPIO_ACTIVE_LOW>;
			reset-assert-us = <35000>;
			reset-deassert-us = <75000>;
			eee-broken-1000t;
			at803x,eee-disabled;
			at803x,vddio-1p8v;
			realtek,aldps-disable;
			realtek,clkout-disable;
		};
	};
};

&mu1 {
	status = "okay";
};

&mu2 {
	status = "okay";
};

&flexcan1 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_flexcan1>;
	status = "disabled";
};

&tpm2 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_tpm_pwm>;
	status = "okay";
};

/*
 * When add, delete or change any target device setting in &lpi2c1,
 * please synchronize the changes to the &i3c1 bus in imx91-11x11-evk-i3c.dts.
 */
&lpi2c1 {
	clock-frequency = <400000>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_lpi2c1>;
	status = "disabled";
};

&lpi2c2 {
	clock-frequency = <400000>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_lpi2c2>;
	status = "okay";

	pmic@32 {
		compatible = "nxp,pf9453";
		reg = <0x32>;
		interrupt-parent = <&pcal6524>;
		interrupts = <11 IRQ_TYPE_EDGE_FALLING>;

		regulators {
			buck1: BUCK1 {
				regulator-name = "BUCK1";
				regulator-min-microvolt = <600000>;
				regulator-max-microvolt = <3775000>;
				regulator-boot-on;
				regulator-always-on;
			};

			buck2: BUCK2 {
				regulator-name = "BUCK2";
				regulator-min-microvolt = <600000>;
				regulator-max-microvolt = <2187500>;
				regulator-boot-on;
				regulator-always-on;
				regulator-ramp-delay = <12500>;
			};

			buck3: BUCK3 {
				regulator-name = "BUCK3";
				regulator-min-microvolt = <600000>;
				regulator-max-microvolt = <3775000>;
				regulator-boot-on;
				regulator-always-on;
			};

			buck4: BUCK4{
				regulator-name = "BUCK4";
				regulator-min-microvolt = <600000>;
				regulator-max-microvolt = <3775000>;
				regulator-boot-on;
				regulator-always-on;
			};

			ldo1: LDO1 {
				regulator-name = "LDO1";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3300000>;
				regulator-boot-on;
				regulator-always-on;
			};

			ldo2: LDO2 {
				regulator-name = "LDO2";
				regulator-min-microvolt = <500000>;
				regulator-max-microvolt = <1950000>;
				regulator-boot-on;
				regulator-always-on;
			};

			ldo_snvs: LDO_SNVS {
				regulator-name = "LDO_SNVS";
				regulator-min-microvolt = <800000>;
				regulator-max-microvolt = <3000000>;
				regulator-boot-on;
				regulator-always-on;
			};
		};
	};

	pcal6524: gpio@22 {
		compatible = "nxp,pcal6524";
		pinctrl-names = "default";
		pinctrl-0 = <&pinctrl_pcal6524>;
		reg = <0x22>;
		gpio-controller;
		#gpio-cells = <2>;
		interrupt-controller;
		#interrupt-cells = <2>;
		interrupt-parent = <&gpio1>;
		interrupts = <10 IRQ_TYPE_LEVEL_LOW>;
		gpio-line-names = "X1_P39", "WL_REG_ON", "BT_REG_ON", "WL_WAKE_HOST",
				"BT_WAKE_HOST", "LCD_BKLEN", "X4_P7", "SDIOREON",

				"X2_P20", "X3_P67", "X2_P22", "PMIC_INTn",
				"X3_P65", "X2_P18", "ENET2_RSTn", "ENET1_RSTn",

				"X1_P66", "X1_P68", "X1_P70", "X1_P72",
				"X1_P74", "X1_P76", "X1_P78", "X1_P80";
	};
};

&lpi2c3 {
	clock-frequency = <400000>;
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_lpi2c3>;
	status = "okay";
};

&lpuart1 { /* console */
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_uart1>;
	status = "okay";
};

&lpuart2 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_uart2>;
	status = "okay";
};

/* BT */
&lpuart5 {
	pinctrl-names = "default";
	pinctrl-0 = <&pinctrl_uart5>;
	assigned-clocks = <&clk IMX91_CLK_LPUART5>;
	assigned-clock-parents = <&clk IMX91_CLK_24M>;
	fsl,uart-has-rtscts;
	resets = <&bt_reg_on>;
	status = "okay";
};

&lpm {
	soc-supply = <&buck2>;
	status = "okay";
};

&media_blk_ctrl {
	status = "okay";
};

&usbotg1 {
	dr_mode = "otg";
	hnp-disable;
	srp-disable;
	adp-disable;
	usb-role-switch;
	disable-over-current;
	samsung,picophy-pre-emp-curr-control = <3>;
	samsung,picophy-dc-vol-level-adjust = <7>;
};

&usbotg2 {
	dr_mode = "host";
};

&usdhc1 {
	pinctrl-names = "default", "state_100mhz", "state_200mhz";
	pinctrl-0 = <&pinctrl_usdhc1>;
	pinctrl-1 = <&pinctrl_usdhc1_100mhz>;
	pinctrl-2 = <&pinctrl_usdhc1_200mhz>;
	bus-width = <8>;
	fsl,tuning-step = <1>;
	non-removable;
	status = "okay";
};

&usdhc2 {
	pinctrl-names = "default", "state_100mhz", "state_200mhz", "sleep";
	pinctrl-0 = <&pinctrl_usdhc2>, <&pinctrl_usdhc2_gpio>;
	pinctrl-1 = <&pinctrl_usdhc2_100mhz>, <&pinctrl_usdhc2_gpio>;
	pinctrl-2 = <&pinctrl_usdhc2_200mhz>, <&pinctrl_usdhc2_gpio>;
	pinctrl-3 = <&pinctrl_usdhc2_sleep>, <&pinctrl_usdhc2_gpio_sleep>;
	cd-gpios = <&gpio3 00 GPIO_ACTIVE_LOW>;
	fsl,cd-gpio-wakeup-disable;
	vmmc-supply = <&reg_usdhc2_vmmc>;
	bus-width = <4>;
	fsl,tuning-step = <1>;
	status = "okay";
	no-sdio;
	no-mmc;
};

/* WIFI SDIO */
&usdhc3 {
	pinctrl-names = "default", "state_100mhz", "state_200mhz", "sleep";
	pinctrl-0 = <&pinctrl_usdhc3>;
	pinctrl-1 = <&pinctrl_usdhc3_100mhz>;
	pinctrl-2 = <&pinctrl_usdhc3_200mhz>;
	pinctrl-3 = <&pinctrl_usdhc3_sleep>;
	vmmc-supply = <&wl_reg_on>;
	bus-width = <4>;
	pm-ignore-notify;
	keep-power-in-suspend;
	non-removable;
	status = "okay";
};

&wdog3 {
	status = "okay";
};

&tempsense0 {
	status = "okay";
};

&iomuxc {
	pinctrl_eqos: eqosgrp {
		fsl,pins = <
			MX91_PAD_ENET1_MDC__ENET1_MDC			0x57e
			MX91_PAD_ENET1_MDIO__ENET_QOS_MDIO			0x57e
			MX91_PAD_ENET1_RD0__ENET_QOS_RGMII_RD0			0x57e
			MX91_PAD_ENET1_RD1__ENET_QOS_RGMII_RD1			0x57e
			MX91_PAD_ENET1_RD2__ENET_QOS_RGMII_RD2			0x57e
			MX91_PAD_ENET1_RD3__ENET_QOS_RGMII_RD3			0x57e
			MX91_PAD_ENET1_RXC__ENET_QOS_RGMII_RXC	0x5fe
			MX91_PAD_ENET1_RX_CTL__ENET_QOS_RGMII_RX_CTL		0x57e
			MX91_PAD_ENET1_TD0__ENET_QOS_RGMII_TD0			0x57e
			MX91_PAD_ENET1_TD1__ENET1_RGMII_TD1			0x57e
			MX91_PAD_ENET1_TD2__ENET_QOS_RGMII_TD2			0x57e
			MX91_PAD_ENET1_TD3__ENET_QOS_RGMII_TD3			0x57e
			MX91_PAD_ENET1_TXC__CCM_ENET_QOS_CLOCK_GENERATE_TX_CLK	0x5fe
			MX91_PAD_ENET1_TX_CTL__ENET_QOS_RGMII_TX_CTL		0x57e
		>;
	};

	pinctrl_eqos_sleep: eqosgrpsleep {
		fsl,pins = <
			MX91_PAD_ENET1_MDC__GPIO4_IO0				0x31e
			MX91_PAD_ENET1_MDIO__GPIO4_IO1				0x31e
			MX91_PAD_ENET1_RD0__GPIO4_IO10                          0x31e
			MX91_PAD_ENET1_RD1__GPIO4_IO11				0x31e
			MX91_PAD_ENET1_RD2__GPIO4_IO12				0x31e
			MX91_PAD_ENET1_RD3__GPIO4_IO13				0x31e
			MX91_PAD_ENET1_RXC__GPIO4_IO9                          0x31e
			MX91_PAD_ENET1_RX_CTL__GPIO4_IO8			0x31e
			MX91_PAD_ENET1_TD0__GPIO4_IO5                          0x31e
			MX91_PAD_ENET1_TD1__GPIO4_IO4                          0x31e
			MX91_PAD_ENET1_TD2__GPIO4_IO3				0x31e
			MX91_PAD_ENET1_TD3__GPIO4_IO2				0x31e
			MX91_PAD_ENET1_TXC__GPIO4_IO7                          0x31e
			MX91_PAD_ENET1_TX_CTL__GPIO4_IO6                       0x31e
		>;
	};

	pinctrl_fec: fecgrp {
		fsl,pins = <
			MX91_PAD_ENET2_MDC__ENET2_MDC			0x57e
			MX91_PAD_ENET2_MDIO__ENET2_MDIO			0x57e
			MX91_PAD_ENET2_RD0__ENET2_RGMII_RD0		0x57e
			MX91_PAD_ENET2_RD1__ENET2_RGMII_RD1		0x57e
			MX91_PAD_ENET2_RD2__ENET2_RGMII_RD2		0x57e
			MX91_PAD_ENET2_RD3__ENET2_RGMII_RD3		0x57e
			MX91_PAD_ENET2_RXC__ENET2_RGMII_RXC		0x5fe
			MX91_PAD_ENET2_RX_CTL__ENET2_RGMII_RX_CTL	0x57e
			MX91_PAD_ENET2_TD0__ENET2_RGMII_TD0		0x57e
			MX91_PAD_ENET2_TD1__ENET2_RGMII_TD1		0x57e
			MX91_PAD_ENET2_TD2__ENET2_RGMII_TD2		0x57e
			MX91_PAD_ENET2_TD3__ENET2_RGMII_TD3		0x57e
			MX91_PAD_ENET2_TXC__ENET2_RGMII_TXC		0x5fe
			MX91_PAD_ENET2_TX_CTL__ENET2_RGMII_TX_CTL	0x57e
		>;
	};

	pinctrl_fec_sleep: fecsleepgrp {
		fsl,pins = <
			MX91_PAD_ENET2_MDC__GPIO4_IO14			0x51e
			MX91_PAD_ENET2_MDIO__GPIO4_IO15			0x51e
			MX91_PAD_ENET2_RD0__GPIO4_IO24			0x51e
			MX91_PAD_ENET2_RD1__GPIO4_IO25			0x51e
			MX91_PAD_ENET2_RD2__GPIO4_IO26			0x51e
			MX91_PAD_ENET2_RD3__GPIO4_IO27			0x51e
			MX91_PAD_ENET2_RXC__GPIO4_IO23                  0x51e
			MX91_PAD_ENET2_RX_CTL__GPIO4_IO22		0x51e
			MX91_PAD_ENET2_TD0__GPIO4_IO19			0x51e
			MX91_PAD_ENET2_TD1__GPIO4_IO18			0x51e
			MX91_PAD_ENET2_TD2__GPIO4_IO17			0x51e
			MX91_PAD_ENET2_TD3__GPIO4_IO16			0x51e
			MX91_PAD_ENET2_TXC__GPIO4_IO21                  0x51e
			MX91_PAD_ENET2_TX_CTL__GPIO4_IO20               0x51e
		>;
	};

	pinctrl_lcdif: lcdifgrp {
		fsl,pins = <
			MX91_PAD_GPIO_IO00__MEDIAMIX_DISP_CLK		0x31e
			MX91_PAD_GPIO_IO01__MEDIAMIX_DISP_DE		0x31e
			MX91_PAD_GPIO_IO02__MEDIAMIX_DISP_VSYNC		0x31e
			MX91_PAD_GPIO_IO03__MEDIAMIX_DISP_HSYNC		0x31e
			MX91_PAD_GPIO_IO04__MEDIAMIX_DISP_DATA0		0x31e
			MX91_PAD_GPIO_IO05__MEDIAMIX_DISP_DATA1		0x31e
			MX91_PAD_GPIO_IO06__MEDIAMIX_DISP_DATA2		0x31e
			MX91_PAD_GPIO_IO07__MEDIAMIX_DISP_DATA3		0x31e
			MX91_PAD_GPIO_IO08__MEDIAMIX_DISP_DATA4		0x31e
			MX91_PAD_GPIO_IO09__MEDIAMIX_DISP_DATA5		0x31e
			MX91_PAD_GPIO_IO10__MEDIAMIX_DISP_DATA6		0x31e
			MX91_PAD_GPIO_IO11__MEDIAMIX_DISP_DATA7		0x31e
			MX91_PAD_GPIO_IO12__MEDIAMIX_DISP_DATA8		0x31e
			MX91_PAD_GPIO_IO13__MEDIAMIX_DISP_DATA9		0x31e
			MX91_PAD_GPIO_IO14__MEDIAMIX_DISP_DATA10	0x31e
			MX91_PAD_GPIO_IO15__MEDIAMIX_DISP_DATA11	0x31e
			MX91_PAD_GPIO_IO16__MEDIAMIX_DISP_DATA12	0x31e
			MX91_PAD_GPIO_IO17__MEDIAMIX_DISP_DATA13	0x31e
			MX91_PAD_GPIO_IO18__MEDIAMIX_DISP_DATA14	0x31e
			MX91_PAD_GPIO_IO19__MEDIAMIX_DISP_DATA15	0x31e
			MX91_PAD_GPIO_IO20__MEDIAMIX_DISP_DATA16	0x31e
			MX91_PAD_GPIO_IO21__MEDIAMIX_DISP_DATA17	0x31e
			MX91_PAD_GPIO_IO22__MEDIAMIX_DISP_DATA18	0x31e
			MX91_PAD_GPIO_IO23__MEDIAMIX_DISP_DATA19	0x31e
			MX91_PAD_GPIO_IO24__MEDIAMIX_DISP_DATA20	0x31e
			MX91_PAD_GPIO_IO25__MEDIAMIX_DISP_DATA21	0x31e
			MX91_PAD_GPIO_IO26__MEDIAMIX_DISP_DATA22	0x31e
			MX91_PAD_GPIO_IO27__MEDIAMIX_DISP_DATA23	0x31e
		>;
	};

	pinctrl_lcdif_gpio: lcdifgpiogrp {
		fsl,pins = <
			MX91_PAD_GPIO_IO00__GPIO2_IO0			0x51e
			MX91_PAD_GPIO_IO01__GPIO2_IO1			0x51e
			MX91_PAD_GPIO_IO02__GPIO2_IO2			0x51e
			MX91_PAD_GPIO_IO03__GPIO2_IO3			0x51e
		>;
	};

	pinctrl_lpi2c1: lpi2c1grp {
		fsl,pins = <
			MX91_PAD_I2C1_SCL__LPI2C1_SCL			0x40000b9e
			MX91_PAD_I2C1_SDA__LPI2C1_SDA			0x40000b9e
		>;
	};

	pinctrl_lpi2c2: lpi2c2grp {
		fsl,pins = <
			MX91_PAD_I2C2_SCL__LPI2C2_SCL			0x40000b9e
			MX91_PAD_I2C2_SDA__LPI2C2_SDA			0x40000b9e
		>;
	};

	pinctrl_lpi2c3: lpi2c3grp {
		fsl,pins = <
			MX91_PAD_GPIO_IO28__LPI2C3_SDA			0x40000b9e
			MX91_PAD_GPIO_IO29__LPI2C3_SCL			0x40000b9e
		>;
	};

	pinctrl_pcal6524: pcal6524grp {
		fsl,pins = <
			MX91_PAD_PDM_BIT_STREAM1__GPIO1_IO10			0x31e
		>;
	};

	pinctrl_uart1: uart1grp {
		fsl,pins = <
			MX91_PAD_UART1_RXD__LPUART1_RX			0x31e
			MX91_PAD_UART1_TXD__LPUART1_TX			0x31e
		>;
	};

	pinctrl_uart2: uart2grp {
		fsl,pins = <
			MX91_PAD_UART2_RXD__LPUART2_RX			0x31e
			MX91_PAD_UART2_TXD__LPUART2_TX			0x31e
		>;
	};

	pinctrl_uart5: uart5grp {
		fsl,pins = <
			MX91_PAD_DAP_TDO_TRACESWO__LPUART5_TX	0x31e
			MX91_PAD_DAP_TDI__LPUART5_RX		0x31e
			MX91_PAD_DAP_TMS_SWDIO__LPUART5_RTS_B	0x31e
			MX91_PAD_DAP_TCLK_SWCLK__LPUART5_CTS_B	0x31e
		>;
	};

	pinctrl_usdhc1: usdhc1grp {
		fsl,pins = <
			MX91_PAD_SD1_CLK__USDHC1_CLK		0x1582
			MX91_PAD_SD1_CMD__USDHC1_CMD		0x1382
			MX91_PAD_SD1_DATA0__USDHC1_DATA0	0x1382
			MX91_PAD_SD1_DATA1__USDHC1_DATA1	0x1382
			MX91_PAD_SD1_DATA2__USDHC1_DATA2	0x1382
			MX91_PAD_SD1_DATA3__USDHC1_DATA3	0x1382
			MX91_PAD_SD1_DATA4__USDHC1_DATA4	0x1382
			MX91_PAD_SD1_DATA5__USDHC1_DATA5	0x1382
			MX91_PAD_SD1_DATA6__USDHC1_DATA6	0x1382
			MX91_PAD_SD1_DATA7__USDHC1_DATA7	0x1382
			MX91_PAD_SD1_STROBE__USDHC1_STROBE	0x1582
		>;
	};

	pinctrl_usdhc1_100mhz: usdhc1-100mhzgrp {
		fsl,pins = <
			MX91_PAD_SD1_CLK__USDHC1_CLK		0x158e
			MX91_PAD_SD1_CMD__USDHC1_CMD		0x138e
			MX91_PAD_SD1_DATA0__USDHC1_DATA0	0x138e
			MX91_PAD_SD1_DATA1__USDHC1_DATA1	0x138e
			MX91_PAD_SD1_DATA2__USDHC1_DATA2	0x138e
			MX91_PAD_SD1_DATA3__USDHC1_DATA3	0x138e
			MX91_PAD_SD1_DATA4__USDHC1_DATA4	0x138e
			MX91_PAD_SD1_DATA5__USDHC1_DATA5	0x138e
			MX91_PAD_SD1_DATA6__USDHC1_DATA6	0x138e
			MX91_PAD_SD1_DATA7__USDHC1_DATA7	0x138e
			MX91_PAD_SD1_STROBE__USDHC1_STROBE	0x158e
		>;
	};

	pinctrl_usdhc1_200mhz: usdhc1-200mhzgrp {
		fsl,pins = <
			MX91_PAD_SD1_CLK__USDHC1_CLK		0x15fe
			MX91_PAD_SD1_CMD__USDHC1_CMD		0x13fe
			MX91_PAD_SD1_DATA0__USDHC1_DATA0	0x13fe
			MX91_PAD_SD1_DATA1__USDHC1_DATA1	0x13fe
			MX91_PAD_SD1_DATA2__USDHC1_DATA2	0x13fe
			MX91_PAD_SD1_DATA3__USDHC1_DATA3	0x13fe
			MX91_PAD_SD1_DATA4__USDHC1_DATA4	0x13fe
			MX91_PAD_SD1_DATA5__USDHC1_DATA5	0x13fe
			MX91_PAD_SD1_DATA6__USDHC1_DATA6	0x13fe
			MX91_PAD_SD1_DATA7__USDHC1_DATA7	0x13fe
			MX91_PAD_SD1_STROBE__USDHC1_STROBE	0x15fe
		>;
	};

	pinctrl_reg_usdhc2_vmmc: regusdhc2vmmcgrp {
		fsl,pins = <
			MX91_PAD_SD2_RESET_B__GPIO3_IO7	0x31e
		>;
	};

	pinctrl_usdhc2_gpio: usdhc2gpiogrp {
		fsl,pins = <
			MX91_PAD_SD2_CD_B__GPIO3_IO0		0x31e
		>;
	};

	pinctrl_usdhc2_gpio_sleep: usdhc2gpiogrpsleep {
		fsl,pins = <
			MX91_PAD_SD2_CD_B__GPIO3_IO0		0x51e
		>;
	};

	pinctrl_usdhc2: usdhc2grp {
		fsl,pins = <
			MX91_PAD_SD2_CLK__USDHC2_CLK		0x1582
			MX91_PAD_SD2_CMD__USDHC2_CMD		0x1382
			MX91_PAD_SD2_DATA0__USDHC2_DATA0	0x1382
			MX91_PAD_SD2_DATA1__USDHC2_DATA1	0x1382
			MX91_PAD_SD2_DATA2__USDHC2_DATA2	0x1382
			MX91_PAD_SD2_DATA3__USDHC2_DATA3	0x1382
			MX91_PAD_SD2_VSELECT__USDHC2_VSELECT	0x51e
		>;
	};

	pinctrl_usdhc2_100mhz: usdhc2-100mhzgrp {
		fsl,pins = <
			MX91_PAD_SD2_CLK__USDHC2_CLK		0x158e
			MX91_PAD_SD2_CMD__USDHC2_CMD		0x138e
			MX91_PAD_SD2_DATA0__USDHC2_DATA0	0x138e
			MX91_PAD_SD2_DATA1__USDHC2_DATA1	0x138e
			MX91_PAD_SD2_DATA2__USDHC2_DATA2	0x138e
			MX91_PAD_SD2_DATA3__USDHC2_DATA3	0x138e
			MX91_PAD_SD2_VSELECT__USDHC2_VSELECT	0x51e
		>;
	};

	pinctrl_usdhc2_200mhz: usdhc2-200mhzgrp {
		fsl,pins = <
			MX91_PAD_SD2_CLK__USDHC2_CLK		0x15fe
			MX91_PAD_SD2_CMD__USDHC2_CMD		0x13fe
			MX91_PAD_SD2_DATA0__USDHC2_DATA0	0x13fe
			MX91_PAD_SD2_DATA1__USDHC2_DATA1	0x13fe
			MX91_PAD_SD2_DATA2__USDHC2_DATA2	0x13fe
			MX91_PAD_SD2_DATA3__USDHC2_DATA3	0x13fe
			MX91_PAD_SD2_VSELECT__USDHC2_VSELECT	0x51e
		>;
	};

	pinctrl_usdhc2_sleep: usdhc2grpsleep {
		fsl,pins = <
			MX91_PAD_SD2_CLK__GPIO3_IO1            0x51e
			MX91_PAD_SD2_CMD__GPIO3_IO2		0x51e
			MX91_PAD_SD2_DATA0__GPIO3_IO3		0x51e
			MX91_PAD_SD2_DATA1__GPIO3_IO4		0x51e
			MX91_PAD_SD2_DATA2__GPIO3_IO5		0x51e
			MX91_PAD_SD2_DATA3__GPIO3_IO6		0x51e
			MX91_PAD_SD2_VSELECT__GPIO3_IO19	0x51e
		>;
	};

	pinctrl_usdhc3: usdhc3grp {
		fsl,pins = <
			MX91_PAD_SD3_CLK__USDHC3_CLK		0x1582
			MX91_PAD_SD3_CMD__USDHC3_CMD		0x1382
			MX91_PAD_SD3_DATA0__USDHC3_DATA0	0x1382
			MX91_PAD_SD3_DATA1__USDHC3_DATA1	0x1382
			MX91_PAD_SD3_DATA2__USDHC3_DATA2	0x1382
			MX91_PAD_SD3_DATA3__USDHC3_DATA3	0x1382
		>;
	};

	pinctrl_usdhc3_100mhz: usdhc3-100mhzgrp {
		fsl,pins = <
			MX91_PAD_SD3_CLK__USDHC3_CLK		0x158e
			MX91_PAD_SD3_CMD__USDHC3_CMD		0x138e
			MX91_PAD_SD3_DATA0__USDHC3_DATA0	0x138e
			MX91_PAD_SD3_DATA1__USDHC3_DATA1	0x138e
			MX91_PAD_SD3_DATA2__USDHC3_DATA2	0x138e
			MX91_PAD_SD3_DATA3__USDHC3_DATA3	0x138e
		>;
	};

	pinctrl_usdhc3_200mhz: usdhc3-200mhzgrp {
		fsl,pins = <
			MX91_PAD_SD3_CLK__USDHC3_CLK		0x15fe
			MX91_PAD_SD3_CMD__USDHC3_CMD		0x13fe
			MX91_PAD_SD3_DATA0__USDHC3_DATA0	0x13fe
			MX91_PAD_SD3_DATA1__USDHC3_DATA1	0x13fe
			MX91_PAD_SD3_DATA2__USDHC3_DATA2	0x13fe
			MX91_PAD_SD3_DATA3__USDHC3_DATA3	0x13fe
		>;
	};

	pinctrl_usdhc3_sleep: usdhc3grpsleep {
		fsl,pins = <
			MX91_PAD_SD3_CLK__GPIO3_IO20		0x31e
			MX91_PAD_SD3_CMD__GPIO3_IO21		0x31e
			MX91_PAD_SD3_DATA0__GPIO3_IO22		0x31e
			MX91_PAD_SD3_DATA1__GPIO3_IO23		0x31e
			MX91_PAD_SD3_DATA2__GPIO3_IO24		0x31e
			MX91_PAD_SD3_DATA3__GPIO3_IO25		0x31e
		>;
	};

	pinctrl_sai1: sai1grp {
		fsl,pins = <
			MX91_PAD_SAI1_TXC__SAI1_TX_BCLK			0x31e
			MX91_PAD_SAI1_TXFS__SAI1_TX_SYNC		0x31e
			MX91_PAD_SAI1_TXD0__SAI1_TX_DATA0		0x31e
			MX91_PAD_SAI1_RXD0__SAI1_RX_DATA0		0x31e
		>;
	};

	pinctrl_sai1_sleep: sai1grpsleep {
		fsl,pins = <
			MX91_PAD_SAI1_TXC__GPIO1_IO12                   0x51e
			MX91_PAD_SAI1_TXFS__GPIO1_IO11			0x51e
			MX91_PAD_SAI1_TXD0__GPIO1_IO13			0x51e
			MX91_PAD_SAI1_RXD0__GPIO1_IO14			0x51e
		>;
	};

	pinctrl_flexcan1: flexcan1grp {
		fsl,pins = <
			MX91_PAD_PDM_CLK__CAN1_TX			0x139e
			MX91_PAD_PDM_BIT_STREAM0__CAN1_RX		0x139e
		>;
	};

	pinctrl_tpm_pwm: tpmpwmgrp {
		fsl,pins = <
			MX91_PAD_I2C1_SCL__TPM2_CH0		0x31e
		>;
	};

	pinctrl_touch_irq: touchirqgrp {
		fsl,pins = <
			MX91_PAD_CCM_CLKO1__GPIO3_IO26		0x31e
		>;
	};
};
