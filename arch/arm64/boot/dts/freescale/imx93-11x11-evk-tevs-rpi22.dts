// SPDX-License-Identifier: (GPL-2.0+ OR MIT)
/*
 * Copyright 2024 TechNexion Ltd.
 *
 * Author: <PERSON><PERSON> <<EMAIL>>
 *
 */

#include "imx93-11x11-evk.dts"

/ {
	model = "NXP i.MX93 11x11 EVK board with TechNexion TEVS AR camera";

	/delete-node/ regulator-dvdd_sel;
	/delete-node/ regulator-dvdd;
	/delete-node/ regulator-vdd;
	/delete-node/ regulator-vddo;
	/delete-node/ regulator-vaa_sel;
	/delete-node/ regulator-avdd;
};

&lpi2c3 {
	clock-frequency = <350000>;

	/delete-node/ ap1302_mipi@3c;
	/delete-node/ mfd-isp@34;

	pca9554_a27_0: pca9554@27 {
		compatible = "nxp,pca9554";
		pinctrl-names = "default";
		reg = <0x27>;
		gpio-controller;
		#gpio-cells = <2>;
		status = "okay";
		gpio-line-names = "EXPOSURE_TRIG_IN1", "FLASH_OUT1", "CAM_SHUTTER1", "XVS1",
							"CSI1_PDB", "CSI1_INT", "INFO_TRIG_IN1", "CSI1_RST_N";
	};

	tevs: tevs@48 {
		compatible = "tn,tevs";
		reg = <0x48>;
		status = "okay";

		reset-gpios   = <&adp5585 0 GPIO_ACTIVE_HIGH>;
		standby-gpios = <&pca9554_a27_0 6 GPIO_ACTIVE_HIGH>;


		port {
			tevs_ep: endpoint {
				remote-endpoint = <&mipi_csi_ep>;
				data-lanes = <1 2>;
				clock-lanes = <0>;
				clock-noncontinuous;
				link-frequencies = /bits/ 64 <400000000>;
			};
		};
	};
};

&mipi_csi {
	#address-cells = <1>;
	#size-cells = <0>;
	status = "okay";

	/delete-node/ port@0;

	ports {
		port@0 {
			reg = <0>;
			mipi_csi_ep: endpoint {
				remote-endpoint = <&tevs_ep>;
				data-lanes = <1 2>;
				clock-lanes = <0>;
			};
		};
	};
};