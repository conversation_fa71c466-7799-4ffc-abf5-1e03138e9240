# SPDX-License-Identifier: GPL-2.0-only
menuconfig SND_SOC_SAMSUNG
	tristate "ASoC support for Samsung"
	depends on PLAT_SAMSUNG || ARCH_S5PV210 || ARCH_EXYNOS || COMPILE_TEST
	depends on COMM<PERSON>_<PERSON><PERSON><PERSON>
	select SND_SOC_GENERIC_DMAENGINE_PCM
	help
	  Say Y or M if you want to add support for codecs attached to
	  the Samsung SoCs' Audio interfaces. You will also need to
	  select the audio interfaces to support below.

if SND_SOC_SAMSUNG

config SND_SAMSUNG_PCM
	tristate "Samsung PCM interface support"

config SND_SAMSUNG_SPDIF
	tristate "Samsung SPDIF transmitter support"
	select SND_SOC_SPDIF

config SND_SAMSUNG_I2S
	tristate "Samsung I2S interface support"

config SND_SOC_SAMSUNG_SMDK_WM8994
	tristate "SoC I2S Audio support for WM8994 on SMDK"
	depends on I2C=y
	select MFD_WM8994
	select SND_SOC_WM8994
	select SND_SAMSUNG_I2S
	help
		Say Y if you want to add support for SoC audio on the SMDKs.

config SND_SOC_SAMSUNG_SMDK_SPDIF
	tristate "SoC S/PDIF Audio support for SMDK"
	select SND_SAMSUNG_SPDIF
	help
	  Say Y if you want to add support for SoC S/PDIF audio on the SMDK.

config SND_SOC_SMDK_WM8994_PCM
	tristate "SoC PCM Audio support for WM8994 on SMDK"
	depends on I2C=y
	select MFD_WM8994
	select SND_SOC_WM8994
	select SND_SAMSUNG_PCM
	help
	  Say Y if you want to add support for SoC audio on the SMDK

config SND_SOC_SPEYSIDE
	tristate "Audio support for Wolfson Speyside"
	depends on I2C && SPI_MASTER
	depends on MACH_WLF_CRAGG_6410 || COMPILE_TEST
	select SND_SAMSUNG_I2S
	select SND_SOC_WM8996
	select SND_SOC_WM9081
	select SND_SOC_WM0010
	select SND_SOC_WM1250_EV1

config SND_SOC_TOBERMORY
	tristate "Audio support for Wolfson Tobermory"
	depends on INPUT && I2C
	depends on MACH_WLF_CRAGG_6410 || COMPILE_TEST
	select SND_SAMSUNG_I2S
	select SND_SOC_WM8962

config SND_SOC_BELLS
	tristate "Audio support for Wolfson Bells"
	depends on MFD_ARIZONA && MFD_WM5102 && MFD_WM5110 && I2C && SPI_MASTER
	depends on MACH_WLF_CRAGG_6410 || COMPILE_TEST
	select SND_SAMSUNG_I2S
	select SND_SOC_WM5102
	select SND_SOC_WM5110
	select SND_SOC_WM9081
	select SND_SOC_WM0010
	select SND_SOC_WM1250_EV1

config SND_SOC_LOWLAND
	tristate "Audio support for Wolfson Lowland"
	depends on I2C
	depends on MACH_WLF_CRAGG_6410 || COMPILE_TEST
	select SND_SAMSUNG_I2S
	select SND_SOC_WM5100
	select SND_SOC_WM9081

config SND_SOC_LITTLEMILL
	tristate "Audio support for Wolfson Littlemill"
	depends on I2C
	depends on MACH_WLF_CRAGG_6410 || COMPILE_TEST
	select SND_SAMSUNG_I2S
	select MFD_WM8994
	select SND_SOC_WM8994

config SND_SOC_SNOW
	tristate "Audio support for Google Snow boards"
	depends on I2C
	select SND_SOC_MAX98090
	select SND_SOC_MAX98095
	select SND_SAMSUNG_I2S
	help
	  Say Y if you want to add audio support for various Snow
	  boards based on Exynos5 series of SoCs.

config SND_SOC_ODROID
	tristate "Audio support for Odroid XU3/XU4"
	depends on SND_SOC_SAMSUNG && I2C
	select SND_SOC_MAX98090
	select SND_SAMSUNG_I2S
	help
	  Say Y here to enable audio support for the Odroid XU3/XU4.

config SND_SOC_ARNDALE
	tristate "Audio support for Arndale Board"
	depends on I2C
	select SND_SAMSUNG_I2S
	select SND_SOC_RT5631
	select MFD_WM8994
	select SND_SOC_WM8994

config SND_SOC_SAMSUNG_TM2_WM5110
	tristate "SoC I2S Audio support for WM5110 on TM2 board"
	depends on SND_SOC_SAMSUNG && MFD_ARIZONA && MFD_WM5110 && I2C && SPI_MASTER
	depends on GPIOLIB || COMPILE_TEST
	select SND_SOC_MAX98504
	select SND_SOC_WM5110
	select SND_SAMSUNG_I2S
	help
	  Say Y if you want to add support for SoC audio on the TM2 board.

config SND_SOC_SAMSUNG_ARIES_WM8994
	tristate "SoC I2S Audio support for WM8994 on Aries"
	depends on SND_SOC_SAMSUNG && I2C && IIO && EXTCON
	select SND_SOC_BT_SCO
	select MFD_WM8994
	select SND_SOC_WM8994
	select SND_SAMSUNG_I2S
	help
	  Say Y if you want to add support for SoC audio on Aries boards,
	  which has a WM8994 codec connected to a BT codec, a cellular
	  modem, and the Samsung I2S controller.  Jack detection is done
	  via ADC, GPIOs, and an extcon device.  Switching between the Mic
	  and TV-Out path is also handled.

config SND_SOC_SAMSUNG_MIDAS_WM1811
	tristate "SoC I2S Audio support for Midas boards"
	depends on SND_SOC_SAMSUNG && I2C && IIO
	select SND_SAMSUNG_I2S
	select MFD_WM8994
	select SND_SOC_WM8994
	help
	  Say Y if you want to add support for SoC audio on the Midas boards.

endif #SND_SOC_SAMSUNG
